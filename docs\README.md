# Quan Ly TTB API

API backend cho hệ thống quản lý thiết bị (bộ đàm và thẻ từ) sử dụng Hono.js và Cloudflare D1.

## Cấu trúc thư mục

```
src/api/
├── index.ts              # Entry point chính
├── routes/               # API routes
│   ├── walkie-talkies.ts # Quản lý bộ đàm
│   ├── access-cards.ts   # Quản lý thẻ từ
│   └── history.ts        # Lịch sử hoạt động
├── middleware/           # Middleware
│   └── auth.ts          # Authentication & logging
├── schema.sql           # Database schema
└── README.md           # Tài liệu này
```

## API Endpoints

### Health Check
- `GET /` - Kiểm tra trạng thái API

### Bộ đàm (Walkie Talkies)
- `GET /api/walkie-talkies` - Lấy danh sách bộ đàm
- `GET /api/walkie-talkies/:id` - Lấy chi tiết bộ đàm
- `POST /api/walkie-talkies` - Tạo bộ đàm mới
- `PUT /api/walkie-talkies/:id` - Cập nhật bộ đàm
- `DELETE /api/walkie-talkies/:id` - Xóa bộ đàm

### Thẻ từ (Access Cards)
- `GET /api/access-cards` - Lấy danh sách thẻ từ
- `GET /api/access-cards/:id` - Lấy chi tiết thẻ từ
- `POST /api/access-cards` - Tạo thẻ từ mới
- `PUT /api/access-cards/:id` - Cập nhật thẻ từ
- `DELETE /api/access-cards/:id` - Xóa thẻ từ
- `GET /api/access-cards/by-user/:userId` - Lấy thẻ từ theo người dùng

### Lịch sử (History)
- `GET /api/history` - Lấy lịch sử hoạt động (có phân trang)
- `GET /api/history/:id` - Lấy chi tiết bản ghi lịch sử
- `POST /api/history` - Tạo bản ghi lịch sử
- `GET /api/history/item/:type/:id` - Lấy lịch sử của thiết bị
- `GET /api/history/user/:userId` - Lấy lịch sử của người dùng

## Scripts

```bash
# Development
bun run api:dev          # Chạy API local với Wrangler
bun run api:deploy       # Deploy API lên Cloudflare Workers

# Database
bun run db:init          # Khởi tạo database schema (remote)
bun run db:init-local    # Khởi tạo database schema (local)
bun run db:studio        # Kiểm tra tables trong database

# Utilities
bun run cf:types         # Generate Cloudflare types
```

## Environment Variables

Tạo file `.dev.vars` cho local development:

```env
NODE_ENV=development
```

## Database Schema

Database sử dụng 3 bảng chính:

1. **walkie_talkies** - Quản lý bộ đàm
2. **access_cards** - Quản lý thẻ từ
3. **activity_history** - Lịch sử hoạt động

Chi tiết schema xem trong file `schema.sql`.

## Response Format

Tất cả API responses đều có format:

```json
{
  "success": true|false,
  "data": {...},
  "message": "...",
  "error": "..."
}
```

## CORS

API đã được cấu hình CORS cho:
- `http://localhost:5173` (Vite dev server)
- `http://localhost:3000` (Alternative dev server)

## Authentication

Hiện tại authentication đang được disable cho development. Sẽ implement JWT authentication sau.

## Deployment

1. Đảm bảo đã login Cloudflare: `bunx wrangler auth login`
2. Deploy: `bun run api:deploy`
3. API sẽ có URL: `https://quan-ly-ttb-api.your-subdomain.workers.dev`
