# 🔄 Auto Backup System cho Flight Schedules

## 📋 Tổng quan

Hệ thống backup tự động cho flight schedules với các tính năng:
- ⏰ Backup tự động mỗi 20 phút
- 📊 Export Excel với format đầy đủ
- ☁️ Lưu trữ trên Cloudflare R2
- 🎛️ Admin UI để quản lý backup
- 🔧 Separate Cloudflare Worker deployment

## 🏗️ Kiến trúc hệ thống

### 1. Backup Worker (Separate Project)
```
backup-worker/
├── src/
│   ├── index.ts              # Main backup worker với cron trigger
│   ├── lib/
│   │   ├── excel-generator.ts # Logic export Excel
│   │   ├── database.ts        # Queries D1 database
│   │   ├── storage.ts         # R2 storage operations
│   │   └── utils.ts           # Utility functions
│   └── types.ts              # TypeScript definitions
├── wrangler.toml             # Worker configuration
├── package.json              # Dependencies (ExcelJS, etc.)
└── README.md                 # Documentation
```

### 2. Main Project Integration
```
src/
├── api/routes/
│   └── backup.ts             # Backup management API endpoints
├── components/admin/
│   └── BackupManagement.tsx  # Admin UI component
├── lib/
│   └── backup-api.ts         # API client cho backup operations
└── pages/admin/
    └── backup.tsx            # Backup management page
```

## 📊 Database Schema

### Bảng backup_logs
```sql
CREATE TABLE backup_logs (
  id TEXT PRIMARY KEY,
  filename TEXT NOT NULL,
  file_size INTEGER,
  backup_date TEXT NOT NULL,        -- Date của flight schedule được backup
  created_at TEXT NOT NULL,         -- Thời gian tạo backup
  status TEXT NOT NULL,             -- 'success', 'failed', 'in_progress'
  error_message TEXT,               -- Error details nếu failed
  processing_time_ms INTEGER,       -- Thời gian xử lý
  total_flights INTEGER,            -- Số lượng flights được backup
  r2_path TEXT NOT NULL,            -- Path trong R2 bucket
  triggered_by TEXT NOT NULL        -- 'cron', 'manual', 'api'
);
```

## 🔧 Core Features

### 1. Automated Backup (Cron Trigger)
- **Schedule**: Mỗi 20 phút (`*/20 * * * *`)
- **Timezone**: UTC (Cloudflare Workers standard)
- **Logic**: Backup flight schedules cho ngày hiện tại và ngày mai
- **Retry**: 3 lần retry nếu backup failed

### 2. Excel Export Format
```
Columns: STT | FLT (ARR) | FROM | REG (ARR) | STA | STAFF (ARR) | 
         FLT (DEP) | TO | REG (DEP) | STD | STAFF (DEP) | REMARK
```

**Special Formatting:**
- ✈️ Overnight flights: `02:30+` format
- ⭐ First flight staff: `Minh*` với blue text
- 👥 Combined staff: `TÚ/NAM` format
- 📝 Modified cells: Red background highlighting

### 3. R2 Storage Structure
```
flight-schedule-backups/
├── 2024/
│   ├── 01/
│   │   ├── 15/
│   │   │   ├── flight-schedule-2024-01-15-08-00.xlsx
│   │   │   ├── flight-schedule-2024-01-15-08-20.xlsx
│   │   │   └── flight-schedule-2024-01-15-08-40.xlsx
│   │   └── 16/
│   └── 02/
└── metadata/
    └── backup-index.json    # Index file cho fast listing
```

### 4. Admin UI Features
- 📋 **Backup List**: View all backups với timestamps
- ⬇️ **Download**: Direct download backup files
- 🗑️ **Delete**: Remove old backup files
- ▶️ **Manual Trigger**: Immediate backup button
- 📊 **Status Monitor**: Real-time backup status
- 📈 **Statistics**: Backup success rate, file sizes, etc.

## 🚀 API Endpoints

### Backup Management API
```typescript
// Main project API endpoints
GET    /api/backup/list              # List all backups
GET    /api/backup/:id/download      # Download backup file
DELETE /api/backup/:id               # Delete backup
POST   /api/backup/trigger           # Manual backup trigger
GET    /api/backup/status            # Current backup status
GET    /api/backup/stats             # Backup statistics

// Backup worker endpoints (internal)
POST   /backup/execute               # Execute backup
GET    /backup/health                # Health check
```

## 🔐 Security & Authentication

### 1. Main Project
- **Admin Role Required**: Chỉ admin mới access backup management
- **JWT Authentication**: Sử dụng existing auth system
- **API Protection**: All endpoints require valid JWT

### 2. Backup Worker
- **Service Authentication**: API key cho communication
- **R2 Access**: Dedicated R2 credentials
- **D1 Access**: Same database binding như main project

### 3. R2 Bucket Security
- **Private Bucket**: Không public access
- **Signed URLs**: Temporary download links
- **Access Control**: Chỉ backup worker và main project

## ⚡ Performance & Optimization

### 1. Backup Worker
- **Chunked Processing**: Process large datasets in chunks
- **Memory Optimization**: Stream Excel generation
- **Concurrent Uploads**: Parallel R2 uploads
- **Error Recovery**: Robust retry mechanisms

### 2. Storage Optimization
- **Compression**: Excel files với compression
- **Deduplication**: Skip duplicate backups
- **Cleanup**: Auto-delete old backups (configurable retention)
- **Indexing**: Fast backup listing với metadata

### 3. Network Optimization
- **CDN**: R2 với Cloudflare CDN
- **Streaming**: Stream downloads cho large files
- **Caching**: Cache backup metadata

## 📊 Monitoring & Logging

### 1. Backup Metrics
- **Success Rate**: Percentage of successful backups
- **Processing Time**: Average backup duration
- **File Sizes**: Backup file size trends
- **Error Rates**: Failed backup frequency

### 2. Alerting
- **Failed Backups**: Immediate notification
- **Storage Limits**: R2 usage warnings
- **Performance Issues**: Slow backup alerts
- **System Health**: Worker availability monitoring

### 3. Logging
- **Structured Logs**: JSON format với timestamps
- **Error Details**: Full error stack traces
- **Audit Trail**: All backup operations logged
- **Performance Logs**: Timing và resource usage

## 🔄 Deployment Process

### 1. Backup Worker Deployment
```bash
# Setup backup worker project
cd backup-worker
bun install
bunx wrangler deploy

# Configure cron trigger
bunx wrangler triggers deploy
```

### 2. R2 Bucket Setup
```bash
# Create R2 bucket
bunx wrangler r2 bucket create flight-schedule-backups

# Configure bucket policies
bunx wrangler r2 bucket cors put flight-schedule-backups --file cors.json
```

### 3. Main Project Updates
```bash
# Apply database migration
bunx wrangler d1 execute quan-ly-ttb-db --file migrations/0008_create_backup_logs.sql

# Deploy updated main project
bun run api:deploy
```

## 📋 Tasks Checklist

### Phase 1: Foundation ⏳
- [ ] Create backup worker project structure
- [ ] Setup ExcelJS dependencies
- [ ] Configure D1 database binding
- [ ] Implement basic Excel export
- [ ] Setup R2 bucket và credentials

### Phase 2: Core Logic ⏳
- [ ] Implement flight data queries
- [ ] Excel generation với proper formatting
- [ ] R2 upload functionality
- [ ] Cron trigger configuration
- [ ] Error handling và retry logic

### Phase 3: Integration ⏳
- [ ] Database migration cho backup_logs
- [ ] Backup management API endpoints
- [ ] Admin UI components
- [ ] Authentication integration
- [ ] API communication setup

### Phase 4: Advanced Features ⏳
- [ ] Manual backup trigger
- [ ] Backup file download
- [ ] Delete old backups
- [ ] Backup statistics
- [ ] Performance monitoring

### Phase 5: Testing & Deployment ⏳
- [ ] Unit tests cho backup logic
- [ ] Integration tests
- [ ] Performance testing
- [ ] Security testing
- [ ] Production deployment

## 🎯 Success Criteria

✅ **Automated Backups**: Chạy đúng schedule mỗi 20 phút
✅ **Excel Format**: Đầy đủ columns và special formatting
✅ **R2 Storage**: Organized structure và reliable uploads
✅ **Admin UI**: User-friendly backup management
✅ **Performance**: < 30 seconds cho backup execution
✅ **Reliability**: > 99% backup success rate
✅ **Security**: Proper authentication và access control

## 📞 Support & Maintenance

### 1. Monitoring Dashboard
- Real-time backup status
- Historical success rates
- Storage usage tracking
- Performance metrics

### 2. Troubleshooting
- Common error scenarios
- Debug procedures
- Recovery processes
- Contact information

### 3. Updates & Maintenance
- Regular dependency updates
- Performance optimizations
- Feature enhancements
- Security patches
