# Flight Schedule Excel-like Features Implementation Plan

## 📋 OVERVIEW
Implement Excel-like functionality for flight schedule table including:
- Insert/Delete rows for arrival/departure parts separately
- Cell coloring with database persistence
- Right-click context menu for row and cell operations
- STT reordering with proper sequence management

## 🗄️ DATABASE CHANGES

### 1. Create flight_cell_colors table
```sql
-- Migration: 0006_create_flight_cell_colors_table.sql
CREATE TABLE IF NOT EXISTS flight_cell_colors (
  id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
  flight_id TEXT NOT NULL,
  field_name TEXT NOT NULL, -- e.g., 'arr_reg', 'dep_staff', 'remark'
  color_value TEXT NOT NULL, -- hex color code e.g., '#ff0000'
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT NOT NULL,
  
  FOREIGN KEY (flight_id) REFERENCES flights(id) ON DELETE CASCADE,
  UNIQUE(flight_id, field_name) -- One color per field per flight
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_flight_cell_colors_flight_id ON flight_cell_colors(flight_id);
CREATE INDEX IF NOT EXISTS idx_flight_cell_colors_field ON flight_cell_colors(field_name);
```

### 2. Add virtual row support to flights table
- No schema changes needed
- Use existing STT field with decimal logic for virtual rows
- Arrival: STT * 2 - 1 (e.g., STT 1 → Virtual STT 1)
- Departure: STT * 2 (e.g., STT 1 → Virtual STT 2)

## 🎨 FRONTEND COMPONENTS

### 1. Context Menu System

#### File: `src/components/flight-schedule/FlightContextMenu.tsx`
```typescript
interface FlightContextMenuProps {
  isOpen: boolean
  position: { x: number; y: number }
  onClose: () => void
  contextType: 'cell' | 'row'
  cellInfo?: {
    flightId: string
    fieldName: string
    currentColor?: string
  }
  rowInfo?: {
    flightId: string
    rowType: 'arrival' | 'departure'
    stt: number
  }
  onColorChange?: (color: string) => void
  onInsertRow?: (type: 'arrival' | 'departure', position: 'before' | 'after') => void
  onDeleteRow?: (type: 'arrival' | 'departure') => void
}
```

#### File: `src/components/flight-schedule/ColorPicker.tsx`
```typescript
interface ColorPickerProps {
  currentColor?: string
  onColorSelect: (color: string) => void
  onClose: () => void
}

// Predefined color palette
const PRESET_COLORS = [
  '#ffffff', '#f3f4f6', '#e5e7eb', '#d1d5db', // Grays
  '#fef2f2', '#fee2e2', '#fca5a5', '#ef4444', // Reds
  '#f0fdf4', '#dcfce7', '#86efac', '#22c55e', // Greens
  '#eff6ff', '#dbeafe', '#93c5fd', '#3b82f6', // Blues
  '#fefce8', '#fef3c7', '#fde047', '#eab308', // Yellows
  '#fdf4ff', '#f3e8ff', '#c084fc', '#a855f7', // Purples
]
```

### 2. Enhanced FlightTable Component

#### File: `src/components/flight-schedule/FlightTable.tsx` (modifications)
```typescript
// Add context menu state and handlers
const [contextMenu, setContextMenu] = useState<{
  isOpen: boolean
  position: { x: number; y: number }
  type: 'cell' | 'row'
  cellInfo?: { flightId: string; fieldName: string; currentColor?: string }
  rowInfo?: { flightId: string; rowType: 'arrival' | 'departure'; stt: number }
}>({
  isOpen: false,
  position: { x: 0, y: 0 },
  type: 'cell'
})

// Right-click handlers
const handleCellRightClick = (e: React.MouseEvent, flightId: string, fieldName: string) => {
  e.preventDefault()
  // Get current cell color from cellColors state
  // Set context menu for cell operations
}

const handleRowRightClick = (e: React.MouseEvent, flightId: string, rowType: 'arrival' | 'departure', stt: number) => {
  e.preventDefault()
  // Set context menu for row operations
}
```

### 3. Cell Color Management

#### File: `src/hooks/useCellColors.ts`
```typescript
export const useCellColors = (flightIds: string[]) => {
  // Fetch cell colors for given flight IDs
  // Return colors map: { flightId: { fieldName: colorValue } }
  // Provide mutation functions for updating colors
}

export const useCellColorMutations = () => {
  // updateCellColor mutation
  // deleteCellColor mutation
  // batchUpdateCellColors mutation
}
```

### 4. Row Operations

#### File: `src/hooks/useRowOperations.ts`
```typescript
export const useRowOperations = () => {
  const insertRow = useMutation({
    mutationFn: async ({
      flightId,
      type, // 'arrival' | 'departure'
      position, // 'before' | 'after'
      date
    }: InsertRowParams) => {
      // API call to insert row
      // Handle STT reordering
    }
  })

  const deleteRow = useMutation({
    mutationFn: async ({
      flightId,
      type, // 'arrival' | 'departure'
      date
    }: DeleteRowParams) => {
      // API call to delete row part
      // Handle STT reordering
    }
  })

  return { insertRow, deleteRow }
}
```

## 🔧 BACKEND API ENDPOINTS

### 1. Cell Colors API

#### File: `src/api/routes/flight-cell-colors.ts`
```typescript
// GET /api/flights/:flightId/colors - Get cell colors for a flight
// GET /api/flights/colors?flightIds=id1,id2,id3 - Batch get colors
// PUT /api/flights/:flightId/colors/:fieldName - Update cell color
// DELETE /api/flights/:flightId/colors/:fieldName - Remove cell color
// POST /api/flights/colors/batch - Batch update colors
```

### 2. Row Operations API

#### File: `src/api/routes/flights.ts` (additions)
```typescript
// POST /api/flights/:flightId/insert-row
// Body: { type: 'arrival' | 'departure', position: 'before' | 'after', date: string }

// DELETE /api/flights/:flightId/delete-row
// Body: { type: 'arrival' | 'departure', date: string }
```

### 3. STT Reordering Logic
```typescript
// Function to reorder STT after insert/delete operations
const reorderSTT = async (db: D1Database, date: string, startSTT: number, increment: number) => {
  // Update all flights with STT >= startSTT
  // Increment/decrement their STT values
  // Maintain sequence integrity
}
```

## 🎯 IMPLEMENTATION PHASES

### Phase 1: Database Setup
1. Create migration file for flight_cell_colors table
2. Apply migration using Cloudflare MCP tools
3. Test database schema

### Phase 2: Cell Colors System
1. Create ColorPicker component
2. Create useCellColors hook
3. Create cell colors API endpoints
4. Integrate cell colors into FlightTable
5. Test cell coloring functionality

### Phase 3: Context Menu System
1. Create FlightContextMenu component
2. Add right-click event handlers to FlightTable
3. Implement context menu positioning logic
4. Test context menu on different screen sizes

### Phase 4: Row Operations
1. Create useRowOperations hook
2. Create row operations API endpoints
3. Implement STT reordering logic
4. Add insert/delete functionality to context menu
5. Test row operations with data integrity

### Phase 5: Integration & Testing
1. Integrate all features into FlightTable
2. Test Excel-like behavior
3. Test mobile responsiveness
4. Performance optimization
5. Error handling and edge cases

## 📱 MOBILE CONSIDERATIONS

### FlightCard Component Updates
- Add long-press gesture for context menu on mobile
- Adapt context menu for touch interfaces
- Ensure color picker works on mobile devices
- Test row operations on mobile layout

## 🔍 TESTING STRATEGY

### Unit Tests
- Cell color CRUD operations
- STT reordering logic
- Context menu positioning
- Row insert/delete operations

### Integration Tests
- Full workflow: right-click → color change → database persistence
- Row operations with multiple flights
- Concurrent user operations
- Mobile gesture handling

### Performance Tests
- Large dataset handling (500+ flights)
- Color loading performance
- STT reordering with many rows
- Real-time updates efficiency

## 🚨 ERROR HANDLING

### Client-side
- Context menu positioning edge cases
- Color picker validation
- Network error handling for mutations
- Optimistic update rollbacks

### Server-side
- Database constraint violations
- STT sequence conflicts
- Invalid color values
- Concurrent modification handling

## 🔒 SECURITY CONSIDERATIONS

### Input Validation
- Color value format validation (hex codes)
- STT range validation
- Flight ID existence validation
- User permission checks

### Data Integrity
- Foreign key constraints
- Transaction handling for STT reordering
- Atomic operations for row insert/delete
- Color data cleanup on flight deletion

---

## 📝 IMPLEMENTATION CHECKLIST

### Database Setup
1. Create migration file `0006_create_flight_cell_colors_table.sql`
2. Apply migration using Cloudflare MCP tools
3. Verify table creation and indexes

### Cell Colors Backend
4. Create `src/api/routes/flight-cell-colors.ts` with CRUD endpoints
5. Add cell colors validation schemas in `src/lib/flight-schemas.ts`
6. Create cell colors service functions
7. Add cell colors to flight API responses

### Cell Colors Frontend
8. Create `src/components/flight-schedule/ColorPicker.tsx`
9. Create `src/hooks/useCellColors.ts` for data fetching
10. Create `src/hooks/useCellColorMutations.ts` for mutations
11. Add cell color styling to EditableCell component
12. Test cell color persistence and display

### Context Menu System
13. Create `src/components/flight-schedule/FlightContextMenu.tsx`
14. Add right-click event handlers to FlightTable cells
15. Add right-click event handlers to FlightTable rows
16. Implement context menu positioning logic
17. Add click-outside-to-close functionality
18. Test context menu on different screen positions

### Row Operations Backend
19. Add insert-row endpoint to `src/api/routes/flights.ts`
20. Add delete-row endpoint to `src/api/routes/flights.ts`
21. Implement STT reordering logic function
22. Add row operations validation
23. Test STT sequence integrity

### Row Operations Frontend
24. Create `src/hooks/useRowOperations.ts`
25. Add insert row functionality to context menu
26. Add delete row functionality to context menu
27. Implement optimistic updates for row operations
28. Add loading states for row operations
29. Test row insert/delete with STT reordering

### FlightTable Integration
30. Integrate context menu into FlightTable component
31. Add cell color loading and display
32. Update FlightTable props for new functionality
33. Add keyboard shortcuts (optional: Ctrl+Z for undo)
34. Test complete Excel-like workflow

### Mobile Support
35. Update FlightCard component for cell colors
36. Add long-press gesture for mobile context menu
37. Adapt context menu for touch interfaces
38. Test mobile row operations
39. Ensure responsive design for color picker

### Testing & Optimization
40. Write unit tests for cell color operations
41. Write unit tests for row operations
42. Write integration tests for complete workflow
43. Performance testing with large datasets
44. Error handling and edge case testing
45. Cross-browser compatibility testing

### Documentation & Cleanup
46. Update API documentation
47. Add component documentation
48. Update user guide for new features
49. Code review and refactoring
50. Final testing and deployment preparation
