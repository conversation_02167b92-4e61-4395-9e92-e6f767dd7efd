# Kế hoạch phát triển module Q<PERSON>ản lý lịch bay

## 🎯 <PERSON><PERSON><PERSON> tiêu
Tích hợp module quản lý lịch bay vào hệ thống quản lý thiết bị hiện tại, ph<PERSON><PERSON> vụ công ty dịch vụ mặt đất sân bay.

## 📋 Tính năng chính

### 1. Import Excel
- Upload file Excel lịch bay
- Parse và validate dữ liệu
- Preview trước khi import
- Báo lỗi nếu format không đúng

### 2. Inline Edit
- Chỉnh sửa trực tiếp từng ô trong bảng
- Auto-save khi blur khỏi ô
- Validation real-time
- Highlight ô đang edit

### 3. Smart Search
- Tìm kiếm theo số hiệu bay
- Tìm kiếm theo tên nhân viên
- Filter theo ngày
- Filter theo loại chuyến bay (ARR/DEP)

### 4. Dashboard riêng biệt
- Tổng quan lịch bay hôm nay

### 5. Public View cho nhân viên
- <PERSON><PERSON> lịch bay không cần đăng nhập
- Chỉ đọc, không edit
- Responsive cho mobile
- Auto refresh

### 6. Export functionality
- Export Excel theo ngày/tuần/tháng
- Export PDF cho in ấn


### 7. Lịch sử thay đổi
- Track mọi thay đổi của từng ô
- Hiển thị: thời gian, người thay đổi, nội dung cũ/mới
- Audit trail đầy đủ

## 🗄️ Database Schema

### Bảng `flights`
```sql
CREATE TABLE flights (
  id TEXT PRIMARY KEY,
  date TEXT NOT NULL, -- YYYY-MM-DD
  stt INTEGER NOT NULL,
  
  -- Arrival
  arr_flt TEXT,
  arr_from TEXT,
  arr_reg TEXT,
  arr_time TEXT,
  arr_staff TEXT,
  
  -- Departure  
  dep_flt TEXT,
  dep_to TEXT,
  dep_reg TEXT,
  dep_time TEXT,
  dep_staff TEXT,
  
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

### Bảng `flight_changes`
```sql
CREATE TABLE flight_changes (
  id TEXT PRIMARY KEY,
  flight_id TEXT NOT NULL,
  field_name TEXT NOT NULL, -- arr_flt, dep_staff, etc.
  old_value TEXT,
  new_value TEXT,
  changed_by TEXT NOT NULL,
  changed_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (flight_id) REFERENCES flights(id)
);
```

### Bảng `flight_imports`
```sql
CREATE TABLE flight_imports (
  id TEXT PRIMARY KEY,
  filename TEXT NOT NULL,
  imported_by TEXT NOT NULL,
  imported_at TEXT DEFAULT CURRENT_TIMESTAMP,
  total_rows INTEGER,
  success_rows INTEGER,
  error_rows INTEGER,
  errors TEXT -- JSON array of errors
);
```

## 🏗️ Kiến trúc Frontend

### Cấu trúc thư mục
```
src/
├── pages/
│   ├── flight-schedule/
│   │   ├── index.tsx          # Dashboard lịch bay
│   │   ├── manage.tsx         # Quản lý lịch bay (admin)
│   │   └── public.tsx         # Xem public (nhân viên)
├── components/
│   ├── flight-schedule/
│   │   ├── FlightTable.tsx    # Bảng lịch bay với inline edit
│   │   ├── ImportExcel.tsx    # Component import Excel
│   │   ├── SearchFilter.tsx   # Tìm kiếm và filter
│   │   ├── ChangeHistory.tsx  # Lịch sử thay đổi
│   │   └── ExportButton.tsx   # Export functionality
├── hooks/
│   ├── useFlights.tsx         # TanStack Query hooks
│   ├── useFlightImport.tsx    # Import logic
│   └── useFlightChanges.tsx   # Change tracking
├── lib/
│   ├── excel-parser.ts        # Parse Excel files
│   ├── flight-validator.ts    # Validate flight data
│   └── export-utils.ts        # Export utilities
```

## 🔧 Tech Stack

### Frontend
- **React 19** - UI framework
- **TanStack Table** - Inline editing table
- **TanStack Query** - Data fetching
- **TanStack Router** - Routing
- **Zod v4** - Validation
- **Tailwind v4** - Styling
- **SheetJS (xlsx)** - Excel parsing
- **jsPDF** - PDF export

### Backend
- **Hono** - API framework
- **Cloudflare D1** - Database
- **Cloudflare Workers** - Runtime

## 📱 UI/UX Design

### Layout chính
- Header với navigation tabs: Thiết bị | Lịch bay
- Sidebar với quick filters
- Main content area với table
- Footer với pagination

### Responsive Design
- Desktop: Full table view
- Tablet: Horizontal scroll
- Mobile: Card view cho từng chuyến bay

### Color Scheme
- Sử dụng theme hiện tại
- Highlight màu xanh cho ARR
- Highlight màu cam cho DEP
- Màu đỏ cho conflicts/errors

## 🚀 Roadmap

### Phase 1: Foundation (1 tuần)
- Database schema setup
- Basic API endpoints
- Basic UI layout

### Phase 2: Core Features (2 tuần)
- Import Excel functionality
- Basic table display
- Inline editing

### Phase 3: Advanced Features (1 tuần)
- Search & filter
- Change history
- Export functionality

### Phase 4: Polish (1 tuần)
- Public view
- Dashboard
- Mobile optimization
- Testing

**Tổng thời gian: 5 tuần**

## 🧪 Testing Strategy

### Unit Tests
- Excel parser functions
- Validation logic
- API endpoints

### Integration Tests
- Import workflow
- Edit workflow
- Export workflow

### E2E Tests
- Complete user journeys
- Mobile responsiveness
- Cross-browser compatibility

## 🔒 Security & Performance

### Security
- Validate uploaded Excel files
- Sanitize input data
- Rate limiting cho API
- Audit trail cho mọi thay đổi

### Performance
- Pagination cho large datasets
- Debounced search
- Optimistic updates
- Caching với TanStack Query

## 📊 Metrics & Analytics

### KPIs
- Số lượng import thành công/thất bại
- Số lượng edit operations
- Response time của search
- User engagement với public view

### Monitoring
- Error tracking
- Performance monitoring
- Usage analytics
