# Tasks - Modu<PERSON> lý lịch bay

## 📋 Phase 1: Foundation (1 tuần)

### Database Setup
- [x] **Task 1.1**: Tạo migration cho bảng `flights`
  - Thiết kế schema với đầy đủ fields ARR/DEP
  - Add indexes cho performance
  - **Estimate**: 2 giờ

- [x] **Task 1.2**: Tạo migration cho bảng `flight_changes`
  - Schema tracking thay đổi
  - Foreign key constraints
  - **Estimate**: 1 giờ

- [x] **Task 1.3**: Tạo migration cho bảng `flight_imports`
  - Schema tracking import history
  - **Estimate**: 1 giờ

### API Foundation
- [ ] **Task 1.4**: Setup API routes cơ bản
  - GET /api/flights - List flights
  - POST /api/flights - Create flight
  - PUT /api/flights/:id - Update flight
  - DELETE /api/flights/:id - Delete flight
  - **Estimate**: 4 giờ

- [x] **Task 1.5**: T<PERSON><PERSON> schemas cho validation
  - FlightSchema với validation rules
  - ImportSchema cho Excel data
  - **Estimate**: 2 giờ

### Frontend Foundation
- [x] **Task 1.6**: Setup routing cho flight module
  - /flight-schedule - Dashboard
  - /flight-schedule/manage - Admin view
  - /flight-schedule/public - Public view
  - **Estimate**: 2 giờ

- [x] **Task 1.7**: Tạo layout cơ bản
  - Header với navigation
  - Sidebar cho filters
  - Main content area
  - **Estimate**: 3 giờ

**Total Phase 1**: ~15 giờ

## 📊 Phase 2: Core Features (2 tuần)

### Excel Import
- [x] **Task 2.1**: Tạo Excel parser utility
  - Parse Excel file với SheetJS
  - Validate format và structure
  - Handle errors gracefully
  - **Estimate**: 6 giờ

- [x] **Task 2.2**: Import UI component
  - File upload interface
  - Preview data trước khi import
  - Progress indicator
  - Error reporting
  - **Estimate**: 8 giờ

- [x] **Task 2.3**: Import API endpoint
  - POST /api/flights/import
  - Batch insert với transaction
  - Return detailed results
  - **Estimate**: 4 giờ

### Table Display & Inline Edit
- [x] **Task 2.4**: FlightTable component với TanStack Table
  - Display ARR/DEP columns
  - Responsive design
  - Pagination
  - **Estimate**: 8 giờ

- [x] **Task 2.5**: Inline editing functionality
  - Editable cells
  - Auto-save on blur
  - Validation feedback
  - Optimistic updates
  - **Estimate**: 10 giờ

- [x] **Task 2.6**: Change tracking system
  - Track mọi edit operation
  - Store trong flight_changes table
  - Real-time updates
  - **Estimate**: 6 giờ

### Data Management
- [ ] **Task 2.7**: TanStack Query hooks
  - useFlights hook
  - useFlightMutations hook
  - Caching strategy
  - **Estimate**: 4 giờ

- [ ] **Task 2.8**: Error handling & loading states
  - Loading skeletons
  - Error boundaries
  - Retry mechanisms
  - **Estimate**: 3 giờ

**Total Phase 2**: ~49 giờ

## 🔍 Phase 3: Advanced Features (1 tuần)

### Search & Filter
- [x] **Task 3.1**: SearchFilter component
  - Search by flight number
  - Search by staff name
  - Date range picker
  - **Estimate**: 6 giờ

- [x] **Task 3.2**: Advanced filtering logic
  - Filter API endpoints
  - Client-side filtering
  - URL state management
  - **Estimate**: 4 giờ

### Change History
- [ ] **Task 3.3**: ChangeHistory component
  - Display change timeline
  - Show old vs new values
  - Filter by field/user/date
  - **Estimate**: 6 giờ

- [ ] **Task 3.4**: Change history API
  - GET /api/flights/:id/changes
  - Pagination cho large history
  - **Estimate**: 2 giờ

### Export Functionality
- [ ] **Task 3.5**: Export utilities
  - Excel export với xlsx
  - PDF export với jsPDF
  - CSV export
  - **Estimate**: 6 giờ

- [ ] **Task 3.6**: ExportButton component
  - Export options UI
  - Progress indicator
  - Download handling
  - **Estimate**: 4 giờ

**Total Phase 3**: ~28 giờ

## ✨ Phase 4: Polish (1 tuần)

### Public View
- [ ] **Task 4.1**: Public flight schedule page
  - Read-only table view
  - No authentication required
  - Auto-refresh every 5 minutes
  - **Estimate**: 4 giờ

- [ ] **Task 4.2**: Mobile optimization cho public view
  - Card layout cho mobile
  - Touch-friendly interface
  - **Estimate**: 4 giờ

### Dashboard
- [ ] **Task 4.3**: Flight dashboard
  - Today's flights overview
  - Statistics cards
  - Upcoming flights alerts
  - **Estimate**: 6 giờ

- [ ] **Task 4.4**: Dashboard widgets
  - Flight count by hour
  - Staff workload
  - Recent changes
  - **Estimate**: 4 giờ

### Final Polish
- [ ] **Task 4.5**: Mobile responsive cho admin view
  - Responsive table
  - Mobile-friendly editing
  - **Estimate**: 6 giờ

- [ ] **Task 4.6**: Performance optimization
  - Code splitting
  - Lazy loading
  - Bundle optimization
  - **Estimate**: 3 giờ

- [ ] **Task 4.7**: Testing & bug fixes
  - Unit tests
  - Integration tests
  - Bug fixes
  - **Estimate**: 5 giờ

**Total Phase 4**: ~32 giờ

## 📝 Detailed Task Breakdown

### Priority Tasks (Must Have)
1. Database setup (Tasks 1.1-1.3)
2. Basic API (Task 1.4)
3. Excel import (Tasks 2.1-2.3)
4. Table display (Task 2.4)
5. Inline editing (Task 2.5)
6. Public view (Task 4.1)

### Secondary Tasks (Should Have)
1. Search & filter (Tasks 3.1-3.2)
2. Change history (Tasks 3.3-3.4)
3. Export functionality (Tasks 3.5-3.6)
4. Dashboard (Tasks 4.3-4.4)

### Nice to Have
1. Mobile optimization (Tasks 4.2, 4.5)
2. Performance optimization (Task 4.6)
3. Advanced testing (Task 4.7)

## ⏱️ Timeline Summary

| Phase | Duration | Total Hours | Key Deliverables |
|-------|----------|-------------|------------------|
| Phase 1 | 1 tuần | 15h | Database + Basic API + Layout |
| Phase 2 | 2 tuần | 49h | Import + Table + Inline Edit |
| Phase 3 | 1 tuần | 28h | Search + History + Export |
| Phase 4 | 1 tuần | 32h | Public View + Dashboard + Polish |
| **Total** | **5 tuần** | **124h** | **Complete Flight Schedule Module** |

## 🎯 Success Criteria

### Phase 1 Complete
- ✅ Database tables created
- ✅ Basic API endpoints working
- ✅ Frontend routing setup

### Phase 2 Complete
- ✅ Excel import working
- ✅ Table displays flight data
- ✅ Inline editing functional
- ✅ Changes tracked

### Phase 3 Complete
- ✅ Search & filter working
- ✅ Change history visible
- ✅ Export functionality working

### Phase 4 Complete
- ✅ Public view accessible
- ✅ Dashboard functional
- ✅ Mobile responsive
- ✅ Production ready

## 🚀 Getting Started

1. **Bắt đầu với Phase 1, Task 1.1**: Tạo database migration
2. **Setup development environment**: Đảm bảo Bun, Cloudflare CLI ready
3. **Create feature branch**: `git checkout -b feature/flight-schedule`
4. **Follow task order**: Complete tasks theo thứ tự priority
