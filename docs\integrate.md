# Kế hoạch tích hợp Durable Objects + KV

## 🎯 Mục tiêu đơn giản

Tích hợp Durable Objects và KV vào dự án quan-ly-ttb để cải thiện performance và thêm một số tính năng real-time cơ bản, **KHÔNG phức tạp**.

## 🚀 Lợi ích thực tế

### 1. KV Cache Layer
- **Fast search**: Cache kết quả tìm kiếm flight/device
- **Device status cache**: Truy cập nhanh trạng thái thiết bị
- **Session management**: Lưu user sessions
- **Configuration cache**: Cache settings, dropdown data

### 2. Durable Objects (Simple Use Cases)
- **Device coordination**: Tránh conflict khi multiple users cùng borrow/return device
- **Real-time notifications**: Thông báo khi device status thay đổi
- **Live status updates**: Auto-refresh device/flight status
- **<PERSON>min → Public sync**: Admin chỉnh sửa → Public view update tức thời (< 1s)
- **Flight table performance**: C<PERSON>i thiện hiệu năng cho hàng trăm chuyến bay
- **Simple WebSocket**: Chỉ cho notifications và live updates, không phức tạp

## 📋 Phase 1: KV Integration (1 tuần)

### Task 1.1: Setup KV Namespaces
```bash
# Create KV namespaces
bunx wrangler kv namespace create "CACHE_KV"
bunx wrangler kv namespace create "SESSION_KV"
```

### Task 1.2: Update wrangler.jsonc
```jsonc
{
  "kv_namespaces": [
    {
      "binding": "CACHE_KV",
      "id": "your-cache-namespace-id"
    },
    {
      "binding": "SESSION_KV", 
      "id": "your-session-namespace-id"
    }
  ]
}
```

### Task 1.3: KV Helper Functions
```typescript
// src/api/lib/kv-cache.ts
export class KVCache {
  static async get(kv: KVNamespace, key: string) {
    return await kv.get(key, 'json')
  }
  
  static async set(kv: KVNamespace, key: string, value: any, ttl = 3600) {
    return await kv.put(key, JSON.stringify(value), { expirationTtl: ttl })
  }
  
  static async delete(kv: KVNamespace, key: string) {
    return await kv.delete(key)
  }
}
```

### Task 1.4: Cache Implementation
- **Device search cache**: 30 phút TTL
- **Flight search cache**: 15 phút TTL
- **Flight data cache**: 10 phút TTL (per date)
- **Search results cache**: 15 phút TTL (per query)
- **User session cache**: 24 giờ TTL
- **Device status cache**: 5 phút TTL

**Estimate**: 15 giờ

## 📋 Phase 2: Simple Durable Objects (1 tuần)

### Task 2.1: Device Status Coordinator
```typescript
// src/api/durable-objects/DeviceCoordinator.ts
export class DeviceCoordinator extends DurableObject {
  constructor(ctx: DurableObjectState, env: Env) {
    super(ctx, env)
  }

  // Simple device status updates
  async updateDeviceStatus(deviceId: string, status: string, userId: string) {
    // Update D1
    // Update KV cache
    // Broadcast to connected clients (simple)
  }
  
  // Simple WebSocket for notifications
  async handleWebSocket(request: Request) {
    // Basic WebSocket connection
    // Send device status updates
  }
}
```

### Task 2.2: Flight Search Coordinator
```typescript
// src/api/durable-objects/FlightSearchCoordinator.ts
export class FlightSearchCoordinator extends DurableObject {
  // Optimized search for hundreds of flights
  async searchFlights(date: string, query: string) {
    // Check KV cache first
    const cacheKey = `search:${date}:${btoa(query)}`
    const cached = await this.env.CACHE_KV.get(cacheKey, 'json')
    if (cached) return cached

    // Perform optimized search in SQLite
    const results = await this.ctx.storage.sql.exec(`
      SELECT * FROM flights
      WHERE date = ?
      AND (arr_flt LIKE ? OR dep_flt LIKE ? OR arr_staff LIKE ? OR dep_staff LIKE ?)
      ORDER BY stt ASC
    `, [date, `%${query}%`, `%${query}%`, `%${query}%`, `%${query}%`])

    // Cache results for 15 minutes
    await this.env.CACHE_KV.put(cacheKey, JSON.stringify(results), { expirationTtl: 15 * 60 })
    return results
  }

  // Simple flight status updates
  async updateFlightStatus(flightId: string, field: string, value: any) {
    // Update D1
    // Clear related KV cache
    // Simple notification
  }
}
```

### Task 2.3: Update wrangler.jsonc
```jsonc
{
  "durable_objects": {
    "bindings": [
      {
        "name": "DEVICE_COORDINATOR",
        "class_name": "DeviceCoordinator"
      },
      {
        "name": "FLIGHT_SEARCH_COORDINATOR",
        "class_name": "FlightSearchCoordinator"
      }
    ]
  },
  "migrations": [
    {
      "tag": "v1",
      "new_sqlite_classes": ["DeviceCoordinator", "FlightSearchCoordinator"]
    }
  ]
}
```

**Estimate**: 20 giờ

## 📋 Phase 3: Frontend Integration (1 tuần)

### Task 3.1: KV Cache Integration
```typescript
// src/hooks/useDevicesWithCache.ts
export function useDevicesWithCache() {
  return useQuery({
    queryKey: ['devices'],
    queryFn: async () => {
      // Try cache first, fallback to API
      const cached = await getCachedDevices()
      if (cached) return cached
      
      const fresh = await fetchDevices()
      await setCachedDevices(fresh)
      return fresh
    },
    staleTime: 5 * 60 * 1000 // 5 minutes
  })
}
```

### Task 3.2: Simple WebSocket Hook
```typescript
// src/hooks/useDeviceNotifications.ts
export function useDeviceNotifications() {
  const [notifications, setNotifications] = useState([])
  
  useEffect(() => {
    // Simple WebSocket connection
    const ws = new WebSocket('/api/device-notifications')
    
    ws.onmessage = (event) => {
      const notification = JSON.parse(event.data)
      setNotifications(prev => [...prev, notification])
      
      // Show toast notification
      toast({
        title: 'Cập nhật thiết bị',
        description: notification.message
      })
    }
    
    return () => ws.close()
  }, [])
  
  return notifications
}
```

### Task 3.3: Optimized Flight Table Performance
```typescript
// src/hooks/useOptimizedFlights.ts
export function useOptimizedFlights(date: string, searchTerm: string) {
  return useQuery({
    queryKey: ['flights-optimized', date, searchTerm],
    queryFn: async () => {
      // Try KV cache first for search results
      if (searchTerm) {
        const cacheKey = `search:${date}:${btoa(searchTerm)}`
        const cached = await getCachedSearchResults(cacheKey)
        if (cached) return cached
      }

      // Fallback to API with server-side search
      const response = await fetch(`/api/flights/search?date=${date}&q=${searchTerm}`)
      const result = await response.json()

      // Cache search results for 15 minutes
      if (searchTerm) {
        await setCachedSearchResults(`search:${date}:${btoa(searchTerm)}`, result, 15 * 60)
      }

      return result
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!date
  })
}
```

### Task 3.4: Virtual Scrolling for Large Tables
```typescript
// src/components/flight-schedule/VirtualFlightTable.tsx
import { useVirtualizer } from '@tanstack/react-virtual'

export function VirtualFlightTable({ data, ...props }) {
  const parentRef = useRef<HTMLDivElement>(null)

  const virtualizer = useVirtualizer({
    count: data.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 60, // Row height
    overscan: 10 // Render 10 extra rows for smooth scrolling
  })

  return (
    <div ref={parentRef} className="h-[600px] overflow-auto">
      <div style={{ height: virtualizer.getTotalSize() }}>
        {virtualizer.getVirtualItems().map((virtualRow) => {
          const flight = data[virtualRow.index]
          return (
            <div
              key={virtualRow.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`,
              }}
            >
              <FlightRow flight={flight} {...props} />
            </div>
          )
        })}
      </div>
    </div>
  )
}
```

### Task 3.5: Auto-refresh Components
```typescript
// src/components/DeviceStatusBadge.tsx
export function DeviceStatusBadge({ deviceId }: { deviceId: string }) {
  const { data: status } = useQuery({
    queryKey: ['device-status', deviceId],
    queryFn: () => fetchDeviceStatus(deviceId),
    refetchInterval: 30000 // Auto-refresh every 30s
  })

  // Simple status display with auto-refresh
}
```

**Estimate**: 20 giờ

## 📋 Phase 4: Testing & Polish (0.5 tuần)

### Task 4.1: Testing
- Test KV cache hit/miss
- Test DO coordination
- Test WebSocket notifications
- Performance testing

### Task 4.2: Documentation
- Update API docs
- Add deployment notes
- Performance guidelines

**Estimate**: 10 giờ

## 🎯 Success Criteria (Đơn giản)

### Phase 1 Complete
- ✅ KV namespaces created
- ✅ Cache helper functions working
- ✅ Search results cached
- ✅ Device status cached

### Phase 2 Complete  
- ✅ DO classes deployed
- ✅ Device status coordination working
- ✅ Simple WebSocket notifications
- ✅ No conflicts when updating devices

### Phase 3 Complete
- ✅ Frontend uses cached data
- ✅ Auto-refresh working
- ✅ Toast notifications for updates
- ✅ Better user experience

### Phase 4 Complete
- ✅ All tests passing
- ✅ Documentation updated
- ✅ Performance improved

## 📊 Timeline Summary

| Phase | Duration | Hours | Key Deliverables |
|-------|----------|-------|------------------|
| Phase 1 | 1 tuần | 15h | KV Cache Layer |
| Phase 2 | 1 tuần | 20h | Simple Durable Objects |
| Phase 3 | 1 tuần | 20h | Frontend Integration + Performance |
| Phase 4 | 0.5 tuần | 10h | Testing & Polish |
| **Total** | **3.5 tuần** | **65h** | **Simple DO + KV Integration** |

## 🚫 Những gì KHÔNG làm

- ❌ Complex collaborative editing
- ❌ Advanced conflict resolution  
- ❌ Complex WebSocket features
- ❌ Complex pagination systems
- ❌ Advanced caching strategies
- ❌ Complex state management
- ❌ Over-engineering

## ✅ Những gì SẼ làm

- ✅ Simple caching for performance
- ✅ Basic device coordination
- ✅ Simple notifications
- ✅ **Admin → Public real-time sync** (< 1 second)
- ✅ **Flight table performance** for hundreds of flights
- ✅ **Virtual scrolling** instead of pagination
- ✅ **Smart caching** for search results
- ✅ Auto-refresh data
- ✅ Better user experience
- ✅ Practical improvements

## 🔧 Implementation Notes

### KV TTL Strategy
```typescript
const TTL_CONFIG = {
  DEVICE_STATUS: 5 * 60,      // 5 minutes
  FLIGHT_DATA: 10 * 60,       // 10 minutes (per date)
  SEARCH_RESULTS: 15 * 60,    // 15 minutes (per query)
  USER_SESSION: 24 * 60 * 60, // 24 hours
  CONFIG_DATA: 60 * 60,       // 1 hour
}
```

### DO Instance Strategy
```typescript
// Simple naming convention
const deviceCoordinatorId = env.DEVICE_COORDINATOR.idFromName('global')
const flightSearchCoordinatorId = env.FLIGHT_SEARCH_COORDINATOR.idFromName(date)
```

### Error Handling
```typescript
// Graceful fallback
try {
  const cached = await kv.get(key)
  if (cached) return cached
} catch (error) {
  console.warn('KV cache miss, falling back to D1')
}

// Always fallback to D1
return await fetchFromD1()
```

## 🎯 Expected Benefits

1. **Performance**: 30-50% faster search/lookup
2. **Flight Table**: Smooth scrolling với hundreds of flights (no pagination needed)
3. **Real-time Sync**: Admin chỉnh sửa → Public view update tức thời (< 1s)
4. **Memory Usage**: Giảm 80% memory với virtual scrolling
5. **User Experience**: Real-time notifications, no manual refresh needed
6. **Reliability**: Better coordination, fewer conflicts
7. **Scalability**: Ready for future growth
8. **Cost**: Minimal additional cost with free tiers

## 📊 Flight Table Performance Improvements

### Before (hiện tại):
```
Load Time: 2-5 seconds (load all flights)
Search: 500ms-1s (client-side filtering)
Memory: ~50MB (all flight data)
DOM Nodes: 500+ rows rendered
Scrolling: Laggy with many flights
```

### After (với DO + KV + Virtual Scrolling):
```
Load Time: 200-500ms (cached + virtual)
Search: 50-100ms (server-side + cached)
Memory: ~5MB (only visible data)
DOM Nodes: 10-20 rows rendered
Scrolling: Smooth với unlimited flights
```

## 🔄 Real-time Flow Example

```
Admin Page                    Public View
    ↓                            ↓
[Edit flight time] ────────→ [Time updates instantly]
[Confirm status]   ────────→ [Status icon changes]
[Assign device]    ────────→ [Device status updates]
    ↓                            ↓
< 1 second delay              Smooth UX
```
