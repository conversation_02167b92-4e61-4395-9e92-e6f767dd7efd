# Multi-term Search Enhancement

## Tổng quan

Tính năng tìm kiếm đa từ khóa cho phép users tìm kiếm nhiều nhân viên hoặc số đăng ký máy bay cùng lúc trong bảng lịch bay, gi<PERSON><PERSON> tăng hiệu quả trong công việc quản lý dịch vụ mặt đất.

## Tính năng chính

### 1. Multi-term Search Support
- **Staff Names**: Tìm kiếm nhiều tên nhân viên cùng lúc (ví dụ: "MINH, TU")
- **REG Numbers**: Tìm kiếm nhiều số đăng ký máy bay (ví dụ: "VN-A123, VN-B456") 
- **Mixed Search**: Kết hợp tìm kiếm nhân viên và số đăng ký (ví dụ: "MINH, VN-A123")

### 2. Enhanced Highlighting
- Highlighting màu vàng cho tất cả các từ khóa khớp
- <PERSON><PERSON><PERSON> tồn formatting đặc biệt cho tên nhân viên:
  - <PERSON><PERSON><PERSON> xanh cho tên có '*' (chuyến bay đầu tiên)
  - Hỗ trợ tên kết hợp với '/' separator
- Merge overlapping matches để tránh highlighting trùng lặp

### 3. Vietnamese Support
- Hỗ trợ tìm kiếm không phân biệt dấu (diacritic-insensitive)
- Tìm kiếm không phân biệt hoa thường (case-insensitive)
- Hoạt động với tên tiếng Việt như 'Thiện', 'Tuấn'

## Cách sử dụng

### Syntax
Sử dụng dấu phẩy (,) để phân cách các từ khóa:
```
MINH, TU, NAM
VN-A123, VN-B456
MINH, VN-A123, TU
```

### Ví dụ thực tế

#### Tìm kiếm nhiều nhân viên:
```
Input: "MINH, TU"
Result: Highlight cả "MINH" và "TU" trong các cột STAFF
```

#### Tìm kiếm nhiều số đăng ký:
```
Input: "VN-A123, VN-B456"
Result: Highlight cả hai số đăng ký trong các cột REG
```

#### Tìm kiếm kết hợp:
```
Input: "MINH, VN-A123"
Result: Highlight "MINH" trong cột STAFF và "VN-A123" trong cột REG
```

#### Tìm kiếm tiếng Việt:
```
Input: "Thiện, tuan"
Result: Highlight "Thiện" và "Tuấn" (không phân biệt dấu)
```

## Technical Implementation

### Core Functions

#### `parseSearchTerms(searchInput: string): string[]`
Parse input thành array các từ khóa:
```typescript
parseSearchTerms("MINH, TU") // ["MINH", "TU"]
parseSearchTerms("  MINH  ,  TU  ") // ["MINH", "TU"]
```

#### `searchFlightData(searchInput: string, flight: any): boolean`
Kiểm tra xem flight có khớp với bất kỳ từ khóa nào:
```typescript
// OR logic: trả về true nếu ANY term matches ANY field
searchFlightData("MINH, VN-A123", flight) // true nếu có MINH hoặc VN-A123
```

#### `highlightText(text: string, searchInput: string): string`
Tạo HTML với highlighting cho multiple terms:
```typescript
highlightText("MINH/TU", "MINH, TU")
// "<mark>MINH</mark>/<mark>TU</mark>"
```

### Components Updated

1. **SearchFilter.tsx**: Cập nhật placeholder text
2. **FlightScheduleToolbar.tsx**: Cập nhật placeholder text  
3. **EditableCell.tsx**: Đã hỗ trợ highlighting (không cần thay đổi)
4. **FlightTable.tsx**: Đã truyền searchTerm (không cần thay đổi)

## Performance

- Optimized highlighting algorithm với merge overlapping matches
- Efficient regex matching cho exact và diacritic-insensitive search
- Test performance: xử lý 50 terms trong <100ms

## Testing

### Unit Tests
- 34 test cases cho single và multi-term functionality
- Coverage cho Vietnamese diacritics, special characters
- Edge cases: empty input, overlapping matches

### Demo Tests  
- 9 real-world scenario tests
- Performance benchmarks
- Complex input parsing

## Browser Compatibility

- Hoạt động trên tất cả modern browsers
- Sử dụng standard JavaScript APIs
- CSS highlighting với Tailwind classes

## Future Enhancements

1. **Advanced Operators**: Hỗ trợ AND/OR logic
2. **Regex Support**: Cho power users
3. **Saved Searches**: Lưu các tìm kiếm thường dùng
4. **Search History**: Lịch sử tìm kiếm gần đây

## Migration Notes

- **Backward Compatible**: Tìm kiếm single term vẫn hoạt động như cũ
- **No Breaking Changes**: Existing code không cần thay đổi
- **Progressive Enhancement**: Multi-term là tính năng bổ sung

## Examples in UI

### Admin View
- Highlighting trong EditableCell components
- Bảo tồn blue text cho first flight assignments

### Public View  
- Highlighting trong HighlightedText components
- Consistent styling với admin view
- Responsive design maintained
