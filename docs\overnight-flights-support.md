# Hỗ trợ Chu<PERSON>ến bay Qua đêm (Overnight Flights)

## Tổng quan

Hệ thống quản lý lịch bay hiện đã hỗ trợ chuyến bay qua đêm với dấu "+" để chỉ thời gian diễn ra vào ngày hôm sau.

## Định dạng thời gian

### Format được hỗ trợ:
- **HH:MM** - Thờ<PERSON> gian chu<PERSON>n (ví dụ: `08:30`, `14:45`)
- **HH:MM+** - Thời gian qua đêm (ví dụ: `02:30+`, `01:15+`)

### Ví dụ sử dụng:
- `23:45` - Chuyến bay khởi hành 23:45 cùng ngày
- `02:30+` - Chuyến bay đến 02:30 ngày hôm sau
- `01:15+` - Chuyến bay khởi hành 01:15 ngày hôm sau

## T<PERSON>h năng được cập nhật

### 1. Validation <PERSON>as
- **File**: `src/lib/flight-validation.ts`, `src/lib/flight-schemas.ts`
- **Thay đổi**: Regex pattern từ `/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/` thành `/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](\+)?$/`
- **Mục đích**: Cho phép dấu "+" optional ở cuối

### 2. EditableCell Component
- **File**: `src/components/flight-schedule/EditableCell.tsx`
- **Thay đổi**: 
  - Thêm type `'flight-time'` 
  - Sử dụng text input thay vì time input cho flight-time fields
- **Mục đích**: HTML time input không hỗ trợ custom suffix như "+"

### 3. FlightTable Component
- **File**: `src/components/flight-schedule/FlightTable.tsx`
- **Thay đổi**:
  - Đổi type từ `"time"` thành `"flight-time"` cho arr_time và dep_time
  - Cập nhật placeholder thành `"HH:MM hoặc HH:MM+"`
- **Mục đích**: Hiển thị đúng format và cho phép nhập dấu "+"

### 4. Excel Parser
- **File**: `src/lib/excel-parser.ts`
- **Thay đổi**: Cập nhật regex trong `cleanTimeValue` function để nhận diện format có dấu "+"
- **Mục đích**: Đảm bảo Excel import preserve dấu "+" từ file gốc

## Cách sử dụng

### 1. Nhập thủ công (Inline Editing)
1. Click vào ô thời gian trong bảng lịch bay
2. Nhập thời gian theo format:
   - `HH:MM` cho thời gian cùng ngày
   - `HH:MM+` cho thời gian ngày hôm sau
3. Nhấn Enter để lưu hoặc Escape để hủy

### 2. Import từ Excel
- File Excel có thể chứa thời gian với dấu "+" (ví dụ: `02:30+`)
- Hệ thống sẽ tự động preserve và validate format này
- Không cần thay đổi gì trong file Excel hiện tại

### 3. Hiển thị
- Thời gian với dấu "+" sẽ được hiển thị nguyên vẹn trong cả admin view và public view
- Dấu "+" giúp phân biệt rõ ràng chuyến bay qua đêm

## Validation Rules

### Thời gian hợp lệ:
- `00:00` đến `23:59` (format chuẩn)
- `00:00+` đến `23:59+` (format qua đêm)
- Giờ: 00-23, Phút: 00-59
- Dấu "+" là optional và chỉ xuất hiện ở cuối

### Thời gian không hợp lệ:
- `25:00` (giờ > 23)
- `12:60` (phút > 59)
- `08:30++` (nhiều dấu +)
- `08:30 +` (có space trước +)
- `+08:30` (dấu + ở đầu)

## Tương thích ngược

- Tất cả thời gian hiện tại không có dấu "+" vẫn hoạt động bình thường
- Không cần migration database
- Excel files hiện tại vẫn import được
- Không ảnh hưởng đến public view

## Testing

Đã test với các trường hợp:
- ✅ Thời gian chuẩn (08:30, 14:45, 23:59, 00:00)
- ✅ Thời gian qua đêm (02:30+, 01:15+, 23:45+, 00:30+)
- ✅ Validation các format không hợp lệ
- ✅ Excel import với dấu "+"
- ✅ Inline editing với dấu "+"
- ✅ Hiển thị trong admin và public view

## Lưu ý kỹ thuật

1. **Database**: Time fields vẫn là TEXT, có thể chứa dấu "+"
2. **Frontend**: Sử dụng text input thay vì time input cho flexibility
3. **Validation**: Regex pattern đã được cập nhật ở tất cả nơi cần thiết
4. **Excel**: Parser đã được cập nhật để handle format mới

## Tác động

- ✅ Tăng tính chính xác cho lịch bay qua đêm
- ✅ Đồng nhất giữa Excel import và manual editing
- ✅ Không breaking changes cho dữ liệu hiện tại
- ✅ User experience tốt hơn cho staff quản lý lịch bay
