// Script để generate bcrypt hash cho password
import bcrypt from 'bcryptjs';

async function generateHash() {
  const password = 'admin123';
  const saltRounds = 10;
  
  try {
    const hash = await bcrypt.hash(password, saltRounds);
    console.log('Password:', password);
    console.log('Hash:', hash);
    
    // Test verify
    const isValid = await bcrypt.compare(password, hash);
    console.log('Verification:', isValid);
  } catch (error) {
    console.error('Error:', error);
  }
}

generateHash();
