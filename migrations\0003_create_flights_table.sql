-- Migration: Create flights table for flight schedule management
-- Created: 2024-12-19
-- Description: Main table to store daily flight schedules with arrival and departure information

CREATE TABLE IF NOT EXISTS flights (
  id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
  date TEXT NOT NULL, -- Format: YYYY-MM-DD
  stt INTEGER NOT NULL, -- <PERSON><PERSON> thứ tự trong ngày
  
  -- Arrival Information (Chuyến bay đến)
  arr_flt TEXT, -- Flight number (VD: VN123)
  arr_from TEXT, -- Departure airport code (VD: HAN, SGN)
  arr_reg TEXT, -- Aircraft registration (VD: VN-A123)
  arr_time TEXT, -- Arrival time (VD: 08:30)
  arr_staff TEXT, -- Staff assigned to arrival
  
  -- Departure Information (Chuyến bay đi)
  dep_flt TEXT, -- Flight number (VD: VN456)
  dep_to TEXT, -- Destination airport code (VD: HAN, SGN)
  dep_reg TEXT, -- Aircraft registration (VD: VN-A456)
  dep_time TEXT, -- Departure time (VD: 10:15)
  dep_staff TEXT, -- Staff assigned to departure
  
  -- Metadata
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT DEFAULT 'system',
  updated_by TEXT DEFAULT 'system'
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_flights_date ON flights(date);
CREATE INDEX IF NOT EXISTS idx_flights_date_stt ON flights(date, stt);
CREATE INDEX IF NOT EXISTS idx_flights_arr_flt ON flights(arr_flt);
CREATE INDEX IF NOT EXISTS idx_flights_dep_flt ON flights(dep_flt);
CREATE INDEX IF NOT EXISTS idx_flights_arr_staff ON flights(arr_staff);
CREATE INDEX IF NOT EXISTS idx_flights_dep_staff ON flights(dep_staff);
CREATE INDEX IF NOT EXISTS idx_flights_created_at ON flights(created_at);

-- Unique constraint to prevent duplicate STT on same date
CREATE UNIQUE INDEX IF NOT EXISTS idx_flights_date_stt_unique ON flights(date, stt);

-- Trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_flights_updated_at
  AFTER UPDATE ON flights
  FOR EACH ROW
BEGIN
  UPDATE flights SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
