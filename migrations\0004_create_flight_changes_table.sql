-- Migration: Create flight_changes table for audit trail
-- Created: 2024-12-19
-- Description: Track all changes made to flight records for audit and history purposes

CREATE TABLE IF NOT EXISTS flight_changes (
  id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
  flight_id TEXT NOT NULL,
  field_name TEXT NOT NULL, -- Field that was changed (arr_flt, dep_staff, etc.)
  old_value TEXT, -- Previous value (can be NULL for new records)
  new_value TEXT, -- New value (can be NULL for deletions)
  change_type TEXT NOT NULL DEFAULT 'update', -- 'create', 'update', 'delete'
  
  -- Audit information
  changed_by TEXT NOT NULL, -- User who made the change
  changed_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ip_address TEXT, -- Optional: IP address of the user
  user_agent TEXT, -- Optional: Browser/client information
  
  -- Additional context
  reason TEXT, -- Optional: Reason for change
  batch_id TEXT, -- Optional: Group related changes (e.g., Excel import)
  
  FOREIGN KEY (flight_id) REFERENCES flights(id) ON DELETE CASCADE
);

-- Indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_flight_changes_flight_id ON flight_changes(flight_id);
CREATE INDEX IF NOT EXISTS idx_flight_changes_changed_at ON flight_changes(changed_at);
CREATE INDEX IF NOT EXISTS idx_flight_changes_changed_by ON flight_changes(changed_by);
CREATE INDEX IF NOT EXISTS idx_flight_changes_field_name ON flight_changes(field_name);
CREATE INDEX IF NOT EXISTS idx_flight_changes_change_type ON flight_changes(change_type);
CREATE INDEX IF NOT EXISTS idx_flight_changes_batch_id ON flight_changes(batch_id);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_flight_changes_flight_field ON flight_changes(flight_id, field_name);
CREATE INDEX IF NOT EXISTS idx_flight_changes_user_date ON flight_changes(changed_by, changed_at);

-- View to get latest changes with flight information
CREATE VIEW IF NOT EXISTS v_recent_flight_changes AS
SELECT 
  fc.id,
  fc.flight_id,
  f.date as flight_date,
  f.stt as flight_stt,
  fc.field_name,
  fc.old_value,
  fc.new_value,
  fc.change_type,
  fc.changed_by,
  fc.changed_at,
  fc.reason,
  fc.batch_id,
  -- Friendly field names in Vietnamese
  CASE fc.field_name
    WHEN 'arr_flt' THEN 'Số hiệu bay đến'
    WHEN 'arr_from' THEN 'Điểm khởi hành'
    WHEN 'arr_reg' THEN 'Số đăng ký máy bay đến'
    WHEN 'arr_time' THEN 'Giờ đến'
    WHEN 'arr_staff' THEN 'Nhân viên phục vụ đến'
    WHEN 'dep_flt' THEN 'Số hiệu bay đi'
    WHEN 'dep_to' THEN 'Điểm đến'
    WHEN 'dep_reg' THEN 'Số đăng ký máy bay đi'
    WHEN 'dep_time' THEN 'Giờ khởi hành'
    WHEN 'dep_staff' THEN 'Nhân viên phục vụ đi'
    ELSE fc.field_name
  END as field_display_name
FROM flight_changes fc
LEFT JOIN flights f ON fc.flight_id = f.id
ORDER BY fc.changed_at DESC;
