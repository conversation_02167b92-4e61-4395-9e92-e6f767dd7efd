-- Migration: Create flight_imports table for import tracking
-- Created: 2024-12-19
-- Description: Track Excel import operations and their results

CREATE TABLE IF NOT EXISTS flight_imports (
  id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
  filename TEXT NOT NULL, -- Original filename of uploaded Excel
  file_size INTEGER, -- File size in bytes
  file_hash TEXT, -- SHA-256 hash to detect duplicate imports
  
  -- Import metadata
  imported_by TEXT NOT NULL, -- User who performed the import
  imported_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  import_date TEXT NOT NULL, -- Date for which the schedule was imported (YYYY-MM-DD)
  
  -- Import results
  total_rows INTEGER NOT NULL DEFAULT 0, -- Total rows in Excel file
  processed_rows INTEGER NOT NULL DEFAULT 0, -- Rows that were processed
  success_rows INTEGER NOT NULL DEFAULT 0, -- Successfully imported rows
  error_rows INTEGER NOT NULL DEFAULT 0, -- Rows with errors
  skipped_rows INTEGER NOT NULL DEFAULT 0, -- Rows that were skipped
  
  -- Status and details
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  errors TEXT, -- JSON array of error details
  warnings TEXT, -- JSON array of warning messages
  summary TEXT, -- Human-readable summary of import
  
  -- Processing time
  started_at TEXT,
  completed_at TEXT,
  processing_time_ms INTEGER, -- Processing time in milliseconds
  
  -- Additional metadata
  excel_sheet_name TEXT, -- Name of the Excel sheet that was imported
  import_mode TEXT DEFAULT 'replace', -- 'replace', 'append', 'update'
  backup_created BOOLEAN DEFAULT FALSE, -- Whether backup was created before import
  
  UNIQUE(file_hash, import_date) -- Prevent duplicate imports of same file for same date
);

-- Indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_flight_imports_imported_by ON flight_imports(imported_by);
CREATE INDEX IF NOT EXISTS idx_flight_imports_imported_at ON flight_imports(imported_at);
CREATE INDEX IF NOT EXISTS idx_flight_imports_import_date ON flight_imports(import_date);
CREATE INDEX IF NOT EXISTS idx_flight_imports_status ON flight_imports(status);
CREATE INDEX IF NOT EXISTS idx_flight_imports_file_hash ON flight_imports(file_hash);

-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_flight_imports_user_date ON flight_imports(imported_by, imported_at);
CREATE INDEX IF NOT EXISTS idx_flight_imports_date_status ON flight_imports(import_date, status);

-- View for import statistics
CREATE VIEW IF NOT EXISTS v_import_stats AS
SELECT 
  import_date,
  COUNT(*) as total_imports,
  SUM(success_rows) as total_success_rows,
  SUM(error_rows) as total_error_rows,
  SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_imports,
  SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_imports,
  AVG(processing_time_ms) as avg_processing_time_ms,
  MAX(imported_at) as last_import_at
FROM flight_imports
GROUP BY import_date
ORDER BY import_date DESC;

-- View for recent imports with user details
CREATE VIEW IF NOT EXISTS v_recent_imports AS
SELECT 
  fi.id,
  fi.filename,
  fi.imported_by,
  fi.imported_at,
  fi.import_date,
  fi.status,
  fi.total_rows,
  fi.success_rows,
  fi.error_rows,
  fi.processing_time_ms,
  fi.summary,
  -- Calculate success rate
  CASE 
    WHEN fi.total_rows > 0 THEN 
      ROUND((fi.success_rows * 100.0) / fi.total_rows, 2)
    ELSE 0 
  END as success_rate_percent
FROM flight_imports fi
ORDER BY fi.imported_at DESC
LIMIT 50;
