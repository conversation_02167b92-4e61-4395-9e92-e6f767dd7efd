-- Migration: Create flight_cell_colors table for cell coloring functionality
-- Created: 2024-12-19
-- Description: Store cell colors for flight schedule table cells with database persistence

CREATE TABLE IF NOT EXISTS flight_cell_colors (
  id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
  flight_id TEXT NOT NULL,
  field_name TEXT NOT NULL, -- e.g., 'arr_reg', 'dep_staff', 'remark', 'arr_time', 'dep_time'
  color_value TEXT NOT NULL, -- hex color code e.g., '#ff0000', '#00ff00'
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT NOT NULL,
  
  FOREIGN KEY (flight_id) REFERENCES flights(id) ON DELETE CASCADE,
  UNIQUE(flight_id, field_name) -- One color per field per flight
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_flight_cell_colors_flight_id ON flight_cell_colors(flight_id);
CREATE INDEX IF NOT EXISTS idx_flight_cell_colors_field ON flight_cell_colors(field_name);
CREATE INDEX IF NOT EXISTS idx_flight_cell_colors_created_by ON flight_cell_colors(created_by);

-- Comments for documentation
-- This table stores individual cell colors for the flight schedule table
-- Each row represents one colored cell identified by flight_id + field_name
-- Colors are stored as hex values and can be applied to any editable field
-- Foreign key constraint ensures data integrity when flights are deleted
