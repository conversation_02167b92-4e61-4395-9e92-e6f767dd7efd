-- Migration: Additional indexes and views for flight schedule optimization
-- Created: 2024-12-19
-- Description: Performance optimizations and useful views for flight schedule module

-- Additional performance indexes
CREATE INDEX IF NOT EXISTS idx_flights_arr_time ON flights(arr_time) WHERE arr_time IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_flights_dep_time ON flights(dep_time) WHERE dep_time IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_flights_arr_from ON flights(arr_from) WHERE arr_from IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_flights_dep_to ON flights(dep_to) WHERE dep_to IS NOT NULL;

-- Composite indexes for common search patterns
CREATE INDEX IF NOT EXISTS idx_flights_date_arr_flt ON flights(date, arr_flt) WHERE arr_flt IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_flights_date_dep_flt ON flights(date, dep_flt) WHERE dep_flt IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_flights_date_staff ON flights(date, arr_staff, dep_staff);

-- Full-text search support (for flight numbers and staff names)
CREATE INDEX IF NOT EXISTS idx_flights_search_arr ON flights(arr_flt, arr_staff) WHERE arr_flt IS NOT NULL OR arr_staff IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_flights_search_dep ON flights(dep_flt, dep_staff) WHERE dep_flt IS NOT NULL OR dep_staff IS NOT NULL;

-- View for today's flights
CREATE VIEW IF NOT EXISTS v_today_flights AS
SELECT 
  id,
  date,
  stt,
  arr_flt,
  arr_from,
  arr_reg,
  arr_time,
  arr_staff,
  dep_flt,
  dep_to,
  dep_reg,
  dep_time,
  dep_staff,
  created_at,
  updated_at
FROM flights 
WHERE date = date('now', 'localtime')
ORDER BY stt;

-- View for upcoming flights (next 7 days)
CREATE VIEW IF NOT EXISTS v_upcoming_flights AS
SELECT 
  id,
  date,
  stt,
  arr_flt,
  arr_from,
  arr_reg,
  arr_time,
  arr_staff,
  dep_flt,
  dep_to,
  dep_reg,
  dep_time,
  dep_staff,
  created_at,
  updated_at
FROM flights 
WHERE date BETWEEN date('now', 'localtime') AND date('now', 'localtime', '+7 days')
ORDER BY date, stt;

-- View for flight statistics by date
CREATE VIEW IF NOT EXISTS v_flight_stats_by_date AS
SELECT 
  date,
  COUNT(*) as total_flights,
  COUNT(CASE WHEN arr_flt IS NOT NULL THEN 1 END) as arrival_count,
  COUNT(CASE WHEN dep_flt IS NOT NULL THEN 1 END) as departure_count,
  COUNT(DISTINCT arr_staff) as unique_arr_staff,
  COUNT(DISTINCT dep_staff) as unique_dep_staff,
  COUNT(DISTINCT COALESCE(arr_staff, dep_staff)) as total_unique_staff,
  MIN(COALESCE(arr_time, dep_time)) as earliest_time,
  MAX(COALESCE(arr_time, dep_time)) as latest_time
FROM flights
GROUP BY date
ORDER BY date DESC;

-- View for staff workload
CREATE VIEW IF NOT EXISTS v_staff_workload AS
SELECT 
  date,
  staff_name,
  COUNT(*) as total_assignments,
  COUNT(CASE WHEN assignment_type = 'arrival' THEN 1 END) as arrival_assignments,
  COUNT(CASE WHEN assignment_type = 'departure' THEN 1 END) as departure_assignments,
  GROUP_CONCAT(flight_info, '; ') as flight_details
FROM (
  SELECT 
    date,
    arr_staff as staff_name,
    'arrival' as assignment_type,
    arr_flt || ' từ ' || COALESCE(arr_from, '?') || ' lúc ' || COALESCE(arr_time, '?') as flight_info
  FROM flights 
  WHERE arr_staff IS NOT NULL AND arr_staff != ''
  
  UNION ALL
  
  SELECT 
    date,
    dep_staff as staff_name,
    'departure' as assignment_type,
    dep_flt || ' đến ' || COALESCE(dep_to, '?') || ' lúc ' || COALESCE(dep_time, '?') as flight_info
  FROM flights 
  WHERE dep_staff IS NOT NULL AND dep_staff != ''
) staff_assignments
GROUP BY date, staff_name
ORDER BY date DESC, total_assignments DESC;

-- View for flight search (optimized for search functionality)
CREATE VIEW IF NOT EXISTS v_flight_search AS
SELECT 
  f.id,
  f.date,
  f.stt,
  f.arr_flt,
  f.arr_from,
  f.arr_time,
  f.arr_staff,
  f.dep_flt,
  f.dep_to,
  f.dep_time,
  f.dep_staff,
  f.updated_at,
  -- Searchable text fields
  COALESCE(f.arr_flt, '') || ' ' || COALESCE(f.dep_flt, '') as flight_numbers,
  COALESCE(f.arr_staff, '') || ' ' || COALESCE(f.dep_staff, '') as staff_names,
  COALESCE(f.arr_from, '') || ' ' || COALESCE(f.dep_to, '') as airports,
  -- Status indicators
  CASE WHEN f.arr_flt IS NOT NULL THEN 1 ELSE 0 END as has_arrival,
  CASE WHEN f.dep_flt IS NOT NULL THEN 1 ELSE 0 END as has_departure,
  -- Time sorting helpers
  CASE 
    WHEN f.arr_time IS NOT NULL AND f.dep_time IS NOT NULL THEN
      MIN(f.arr_time, f.dep_time)
    ELSE COALESCE(f.arr_time, f.dep_time)
  END as earliest_time
FROM flights f;

-- View for public schedule (read-only for staff)
CREATE VIEW IF NOT EXISTS v_public_schedule AS
SELECT 
  date,
  stt,
  arr_flt,
  arr_from,
  arr_time,
  dep_flt,
  dep_to,
  dep_time,
  -- Hide sensitive information in public view
  CASE 
    WHEN arr_staff IS NOT NULL AND arr_staff != '' THEN 'Đã phân công'
    ELSE 'Chưa phân công'
  END as arr_status,
  CASE 
    WHEN dep_staff IS NOT NULL AND dep_staff != '' THEN 'Đã phân công'
    ELSE 'Chưa phân công'
  END as dep_status
FROM flights
WHERE date >= date('now', 'localtime', '-1 day') -- Show from yesterday onwards
ORDER BY date, stt;
