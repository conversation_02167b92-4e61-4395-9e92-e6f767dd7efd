-- Migration: Add REMARK column to flights table
-- Date: 2024-01-15
-- Description: Add optional REMARK column to support new Excel import format

-- Add REMARK column to flights table
ALTER TABLE flights ADD COLUMN remark TEXT;

-- Add comment for documentation
-- REMARK column stores optional notes/comments for each flight
-- Maximum length: 500 characters (enforced by application layer)
-- Can be NULL or empty string

-- Update any existing flights to have NULL remark (already default)
-- No data migration needed as this is a new optional field

-- Create index on remark for search functionality (optional)
-- CREATE INDEX IF NOT EXISTS idx_flights_remark ON flights(remark) WHERE remark IS NOT NULL;
