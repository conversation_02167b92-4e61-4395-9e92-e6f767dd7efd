-- Migration: Add flight_changes table for change tracking
-- Date: 2024-01-15
-- Description: Tạ<PERSON> bảng flight_changes để theo dõi lịch sử thay đổi của flight schedule

-- Create flight_changes table
CREATE TABLE IF NOT EXISTS flight_changes (
  id TEXT PRIMARY KEY,
  flight_id TEXT NOT NULL,
  field_name TEXT NOT NULL,
  old_value TEXT,
  new_value TEXT,
  changed_by TEXT NOT NULL,
  changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  change_reason TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  -- Foreign key constraint
  FOREIGN KEY (flight_id) REFERENCES flights(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_flight_changes_flight_id ON flight_changes(flight_id);
CREATE INDEX IF NOT EXISTS idx_flight_changes_changed_at ON flight_changes(changed_at DESC);
CREATE INDEX IF NOT EXISTS idx_flight_changes_changed_by ON flight_changes(changed_by);
CREATE INDEX IF NOT EXISTS idx_flight_changes_field_name ON flight_changes(field_name);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_flight_changes_flight_field ON flight_changes(flight_id, field_name);

-- Add updated_by and updated_at columns to flights table if not exists
ALTER TABLE flights ADD COLUMN updated_by TEXT;
ALTER TABLE flights ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP;

-- Create trigger to automatically update updated_at when flights table is modified
CREATE TRIGGER IF NOT EXISTS update_flights_timestamp 
  AFTER UPDATE ON flights
  FOR EACH ROW
BEGIN
  UPDATE flights SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Insert sample data for testing (optional)
-- INSERT INTO flight_changes (
--   id, flight_id, field_name, old_value, new_value, changed_by, change_reason
-- ) VALUES (
--   'change-001',
--   'flight-001', 
--   'arr_time',
--   '04:40',
--   '04:45',
--   'admin',
--   'Cập nhật giờ đến do trễ chuyến'
-- );

-- Verify table creation
SELECT name FROM sqlite_master WHERE type='table' AND name='flight_changes';
