-- Migration: Add status columns to flights table for tracking flight confirmation status
-- File: 004_add_flight_status_columns.sql
-- Description: Adds columns to track arrival and departure status confirmations

-- Add status columns for arrival flights
ALTER TABLE flights ADD COLUMN arr_present BOOLEAN DEFAULT FALSE;
ALTER TABLE flights ADD COLUMN arr_finished BOOLEAN DEFAULT FALSE;

-- Add status columns for departure flights  
ALTER TABLE flights ADD COLUMN dep_present BOOLEAN DEFAULT FALSE;
ALTER TABLE flights ADD COLUMN dep_boarded BOOLEAN DEFAULT FALSE;
ALTER TABLE flights ADD COLUMN dep_finished BOOLEAN DEFAULT FALSE;

-- Add indexes for better query performance on status columns
CREATE INDEX idx_flights_arr_present ON flights(arr_present);
CREATE INDEX idx_flights_arr_finished ON flights(arr_finished);
CREATE INDEX idx_flights_dep_present ON flights(dep_present);
CREATE INDEX idx_flights_dep_boarded ON flights(dep_boarded);
CREATE INDEX idx_flights_dep_finished ON flights(dep_finished);

-- Add updated_at trigger to track when status changes
CREATE TRIGGER update_flights_updated_at 
    BEFORE UPDATE ON flights
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON COLUMN flights.arr_present IS 'Arrival flight: Staff present confirmation';
COMMENT ON COLUMN flights.arr_finished IS 'Arrival flight: Flight finished confirmation';
COMMENT ON COLUMN flights.dep_present IS 'Departure flight: Staff present confirmation';
COMMENT ON COLUMN flights.dep_boarded IS 'Departure flight: Boarding completed confirmation';
COMMENT ON COLUMN flights.dep_finished IS 'Departure flight: Flight finished confirmation';
