import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'

// Import routes
import walkieTalkiesRoutes from './routes/walkie-talkies'
import accessCardsRoutes from './routes/access-cards'
import historyRoutes from './routes/history'
import authRoutes from './routes/auth'
import flightsRoutes from './routes/flights'
import flightStatusRoutes from './routes/flight-status'
import publicFlightsRoutes from './routes/public-flights'
import flightCellColorsRoutes from './routes/flight-cell-colors'

// Create main Hono app
const app = new Hono()

// Global middleware
app.use('*', cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:8080',
    'http://localhost:8787',
    'https://quan-ly-ttb.hiepvu300497.workers.dev'
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}))

app.use('*', logger())
app.use('*', prettyJSON())

// Health check endpoint
app.get('/api/health', (c) => {
  return c.json({
    message: 'Quan Ly TTB API is running!',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    environment: c.env.NODE_ENV || 'development'
  })
})

// API routes
app.route('/api/auth', authRoutes)
app.route('/api/walkie-talkies', walkieTalkiesRoutes)
app.route('/api/access-cards', accessCardsRoutes)
app.route('/api/history', historyRoutes)
app.route('/api/flights', flightsRoutes)
app.route('/api/flight-status', flightStatusRoutes)
app.route('/api/public/flights', publicFlightsRoutes)
app.route('/api/flight-cell-colors', flightCellColorsRoutes)

// Serve static assets for SPA - fallback to ASSETS binding
app.get('*', async (c) => {
  const url = new URL(c.req.url)

  // If it's an API route, let it continue to 404 handler
  if (url.pathname.startsWith('/api/')) {
    return c.json({ error: 'Not Found', message: 'The requested endpoint does not exist' }, 404)
  }

  // For all other routes, serve static assets via ASSETS binding
  return c.env.ASSETS.fetch(c.req.raw)
})

// 404 handler for API routes only
app.notFound((c) => {
  const url = new URL(c.req.url)
  if (url.pathname.startsWith('/api/')) {
    return c.json({ error: 'Not Found', message: 'The requested endpoint does not exist' }, 404)
  }
  // For non-API routes, this shouldn't be reached due to the catch-all above
  return c.json({ error: 'Not Found' }, 404)
})

// Error handler
app.onError((err, c) => {
  console.error('API Error:', err)
  return c.json({ 
    error: 'Internal Server Error', 
    message: c.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  }, 500)
})

export default app
