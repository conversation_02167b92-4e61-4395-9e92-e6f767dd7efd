import bcrypt from 'bcryptjs'
import { SignJWT, jwtVerify, decodeJwt } from 'jose'
import { Context } from 'hono'

// JWT Secret - trong production nên dùng environment variable
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production'
const JWT_EXPIRES_IN = '15m' // Access token expires in 15 minutes
const REFRESH_TOKEN_EXPIRES_IN = '7d' // Refresh token expires in 7 days

export interface User {
  id: number
  username: string
  email: string
  full_name: string | null
  role: 'user' | 'admin'
  status: 'active' | 'inactive'
  last_login: string | null
  created_at: string
  updated_at: string
}

export interface JWTPayload {
  userId: number
  username: string
  email: string
  role: 'user' | 'admin'
  iat?: number
  exp?: number
}

/**
 * Hash password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10
  return await bcrypt.hash(password, saltRounds)
}

/**
 * Verify password against hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return await bcrypt.compare(password, hash)
}

/**
 * Generate JWT access token
 */
export async function generateAccessToken(user: User): Promise<string> {
  const payload: JWTPayload = {
    userId: user.id,
    username: user.username,
    email: user.email,
    role: user.role
  }

  const secret = new TextEncoder().encode(JWT_SECRET)

  return await new SignJWT(payload as Record<string, any>)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(JWT_EXPIRES_IN)
    .sign(secret)
}

/**
 * Generate JWT refresh token
 */
export async function generateRefreshToken(user: User): Promise<string> {
  const payload: JWTPayload = {
    userId: user.id,
    username: user.username,
    email: user.email,
    role: user.role
  }

  const secret = new TextEncoder().encode(JWT_SECRET)

  return await new SignJWT(payload as Record<string, any>)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(REFRESH_TOKEN_EXPIRES_IN)
    .sign(secret)
}

/**
 * Verify JWT token
 */
export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const secret = new TextEncoder().encode(JWT_SECRET)
    const { payload } = await jwtVerify(token, secret)
    return payload as unknown as JWTPayload
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}

/**
 * Extract token from Authorization header
 */
export function extractTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader) return null
  
  const parts = authHeader.split(' ')
  if (parts.length !== 2 || parts[0] !== 'Bearer') return null
  
  return parts[1]
}

/**
 * Get user from database by email
 */
export async function getUserByEmail(c: Context, email: string): Promise<User | null> {
  try {
    const result = await c.env.DB.prepare(
      'SELECT * FROM users WHERE email = ? AND status = ?'
    ).bind(email, 'active').first()
    
    return result as User | null
  } catch (error) {
    console.error('Error getting user by email:', error)
    return null
  }
}

/**
 * Get user from database by ID
 */
export async function getUserById(c: Context, userId: number): Promise<User | null> {
  try {
    const result = await c.env.DB.prepare(
      'SELECT * FROM users WHERE id = ? AND status = ?'
    ).bind(userId, 'active').first()
    
    return result as User | null
  } catch (error) {
    console.error('Error getting user by ID:', error)
    return null
  }
}

/**
 * Update user last login time
 */
export async function updateLastLogin(c: Context, userId: number): Promise<void> {
  try {
    await c.env.DB.prepare(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?'
    ).bind(userId).run()
  } catch (error) {
    console.error('Error updating last login:', error)
  }
}

/**
 * Store refresh token in database
 */
export async function storeRefreshToken(
  c: Context, 
  userId: number, 
  tokenHash: string, 
  expiresAt: Date
): Promise<void> {
  try {
    await c.env.DB.prepare(
      'INSERT INTO user_sessions (user_id, token_hash, expires_at) VALUES (?, ?, ?)'
    ).bind(userId, tokenHash, expiresAt.toISOString()).run()
  } catch (error) {
    console.error('Error storing refresh token:', error)
  }
}

/**
 * Remove refresh token from database
 */
export async function removeRefreshToken(c: Context, tokenHash: string): Promise<void> {
  try {
    await c.env.DB.prepare(
      'DELETE FROM user_sessions WHERE token_hash = ?'
    ).bind(tokenHash).run()
  } catch (error) {
    console.error('Error removing refresh token:', error)
  }
}

/**
 * Clean expired tokens from database
 */
export async function cleanExpiredTokens(c: Context): Promise<void> {
  try {
    await c.env.DB.prepare(
      'DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP'
    ).run()
  } catch (error) {
    console.error('Error cleaning expired tokens:', error)
  }
}

/**
 * Hash token for storage (for security)
 */
export function hashToken(token: string): string {
  return bcrypt.hashSync(token, 10)
}

/**
 * Verify stored token hash
 */
export function verifyTokenHash(token: string, hash: string): boolean {
  return bcrypt.compareSync(token, hash)
}
