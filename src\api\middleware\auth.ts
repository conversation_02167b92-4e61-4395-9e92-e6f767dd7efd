import { Context, Next } from 'hono'
import { verifyToken, extractTokenFromHeader, getUserById, type JWTPayload } from '../lib/auth'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

// Middleware xác thực JWT
export const authMiddleware = async (c: Context<{ Bindings: Bindings }>, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization')
    const token = extractTokenFromHeader(authHeader)

    if (!token) {
      return c.json({
        success: false,
        error: 'Unauthorized - Missing or invalid token'
      }, 401)
    }

    // Verify JWT token
    const payload = await verifyToken(token)
    if (!payload) {
      return c.json({
        success: false,
        error: 'Unauthorized - Invalid or expired token'
      }, 401)
    }

    // Get user from database to ensure they still exist and are active
    const user = await getUserById(c, payload.userId)
    if (!user) {
      return c.json({
        success: false,
        error: 'Unauthorized - User not found or inactive'
      }, 401)
    }

    // Store user info in context for use in route handlers
    c.set('user', user)
    c.set('jwtPayload', payload)

    await next()
  } catch (error) {
    console.error('Auth middleware error:', error)
    return c.json({
      success: false,
      error: 'Unauthorized - Authentication failed'
    }, 401)
  }
}

// Optional middleware - không require authentication nhưng set user nếu có token
export const optionalAuthMiddleware = async (c: Context<{ Bindings: Bindings }>, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization')
    const token = extractTokenFromHeader(authHeader)

    if (token) {
      const payload = await verifyToken(token)
      if (payload) {
        const user = await getUserById(c, payload.userId)
        if (user) {
          c.set('user', user)
          c.set('jwtPayload', payload)
        }
      }
    }

    await next()
  } catch (error) {
    // Không throw error, chỉ log và continue
    console.error('Optional auth middleware error:', error)
    await next()
  }
}

// Middleware kiểm tra role admin
export const requireAdmin = async (c: Context<{ Bindings: Bindings }>, next: Next) => {
  const user = c.get('user')

  if (!user || user.role !== 'admin') {
    return c.json({
      success: false,
      error: 'Forbidden - Admin access required'
    }, 403)
  }

  await next()
}

// Middleware ghi log hoạt động
export const activityLogger = async (c: Context<{ Bindings: Bindings }>, next: Next) => {
  const startTime = Date.now()

  await next()

  const endTime = Date.now()
  const duration = endTime - startTime

  // Get user info if available
  const user = c.get('user')
  const userInfo = user ? `${user.username} (${user.email})` : 'Anonymous'

  // Log request info
  console.log(`${c.req.method} ${c.req.url} - ${c.res.status} - ${duration}ms - User: ${userInfo}`)

  // Save to activity_history table for non-GET requests
  if (c.req.method !== 'GET' && user) {
    try {
      const url = new URL(c.req.url)
      const action = getActionFromMethod(c.req.method)
      const description = `${action} ${url.pathname}`

      await c.env.DB.prepare(
        'INSERT INTO activity_history (item_type, item_id, action, description, user_name, created_at) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)'
      ).bind('system', 0, action, description, user.username).run()
    } catch (error) {
      console.error('Error logging activity:', error)
    }
  }
}

// Helper function để map HTTP method thành action
function getActionFromMethod(method: string): string {
  switch (method.toUpperCase()) {
    case 'POST': return 'create'
    case 'PUT': return 'update'
    case 'DELETE': return 'delete'
    default: return 'action'
  }
}

// Middleware validation cho request body
export const validateRequest = (schema: any) => {
  return async (c: Context, next: Next) => {
    try {
      if (c.req.method === 'POST' || c.req.method === 'PUT') {
        const body = await c.req.json()
        // TODO: Implement validation with Zod
        // const validatedData = schema.parse(body)
        // c.set('validatedData', validatedData)
      }
      await next()
    } catch (error) {
      return c.json({
        success: false,
        error: 'Invalid request data',
        details: error instanceof Error ? error.message : 'Unknown validation error'
      }, 400)
    }
  }
}
