import { Hono } from 'hono'
import { jwt } from 'hono/jwt'
import { 
  hashPassword, 
  verifyPassword, 
  generateAccessToken, 
  generateRefreshToken,
  verifyToken,
  extractTokenFromHeader,
  getUserByEmail,
  getUserById,
  updateLastLogin,
  storeRefreshToken,
  removeRefreshToken,
  cleanExpiredTokens,
  hashToken,
  type User,
  type JWTPayload
} from '../lib/auth'

type Bindings = {
  DB: D1Database
}

const auth = new Hono<{ Bindings: Bindings }>()

// JWT Secret - trong production nên dùng environment variable
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production'

/**
 * POST /api/auth/login
 * Đăng nhập user
 */
auth.post('/login', async (c) => {
  try {
    const { email, password } = await c.req.json()

    // Validate input
    if (!email || !password) {
      return c.json({ error: 'Email và mật khẩu là bắt buộc' }, 400)
    }

    // Get user from database
    const user = await getUserByEmail(c, email)
    if (!user) {
      return c.json({ error: 'Em<PERSON> hoặc mật khẩu không đúng' }, 401)
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.password_hash)
    if (!isValidPassword) {
      return c.json({ error: 'Email hoặc mật khẩu không đúng' }, 401)
    }

    // Generate tokens
    const accessToken = await generateAccessToken(user)
    const refreshToken = await generateRefreshToken(user)

    // Store refresh token in database
    const refreshTokenHash = hashToken(refreshToken)
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    await storeRefreshToken(c, user.id, refreshTokenHash, expiresAt)

    // Update last login
    await updateLastLogin(c, user.id)

    // Clean expired tokens
    await cleanExpiredTokens(c)

    // Return user info (without password) and tokens
    const { password_hash, ...userWithoutPassword } = user
    
    return c.json({
      user: userWithoutPassword,
      accessToken,
      refreshToken,
      message: 'Đăng nhập thành công'
    })

  } catch (error) {
    console.error('Login error:', error)
    return c.json({ error: 'Lỗi server nội bộ' }, 500)
  }
})

/**
 * POST /api/auth/logout
 * Đăng xuất user
 */
auth.post('/logout', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')
    const token = extractTokenFromHeader(authHeader)

    if (token) {
      // Remove refresh token from database if provided
      const refreshTokenHash = hashToken(token)
      await removeRefreshToken(c, refreshTokenHash)
    }

    return c.json({ message: 'Đăng xuất thành công' })

  } catch (error) {
    console.error('Logout error:', error)
    return c.json({ error: 'Lỗi server nội bộ' }, 500)
  }
})

/**
 * GET /api/auth/me
 * Lấy thông tin user hiện tại
 */
auth.get('/me', jwt({ secret: JWT_SECRET }), async (c) => {
  try {
    const payload = c.get('jwtPayload') as JWTPayload
    
    // Get fresh user data from database
    const user = await getUserById(c, payload.userId)
    if (!user) {
      return c.json({ error: 'User không tồn tại' }, 404)
    }

    // Return user info (without password)
    const { password_hash, ...userWithoutPassword } = user
    
    return c.json({ user: userWithoutPassword })

  } catch (error) {
    console.error('Get me error:', error)
    return c.json({ error: 'Lỗi server nội bộ' }, 500)
  }
})

/**
 * POST /api/auth/refresh
 * Refresh access token
 */
auth.post('/refresh', async (c) => {
  try {
    const { refreshToken } = await c.req.json()

    if (!refreshToken) {
      return c.json({ error: 'Refresh token là bắt buộc' }, 400)
    }

    // Verify refresh token
    const payload = await verifyToken(refreshToken)
    if (!payload) {
      return c.json({ error: 'Refresh token không hợp lệ' }, 401)
    }

    // Check if refresh token exists in database
    const refreshTokenHash = hashToken(refreshToken)
    const session = await c.env.DB.prepare(
      'SELECT * FROM user_sessions WHERE token_hash = ? AND expires_at > CURRENT_TIMESTAMP'
    ).bind(refreshTokenHash).first()

    if (!session) {
      return c.json({ error: 'Refresh token đã hết hạn hoặc không tồn tại' }, 401)
    }

    // Get user from database
    const user = await getUserById(c, payload.userId)
    if (!user) {
      return c.json({ error: 'User không tồn tại' }, 404)
    }

    // Generate new access token
    const newAccessToken = await generateAccessToken(user)

    return c.json({
      accessToken: newAccessToken,
      message: 'Token đã được refresh thành công'
    })

  } catch (error) {
    console.error('Refresh token error:', error)
    return c.json({ error: 'Lỗi server nội bộ' }, 500)
  }
})

/**
 * POST /api/auth/change-password
 * Đổi mật khẩu (yêu cầu đăng nhập)
 */
auth.post('/change-password', jwt({ secret: JWT_SECRET }), async (c) => {
  try {
    const payload = c.get('jwtPayload') as JWTPayload
    const { currentPassword, newPassword } = await c.req.json()

    // Validate input
    if (!currentPassword || !newPassword) {
      return c.json({ error: 'Mật khẩu hiện tại và mật khẩu mới là bắt buộc' }, 400)
    }

    if (newPassword.length < 6) {
      return c.json({ error: 'Mật khẩu mới phải có ít nhất 6 ký tự' }, 400)
    }

    // Get user from database
    const user = await getUserById(c, payload.userId)
    if (!user) {
      return c.json({ error: 'User không tồn tại' }, 404)
    }

    // Verify current password
    const isValidPassword = await verifyPassword(currentPassword, user.password_hash)
    if (!isValidPassword) {
      return c.json({ error: 'Mật khẩu hiện tại không đúng' }, 401)
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword)

    // Update password in database
    await c.env.DB.prepare(
      'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?'
    ).bind(newPasswordHash, user.id).run()

    return c.json({ message: 'Đổi mật khẩu thành công' })

  } catch (error) {
    console.error('Change password error:', error)
    return c.json({ error: 'Lỗi server nội bộ' }, 500)
  }
})

export default auth
