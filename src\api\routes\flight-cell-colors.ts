import { Hono } from 'hono'
import { authMiddleware } from '../middleware/auth'
import { z } from 'zod/v4'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// Apply auth middleware to all routes
app.use('*', authMiddleware)

// Validation schemas
const colorValueSchema = z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Color must be a valid hex code (e.g., #ff0000)')

const updateCellColorSchema = z.object({
  color_value: colorValueSchema
})

const batchUpdateColorsSchema = z.object({
  colors: z.array(z.object({
    flight_id: z.string().min(1),
    field_name: z.string().min(1),
    color_value: colorValueSchema
  }))
})

// GET /api/flight-cell-colors/:flightId - Get cell colors for a specific flight
app.get('/:flightId', async (c) => {
  try {
    const flightId = c.req.param('flightId')
    
    if (!flightId) {
      return c.json({
        success: false,
        error: 'Flight ID is required'
      }, 400)
    }

    // Get all cell colors for the flight
    const colors = await c.env.DB.prepare(`
      SELECT field_name, color_value, created_at, created_by
      FROM flight_cell_colors 
      WHERE flight_id = ?
      ORDER BY field_name
    `).bind(flightId).all()

    if (!colors.success) {
      throw new Error('Failed to fetch cell colors')
    }

    // Transform to object format for easier frontend usage
    const colorMap: Record<string, string> = {}
    colors.results.forEach((row: any) => {
      colorMap[row.field_name] = row.color_value
    })

    return c.json({
      success: true,
      data: {
        flight_id: flightId,
        colors: colorMap,
        details: colors.results
      }
    })

  } catch (error) {
    console.error('Error fetching cell colors:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy màu ô'
    }, 500)
  }
})

// GET /api/flight-cell-colors/batch?flightIds=id1,id2,id3 - Batch get colors for multiple flights
app.get('/batch', async (c) => {
  try {
    const flightIdsParam = c.req.query('flightIds')
    
    if (!flightIdsParam) {
      return c.json({
        success: false,
        error: 'flightIds parameter is required'
      }, 400)
    }

    const flightIds = flightIdsParam.split(',').filter(id => id.trim())
    
    if (flightIds.length === 0) {
      return c.json({
        success: true,
        data: {}
      })
    }

    // Create placeholders for IN clause
    const placeholders = flightIds.map(() => '?').join(',')
    
    const colors = await c.env.DB.prepare(`
      SELECT flight_id, field_name, color_value
      FROM flight_cell_colors 
      WHERE flight_id IN (${placeholders})
      ORDER BY flight_id, field_name
    `).bind(...flightIds).all()

    if (!colors.success) {
      throw new Error('Failed to fetch cell colors')
    }

    // Group by flight_id
    const colorsByFlight: Record<string, Record<string, string>> = {}
    colors.results.forEach((row: any) => {
      if (!colorsByFlight[row.flight_id]) {
        colorsByFlight[row.flight_id] = {}
      }
      colorsByFlight[row.flight_id][row.field_name] = row.color_value
    })

    return c.json({
      success: true,
      data: colorsByFlight
    })

  } catch (error) {
    console.error('Error fetching batch cell colors:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy màu ô hàng loạt'
    }, 500)
  }
})

// PUT /api/flight-cell-colors/:flightId/:fieldName - Update cell color
app.put('/:flightId/:fieldName', async (c) => {
  try {
    const flightId = c.req.param('flightId')
    const fieldName = c.req.param('fieldName')
    const body = await c.req.json()
    const user = c.get('user')

    // Validate request body
    const validationResult = updateCellColorSchema.safeParse(body)
    if (!validationResult.success) {
      return c.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: validationResult.error.errors
      }, 400)
    }

    const { color_value } = validationResult.data

    // Check if flight exists
    const flight = await c.env.DB.prepare(`
      SELECT id FROM flights WHERE id = ?
    `).bind(flightId).first()

    if (!flight) {
      return c.json({
        success: false,
        error: 'Chuyến bay không tồn tại'
      }, 404)
    }

    // Upsert cell color (insert or update if exists)
    const result = await c.env.DB.prepare(`
      INSERT INTO flight_cell_colors (flight_id, field_name, color_value, created_by)
      VALUES (?, ?, ?, ?)
      ON CONFLICT(flight_id, field_name) 
      DO UPDATE SET 
        color_value = excluded.color_value,
        updated_at = CURRENT_TIMESTAMP
    `).bind(flightId, fieldName, color_value, user?.username || 'system').run()

    if (!result.success) {
      throw new Error('Failed to update cell color')
    }

    return c.json({
      success: true,
      data: {
        flight_id: flightId,
        field_name: fieldName,
        color_value: color_value
      }
    })

  } catch (error) {
    console.error('Error updating cell color:', error)
    return c.json({
      success: false,
      error: 'Không thể cập nhật màu ô'
    }, 500)
  }
})

// DELETE /api/flight-cell-colors/:flightId/:fieldName - Remove cell color
app.delete('/:flightId/:fieldName', async (c) => {
  try {
    const flightId = c.req.param('flightId')
    const fieldName = c.req.param('fieldName')

    const result = await c.env.DB.prepare(`
      DELETE FROM flight_cell_colors 
      WHERE flight_id = ? AND field_name = ?
    `).bind(flightId, fieldName).run()

    if (!result.success) {
      throw new Error('Failed to delete cell color')
    }

    return c.json({
      success: true,
      data: {
        flight_id: flightId,
        field_name: fieldName,
        deleted: result.changes > 0
      }
    })

  } catch (error) {
    console.error('Error deleting cell color:', error)
    return c.json({
      success: false,
      error: 'Không thể xóa màu ô'
    }, 500)
  }
})

// POST /api/flight-cell-colors/batch - Batch update colors
app.post('/batch', async (c) => {
  try {
    const body = await c.req.json()
    const user = c.get('user')

    // Validate request body
    const validationResult = batchUpdateColorsSchema.safeParse(body)
    if (!validationResult.success) {
      return c.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: validationResult.error.errors
      }, 400)
    }

    const { colors } = validationResult.data

    if (colors.length === 0) {
      return c.json({
        success: true,
        data: { updated: 0 }
      })
    }

    // Batch upsert using transaction-like approach
    let successCount = 0
    const errors: any[] = []

    for (const colorData of colors) {
      try {
        const result = await c.env.DB.prepare(`
          INSERT INTO flight_cell_colors (flight_id, field_name, color_value, created_by)
          VALUES (?, ?, ?, ?)
          ON CONFLICT(flight_id, field_name) 
          DO UPDATE SET 
            color_value = excluded.color_value,
            updated_at = CURRENT_TIMESTAMP
        `).bind(
          colorData.flight_id,
          colorData.field_name,
          colorData.color_value,
          user?.username || 'system'
        ).run()

        if (result.success) {
          successCount++
        } else {
          errors.push({
            flight_id: colorData.flight_id,
            field_name: colorData.field_name,
            error: 'Database operation failed'
          })
        }
      } catch (error) {
        errors.push({
          flight_id: colorData.flight_id,
          field_name: colorData.field_name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return c.json({
      success: errors.length === 0,
      data: {
        total: colors.length,
        updated: successCount,
        errors: errors.length > 0 ? errors : undefined
      }
    })

  } catch (error) {
    console.error('Error batch updating cell colors:', error)
    return c.json({
      success: false,
      error: 'Không thể cập nhật màu ô hàng loạt'
    }, 500)
  }
})

export default app
