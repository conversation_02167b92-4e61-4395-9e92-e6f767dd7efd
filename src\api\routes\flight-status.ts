import { Hono } from 'hono';
import { z } from 'zod';
import { authMiddleware } from '../middleware/auth';

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// Apply auth middleware to all routes
app.use('*', authMiddleware)

// Schema for status update request
const StatusUpdateSchema = z.object({
  field: z.enum(['arr_present', 'arr_finished', 'dep_present', 'dep_boarded', 'dep_finished']),
  value: z.boolean()
});

// Update flight status
app.patch('/:id/status', async (c) => {
  try {
    const flightId = c.req.param('id');
    const body = await c.req.json();

    // Validate request body
    const validation = StatusUpdateSchema.safeParse(body);
    if (!validation.success) {
      return c.json({ error: 'Invalid request body', details: validation.error.errors }, 400);
    }

    const { field, value } = validation.data;

    // Get database from context
    const db = c.env.DB;
    
    // Update the specific status field
    const result = await db.prepare(`
      UPDATE flights 
      SET ${field} = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).bind(value, flightId).run();

    if (result.changes === 0) {
      return c.json({ error: 'Flight not found' }, 404);
    }

    // Get updated flight data
    const updatedFlight = await db.prepare(`
      SELECT * FROM flights WHERE id = ?
    `).bind(flightId).first();

    return c.json({
      success: true,
      message: 'Status updated successfully',
      flight: updatedFlight
    });

  } catch (error) {
    console.error('Error updating flight status:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Get flight status
app.get('/:id/status', async (c) => {
  try {
    const flightId = c.req.param('id');
    const db = c.env.DB;

    const flight = await db.prepare(`
      SELECT 
        id,
        arr_present,
        arr_finished,
        dep_present,
        dep_boarded,
        dep_finished,
        updated_at
      FROM flights 
      WHERE id = ?
    `).bind(flightId).first();

    if (!flight) {
      return c.json({ error: 'Flight not found' }, 404);
    }

    return c.json({
      success: true,
      status: {
        arr_present: Boolean(flight.arr_present),
        arr_finished: Boolean(flight.arr_finished),
        dep_present: Boolean(flight.dep_present),
        dep_boarded: Boolean(flight.dep_boarded),
        dep_finished: Boolean(flight.dep_finished),
        updated_at: flight.updated_at
      }
    });

  } catch (error) {
    console.error('Error getting flight status:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Schema for batch update
const BatchUpdateSchema = z.object({
  updates: z.array(z.object({
    id: z.string(),
    field: z.enum(['arr_present', 'arr_finished', 'dep_present', 'dep_boarded', 'dep_finished']),
    value: z.boolean()
  }))
});

// Batch update multiple flight statuses
app.patch('/batch-status', async (c) => {
  try {
    const body = await c.req.json();

    // Validate request body
    const validation = BatchUpdateSchema.safeParse(body);
    if (!validation.success) {
      return c.json({ error: 'Invalid request body', details: validation.error.errors }, 400);
    }

    const { updates } = validation.data;
    const db = c.env.DB;

    // Process updates in a transaction
    const results = [];
    
    for (const update of updates) {
      const result = await db.prepare(`
        UPDATE flights 
        SET ${update.field} = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `).bind(update.value, update.id).run();
      
      results.push({
        id: update.id,
        field: update.field,
        success: result.changes > 0
      });
    }

    return c.json({
      success: true,
      message: 'Batch status update completed',
      results
    });

  } catch (error) {
    console.error('Error in batch status update:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

export default app;
