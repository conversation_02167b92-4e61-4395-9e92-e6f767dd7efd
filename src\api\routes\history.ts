import { Hono } from 'hono'
import { authMiddleware } from '../middleware/auth'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// Apply auth middleware to all routes
app.use('*', authMiddleware)

// GET /api/history - <PERSON><PERSON><PERSON>ch sử hoạt động
app.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '50')
    const offset = (page - 1) * limit
    
    const type = c.req.query('type') // 'walkie_talkie' hoặc 'access_card'
    const action = c.req.query('action') // 'create', 'update', 'delete', 'assign', 'return'

    let query = `
      SELECT
        ah.*,
        CASE
          WHEN ah.item_type = 'walkie_talkie' THEN wt.name
          WHEN ah.item_type = 'access_card' THEN ac.name
          ELSE NULL
        END as device_name,
        u.full_name as issuer_name
      FROM activity_history ah
      LEFT JOIN walkie_talkies wt ON ah.item_type = 'walkie_talkie' AND ah.item_id = wt.id
      LEFT JOIN access_cards ac ON ah.item_type = 'access_card' AND ah.item_id = ac.id
      LEFT JOIN users u ON ah.user_id = u.username
      WHERE 1=1
    `
    const params: any[] = []

    if (type) {
      query += ` AND item_type = ?`
      params.push(type)
    }

    if (action) {
      query += ` AND action = ?`
      params.push(action)
    }

    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`
    params.push(limit, offset)

    const { results } = await c.env.DB.prepare(query).bind(...params).all()

    // Đếm tổng số records
    let countQuery = `SELECT COUNT(*) as total FROM activity_history WHERE 1=1`
    const countParams: any[] = []

    if (type) {
      countQuery += ` AND item_type = ?`
      countParams.push(type)
    }

    if (action) {
      countQuery += ` AND action = ?`
      countParams.push(action)
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first()
    const total = countResult?.total || 0

    return c.json({
      success: true,
      data: results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching history:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy lịch sử hoạt động'
    }, 500)
  }
})

// GET /api/history/:id - Lấy chi tiết một bản ghi lịch sử
app.get('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const result = await c.env.DB.prepare(`
      SELECT * FROM activity_history WHERE id = ?
    `).bind(id).first()

    if (!result) {
      return c.json({
        success: false,
        error: 'Không tìm thấy bản ghi lịch sử'
      }, 404)
    }

    return c.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching history record:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy thông tin lịch sử'
    }, 500)
  }
})

// POST /api/history - Tạo bản ghi lịch sử mới
app.post('/', async (c) => {
  try {
    const body = await c.req.json()
    const { item_type, item_id, action, description, user_id, borrower_name, old_values, new_values } = body

    // Validation
    if (!item_type || !item_id || !action) {
      return c.json({
        success: false,
        error: 'Loại thiết bị, ID thiết bị và hành động là bắt buộc'
      }, 400)
    }

    const result = await c.env.DB.prepare(`
      INSERT INTO activity_history (item_type, item_id, action, description, user_id, borrower_name, old_values, new_values, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `).bind(
      item_type,
      item_id,
      action,
      description || null,
      user_id || null,
      borrower_name || null,
      old_values ? JSON.stringify(old_values) : null,
      new_values ? JSON.stringify(new_values) : null
    ).run()

    return c.json({
      success: true,
      data: { id: result.meta.last_row_id, ...body },
      message: 'Tạo bản ghi lịch sử thành công'
    }, 201)
  } catch (error) {
    console.error('Error creating history record:', error)
    return c.json({
      success: false,
      error: 'Không thể tạo bản ghi lịch sử'
    }, 500)
  }
})

// GET /api/history/item/:type/:id - Lấy lịch sử của một thiết bị cụ thể
app.get('/item/:type/:id', async (c) => {
  try {
    const itemType = c.req.param('type')
    const itemId = c.req.param('id')

    const { results } = await c.env.DB.prepare(`
      SELECT
        ah.*,
        CASE
          WHEN ah.item_type = 'walkie_talkie' THEN wt.name
          WHEN ah.item_type = 'access_card' THEN ac.name
          ELSE NULL
        END as device_name,
        u.full_name as issuer_name
      FROM activity_history ah
      LEFT JOIN walkie_talkies wt ON ah.item_type = 'walkie_talkie' AND ah.item_id = wt.id
      LEFT JOIN access_cards ac ON ah.item_type = 'access_card' AND ah.item_id = ac.id
      LEFT JOIN users u ON ah.user_id = u.username
      WHERE ah.item_type = ? AND ah.item_id = ?
      ORDER BY ah.created_at DESC
    `).bind(itemType, itemId).all()

    return c.json({
      success: true,
      data: results,
      total: results.length
    })
  } catch (error) {
    console.error('Error fetching item history:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy lịch sử thiết bị'
    }, 500)
  }
})

// GET /api/history/user/:userId - Lấy lịch sử hoạt động của một người dùng
app.get('/user/:userId', async (c) => {
  try {
    const userId = c.req.param('userId')
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '20')
    const offset = (page - 1) * limit

    const { results } = await c.env.DB.prepare(`
      SELECT
        ah.*,
        CASE
          WHEN ah.item_type = 'walkie_talkie' THEN wt.name
          WHEN ah.item_type = 'access_card' THEN ac.name
          ELSE NULL
        END as device_name
      FROM activity_history ah
      LEFT JOIN walkie_talkies wt ON ah.item_type = 'walkie_talkie' AND ah.item_id = wt.id
      LEFT JOIN access_cards ac ON ah.item_type = 'access_card' AND ah.item_id = ac.id
      WHERE ah.user_id = ?
      ORDER BY ah.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(userId, limit, offset).all()

    const countResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as total FROM activity_history WHERE user_id = ?
    `).bind(userId).first()
    const total = countResult?.total || 0

    return c.json({
      success: true,
      data: results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching user history:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy lịch sử người dùng'
    }, 500)
  }
})

export default app
