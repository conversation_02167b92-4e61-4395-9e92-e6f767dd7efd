import { Hono } from 'hono'
import { optionalAuthMiddleware } from '../middleware/auth'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// Apply optional auth middleware - không require authentication nhưng set user nếu có token
app.use('*', optionalAuthMiddleware)

// GET /api/public/flights - L<PERSON>y danh sách lịch bay cho public view (không cần authentication)
app.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '2000') // Increase default limit to show all flights (up to 2000)
    const offset = (page - 1) * limit
    
    // Query parameters for filtering
    const date = c.req.query('date') // YYYY-MM-DD
    const search = c.req.query('search') // Search in flight numbers and staff
    const type = c.req.query('type') // 'arrival' or 'departure'
    
    let whereClause = ''
    const bindings: any[] = []
    
    // Build WHERE clause based on filters
    const conditions: string[] = []
    
    if (date) {
      conditions.push('date = ?')
      bindings.push(date)
    }
    
    if (search) {
      conditions.push(`(
        arr_flt LIKE ? OR dep_flt LIKE ? OR 
        arr_staff LIKE ? OR dep_staff LIKE ? OR
        arr_reg LIKE ? OR dep_reg LIKE ?
      )`)
      const searchPattern = `%${search}%`
      bindings.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
    }
    
    if (type === 'arrival') {
      conditions.push('arr_flt IS NOT NULL AND arr_flt != ""')
    } else if (type === 'departure') {
      conditions.push('dep_flt IS NOT NULL AND dep_flt != ""')
    }
    
    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ')
    }
    
    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM flights 
      ${whereClause}
    `
    
    const countResult = await c.env.DB.prepare(countQuery).bind(...bindings).first()
    const total = countResult?.total || 0
    
    // Get flights data - only return public-safe fields
    const dataQuery = `
      SELECT 
        id,
        date,
        stt,
        arr_flt,
        arr_from,
        arr_reg,
        arr_time,
        arr_staff,
        dep_flt,
        dep_to,
        dep_reg,
        dep_time,
        dep_staff,
        remark,
        created_at,
        updated_at
      FROM flights 
      ${whereClause}
      ORDER BY date DESC, stt ASC
      LIMIT ? OFFSET ?
    `
    
    const { results } = await c.env.DB.prepare(dataQuery)
      .bind(...bindings, limit, offset)
      .all()

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit)
    
    return c.json({
      success: true,
      data: results || [],
      total,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Error fetching public flights:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy danh sách lịch bay'
    }, 500)
  }
})

// GET /api/public/flights/:id - Lấy chi tiết chuyến bay cho public view
app.get('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    
    const flight = await c.env.DB.prepare(`
      SELECT 
        id,
        date,
        stt,
        arr_flt,
        arr_from,
        arr_reg,
        arr_time,
        arr_staff,
        dep_flt,
        dep_to,
        dep_reg,
        dep_time,
        dep_staff,
        remark,
        created_at,
        updated_at
      FROM flights 
      WHERE id = ?
    `).bind(id).first()

    if (!flight) {
      return c.json({
        success: false,
        error: 'Không tìm thấy chuyến bay'
      }, 404)
    }

    return c.json({
      success: true,
      data: flight
    })
  } catch (error) {
    console.error('Error fetching public flight detail:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy thông tin chuyến bay'
    }, 500)
  }
})

// GET /api/public/flights/stats - Lấy thống kê lịch bay cho public view
app.get('/stats', async (c) => {
  try {
    const date = c.req.query('date') || new Date().toISOString().split('T')[0]
    
    // Get basic stats for the date
    const stats = await c.env.DB.prepare(`
      SELECT 
        COUNT(*) as total_flights,
        COUNT(CASE WHEN arr_flt IS NOT NULL AND arr_flt != '' THEN 1 END) as total_arrivals,
        COUNT(CASE WHEN dep_flt IS NOT NULL AND dep_flt != '' THEN 1 END) as total_departures,
        COUNT(CASE WHEN arr_staff IS NOT NULL AND arr_staff != '' THEN 1 END) as assigned_arrivals,
        COUNT(CASE WHEN dep_staff IS NOT NULL AND dep_staff != '' THEN 1 END) as assigned_departures
      FROM flights 
      WHERE date = ?
    `).bind(date).first()

    return c.json({
      success: true,
      data: {
        date,
        total_flights: stats?.total_flights || 0,
        total_arrivals: stats?.total_arrivals || 0,
        total_departures: stats?.total_departures || 0,
        assigned_arrivals: stats?.assigned_arrivals || 0,
        assigned_departures: stats?.assigned_departures || 0,
        unassigned_arrivals: (stats?.total_arrivals || 0) - (stats?.assigned_arrivals || 0),
        unassigned_departures: (stats?.total_departures || 0) - (stats?.assigned_departures || 0)
      }
    })
  } catch (error) {
    console.error('Error fetching public flight stats:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy thống kê lịch bay'
    }, 500)
  }
})

export default app
