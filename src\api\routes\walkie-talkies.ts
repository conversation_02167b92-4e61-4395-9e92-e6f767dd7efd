import { Hono } from 'hono'
import { authMiddleware } from '../middleware/auth'

type Bindings = {
  DB: D1Database
  NODE_ENV: string
}

const app = new Hono<{ Bindings: Bindings }>()

// Apply auth middleware to all routes
app.use('*', authMiddleware)

// GET /api/walkie-talkies - L<PERSON>y danh sách tất cả bộ đàm
app.get('/', async (c) => {
  try {
    const { results } = await c.env.DB.prepare(`
      SELECT * FROM walkie_talkies 
      ORDER BY created_at DESC
    `).all()

    return c.json({
      success: true,
      data: results,
      total: results.length
    })
  } catch (error) {
    console.error('Error fetching walkie-talkies:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy danh sách bộ đàm'
    }, 500)
  }
})

// GET /api/walkie-talkies/:id - <PERSON><PERSON><PERSON> thông tin chi tiết một bộ đàm
app.get('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const result = await c.env.DB.prepare(`
      SELECT * FROM walkie_talkies WHERE id = ?
    `).bind(id).first()

    if (!result) {
      return c.json({
        success: false,
        error: 'Không tìm thấy bộ đàm'
      }, 404)
    }

    return c.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Error fetching walkie-talkie:', error)
    return c.json({
      success: false,
      error: 'Không thể lấy thông tin bộ đàm'
    }, 500)
  }
})

// POST /api/walkie-talkies - Tạo bộ đàm mới
app.post('/', async (c) => {
  try {
    const body = await c.req.json()
    const { name, status, assigned_to } = body

    // Validation
    if (!name) {
      return c.json({
        success: false,
        error: 'Tên bộ đàm là bắt buộc'
      }, 400)
    }

    const result = await c.env.DB.prepare(`
      INSERT INTO walkie_talkies (name, status, assigned_to, created_at, updated_at)
      VALUES (?, ?, ?, datetime('now'), datetime('now'))
    `).bind(name, status || 'available', assigned_to || null).run()

    return c.json({
      success: true,
      data: { id: result.meta.last_row_id, name, status: status || 'available', assigned_to: assigned_to || null },
      message: 'Tạo bộ đàm thành công'
    }, 201)
  } catch (error) {
    console.error('Error creating walkie-talkie:', error)
    return c.json({
      success: false,
      error: 'Không thể tạo bộ đàm mới'
    }, 500)
  }
})

// PUT /api/walkie-talkies/:id - Cập nhật thông tin bộ đàm
app.put('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    const body = await c.req.json()
    const { name, status, assigned_to } = body

    // Check if record exists first
    const existingRecord = await c.env.DB.prepare(`
      SELECT * FROM walkie_talkies WHERE id = ?
    `).bind(id).first()

    if (!existingRecord) {
      return c.json({
        success: false,
        error: 'Không tìm thấy bộ đàm để cập nhật'
      }, 404)
    }

    // Build dynamic update query based on provided fields
    const updateFields = []
    const updateValues = []

    if (name !== undefined) {
      if (!name.trim()) {
        return c.json({
          success: false,
          error: 'Tên bộ đàm không được để trống'
        }, 400)
      }
      updateFields.push('name = ?')
      updateValues.push(name)
    }

    if (status !== undefined) {
      updateFields.push('status = ?')
      updateValues.push(status)
    }

    if (assigned_to !== undefined) {
      updateFields.push('assigned_to = ?')
      updateValues.push(assigned_to)
    }

    // Always update the updated_at field
    updateFields.push('updated_at = datetime(\'now\')')
    updateValues.push(id) // Add id for WHERE clause

    if (updateFields.length === 1) { // Only updated_at field
      return c.json({
        success: false,
        error: 'Không có trường nào để cập nhật'
      }, 400)
    }

    const query = `UPDATE walkie_talkies SET ${updateFields.join(', ')} WHERE id = ?`
    const result = await c.env.DB.prepare(query).bind(...updateValues).run()

    return c.json({
      success: true,
      message: 'Cập nhật bộ đàm thành công'
    })
  } catch (error) {
    console.error('Error updating walkie-talkie:', error)
    return c.json({
      success: false,
      error: 'Không thể cập nhật bộ đàm'
    }, 500)
  }
})

// DELETE /api/walkie-talkies/:id - Xóa bộ đàm
app.delete('/:id', async (c) => {
  try {
    const id = c.req.param('id')
    
    const result = await c.env.DB.prepare(`
      DELETE FROM walkie_talkies WHERE id = ?
    `).bind(id).run()

    if (result.changes === 0) {
      return c.json({
        success: false,
        error: 'Không tìm thấy bộ đàm để xóa'
      }, 404)
    }

    return c.json({
      success: true,
      message: 'Xóa bộ đàm thành công'
    })
  } catch (error) {
    console.error('Error deleting walkie-talkie:', error)
    return c.json({
      success: false,
      error: 'Không thể xóa bộ đàm'
    }, 500)
  }
})

export default app
