
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CreditCard, Plus, UserCheck, UserX, Search, Loader2, Trash2, Settings } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "@tanstack/react-form";
import { addDeviceSchema, borrowDeviceSchema, type AddDeviceFormData, type BorrowDeviceFormData } from "@/lib/schemas";
import { useAccessCards, useCreateAccessCard, useUpdateAccessCard, useDeleteAccessCard, useCreateActivityRecord } from "@/hooks/api";
import type { AccessCard } from "@/lib/api";
import { useAuth } from "@/contexts/AuthContext";

export const AccessCardManager = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBorrowDialogOpen, setIsBorrowDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isUpdateStatusDialogOpen, setIsUpdateStatusDialogOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState<AccessCard | null>(null);
  const [cardToDelete, setCardToDelete] = useState<AccessCard | null>(null);
  const [cardToUpdate, setCardToUpdate] = useState<AccessCard | null>(null);
  const [newStatus, setNewStatus] = useState<AccessCard['status']>('active');
  const { toast } = useToast();
  const { user } = useAuth();

  // API hooks
  const { data: accessCards = [], isLoading, error } = useAccessCards();
  const createAccessCard = useCreateAccessCard();
  const updateAccessCard = useUpdateAccessCard();
  const deleteAccessCard = useDeleteAccessCard();
  const createActivityRecord = useCreateActivityRecord();

  // TanStack Form cho thêm thẻ từ
  const addCardForm = useForm({
    defaultValues: {
      name: '',
    } as AddDeviceFormData,
    validators: {
      onChange: ({ value }) => {
        const result = addDeviceSchema.safeParse(value);
        if (!result.success) {
          // Convert Zod v4 errors to field errors format
          const fieldErrors: Record<string, string[]> = {};
          result.error.issues.forEach(issue => {
            const path = issue.path.join('.');
            if (!fieldErrors[path]) {
              fieldErrors[path] = [];
            }
            fieldErrors[path].push(issue.message);
          });
          return fieldErrors;
        }
        return undefined;
      },
    },
    onSubmit: async ({ value }) => {
      try {
        await createAccessCard.mutateAsync({
          name: value.name,
          status: 'active',
        });

        setIsAddDialogOpen(false);
        addCardForm.reset();
      } catch (error) {
        // Error handling is done in the hook
      }
    },
  });

  // TanStack Form cho cho mượn thẻ từ
  const borrowCardForm = useForm({
    defaultValues: {
      borrowerName: '',
    } as BorrowDeviceFormData,
    validators: {
      onChange: ({ value }) => {
        const result = borrowDeviceSchema.safeParse(value);
        if (!result.success) {
          // Convert Zod v4 errors to field errors format
          const fieldErrors: Record<string, string[]> = {};
          result.error.issues.forEach(issue => {
            const path = issue.path.join('.');
            if (!fieldErrors[path]) {
              fieldErrors[path] = [];
            }
            fieldErrors[path].push(issue.message);
          });
          return fieldErrors;
        }
        return undefined;
      },
    },
    onSubmit: async ({ value }) => {
      if (!selectedCard) return;

      try {
        await updateAccessCard.mutateAsync({
          id: selectedCard.id,
          data: {
            status: 'active',
            assigned_to: value.borrowerName,
          }
        });

        // Create activity record
        await createActivityRecord.mutateAsync({
          item_type: 'access_card',
          item_id: selectedCard.id,
          action: 'assign',
          description: `Giao thẻ từ ${selectedCard.name} cho ${value.borrowerName}`,
          user_id: user?.username || 'admin',
          borrower_name: value.borrowerName,
        });

        setIsBorrowDialogOpen(false);
        setSelectedCard(null);
        borrowCardForm.reset();
      } catch (error) {
        // Error handling is done in the hook
      }
    },
  });

  const filteredCards = accessCards.filter(card =>
    card.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (card.assigned_to && card.assigned_to.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const returnCard = async (card: AccessCard) => {
    try {
      await updateAccessCard.mutateAsync({
        id: card.id,
        data: {
          status: 'active',
          assigned_to: null,
        }
      });

      // Create activity record
      await createActivityRecord.mutateAsync({
        item_type: 'access_card',
        item_id: card.id,
        action: 'return',
        description: `Thu hồi thẻ từ ${card.name} từ ${card.assigned_to}`,
        user_id: user?.username || 'admin',
        borrower_name: card.assigned_to || undefined,
      });
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const openBorrowDialog = (card: AccessCard) => {
    setSelectedCard(card);
    borrowCardForm.reset(); // Reset form khi mở dialog
    setIsBorrowDialogOpen(true);
  };

  const openDeleteDialog = (card: AccessCard) => {
    setCardToDelete(card);
    setIsDeleteDialogOpen(true);
  };

  const openUpdateStatusDialog = (card: AccessCard) => {
    setCardToUpdate(card);
    setNewStatus(card.status);
    setIsUpdateStatusDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!cardToDelete) return;

    try {
      await deleteAccessCard.mutateAsync(cardToDelete.id);

      // Create activity record
      await createActivityRecord.mutateAsync({
        item_type: 'access_card',
        item_id: cardToDelete.id,
        action: 'delete',
        description: `Xóa thẻ từ ${cardToDelete.name}`,
        user_id: user?.username || 'admin',
      });

      setIsDeleteDialogOpen(false);
      setCardToDelete(null);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleUpdateStatus = async () => {
    if (!cardToUpdate) return;

    try {
      await updateAccessCard.mutateAsync({
        id: cardToUpdate.id,
        data: {
          status: newStatus,
          // Clear assigned_to if status is not active
          assigned_to: newStatus === 'active' ? cardToUpdate.assigned_to : null
        }
      });

      // Create activity record
      await createActivityRecord.mutateAsync({
        item_type: 'access_card',
        item_id: cardToUpdate.id,
        action: 'update',
        description: `Cập nhật trạng thái thẻ từ ${cardToUpdate.name} thành ${
          newStatus === 'active' ? 'Hoạt động' :
          newStatus === 'inactive' ? 'Vô hiệu' :
          newStatus === 'expired' ? 'Hết hạn' : 'Mất'
        }`,
        user_id: user?.username || 'admin',
      });

      setIsUpdateStatusDialogOpen(false);
      setCardToUpdate(null);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Đang tải dữ liệu thẻ từ...</span>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-500 mb-2">Lỗi khi tải dữ liệu thẻ từ</p>
            <p className="text-sm text-gray-500">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5 text-green-600" />
              <span>Quản lý thẻ từ</span>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-green-600 hover:bg-green-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm thẻ từ
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Thêm thẻ từ mới</DialogTitle>
                </DialogHeader>
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    addCardForm.handleSubmit()
                  }}
                  className="space-y-4"
                >
                  <addCardForm.Field
                    name="name"
                    children={(field) => (
                      <div>
                        <Label htmlFor="card-name">Tên thẻ từ</Label>
                        <Input
                          id="card-name"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                          placeholder="VD: Thẻ A-004"
                        />
                        {field.state.meta.errors.length > 0 && (
                          <p className="text-sm text-red-500 mt-1">
                            {field.state.meta.errors[0] as string}
                          </p>
                        )}
                      </div>
                    )}
                  />
                  <addCardForm.Subscribe
                    selector={(state) => [state.canSubmit, state.isSubmitting]}
                    children={([canSubmit, isSubmitting]) => (
                      <Button type="submit" disabled={!canSubmit || isSubmitting} className="w-full">
                        {isSubmitting ? 'Đang thêm...' : 'Thêm'}
                      </Button>
                    )}
                  />
                </form>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4 space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Tìm kiếm thẻ từ hoặc người mượn..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredCards.map((card) => (
              <Card key={card.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-gray-900">{card.name}</h3>
                    </div>
                    <Badge
                      variant={card.status === "active" && !card.assigned_to ? "default" : "destructive"}
                      className={card.status === "active" && !card.assigned_to ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}
                    >
                      {card.status === "active" && !card.assigned_to ? "Có sẵn" :
                       card.assigned_to ? "Đã giao" :
                       card.status === "inactive" ? "Vô hiệu" :
                       card.status === "expired" ? "Hết hạn" : "Mất"}
                    </Badge>
                  </div>
                  
                  {card.assigned_to && (
                    <div className="mb-3 text-sm text-gray-600">
                      <p><strong>Người được giao:</strong> {card.assigned_to}</p>
                    </div>
                  )}
                  
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      {card.status === "active" && !card.assigned_to ? (
                        <Button
                          onClick={() => openBorrowDialog(card)}
                          className="flex-1 bg-blue-600 hover:bg-blue-700"
                          size="sm"
                        >
                          <UserCheck className="h-4 w-4 mr-1" />
                          Giao thẻ
                        </Button>
                      ) : card.assigned_to ? (
                        <Button
                          onClick={() => returnCard(card)}
                          variant="outline"
                          className="flex-1"
                          size="sm"
                        >
                          <UserX className="h-4 w-4 mr-1" />
                          Thu hồi
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          className="flex-1"
                          size="sm"
                          disabled
                        >
                          {card.status === "inactive" ? "Vô hiệu" :
                           card.status === "expired" ? "Hết hạn" : "Không khả dụng"}
                        </Button>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        onClick={() => openUpdateStatusDialog(card)}
                        variant="outline"
                        className="flex-1"
                        size="sm"
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Cập nhật trạng thái
                      </Button>
                      <Button
                        onClick={() => openDeleteDialog(card)}
                        variant="destructive"
                        size="sm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Borrow Dialog */}
      <Dialog open={isBorrowDialogOpen} onOpenChange={setIsBorrowDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Giao thẻ từ {selectedCard?.name}</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              borrowCardForm.handleSubmit()
            }}
            className="space-y-4"
          >
            <borrowCardForm.Field
              name="borrowerName"
              children={(field) => (
                <div>
                  <Label htmlFor="borrower-name">Tên người được giao</Label>
                  <Input
                    id="borrower-name"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    placeholder="Nhập tên người được giao thẻ"
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="text-sm text-red-500 mt-1">
                      {field.state.meta.errors[0] as string}
                    </p>
                  )}
                </div>
              )}
            />
            <borrowCardForm.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || isSubmitting} className="w-full">
                  {isSubmitting ? 'Đang xử lý...' : 'Xác nhận giao thẻ'}
                </Button>
              )}
            />
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa thẻ từ</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Bạn có chắc chắn muốn xóa thẻ từ <strong>{cardToDelete?.name}</strong>?</p>
            <p className="text-sm text-gray-500">Hành động này không thể hoàn tác.</p>
            <div className="flex space-x-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Hủy
              </Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleteAccessCard.isPending}
              >
                {deleteAccessCard.isPending ? 'Đang xóa...' : 'Xóa'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Update Status Dialog */}
      <Dialog open={isUpdateStatusDialogOpen} onOpenChange={setIsUpdateStatusDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cập nhật trạng thái {cardToUpdate?.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status-select">Trạng thái mới</Label>
              <Select value={newStatus} onValueChange={(value: AccessCard['status']) => setNewStatus(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Hoạt động</SelectItem>
                  <SelectItem value="inactive">Vô hiệu</SelectItem>
                  <SelectItem value="expired">Hết hạn</SelectItem>
                  <SelectItem value="lost">Mất</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex space-x-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setIsUpdateStatusDialogOpen(false)}
              >
                Hủy
              </Button>
              <Button
                onClick={handleUpdateStatus}
                disabled={updateAccessCard.isPending}
              >
                {updateAccessCard.isPending ? 'Đang cập nhật...' : 'Cập nhật'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
