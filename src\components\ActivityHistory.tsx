
import { useState, useMemo } from "react";
import { ActivityTable } from "./ActivityTable";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { Filter, Download, CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useActivityHistory } from "@/hooks/api";
import { useIsMobile } from "@/hooks/use-mobile";
import type { ActivityHistory as ActivityHistoryType } from "@/lib/api";

// Transform API data to match ActivityTable expected format
interface Activity {
  id: number;
  type: "walkie-talkie" | "access-card";
  deviceName: string;
  action: "borrowed" | "returned" | "updated";
  borrower: string;
  issuer: string;
  borrowTime?: Date;
  returnTime?: Date;
  description?: string; // Thêm description để hiển thị chi tiết
  created_at: Date; // Thêm để sắp xếp
}

export const ActivityHistory = () => {
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [filterType, setFilterType] = useState<"all" | "walkie-talkie" | "access-card">("all");
  const [page, setPage] = useState(1);
  const { isMobile } = useIsMobile();

  // Fetch activity history from API
  const { data: activityData, isLoading, error } = useActivityHistory({
    page,
    limit: 50,
    type: filterType === "all" ? undefined : filterType.replace("-", "_") as "walkie_talkie" | "access_card",
  });

  // Transform API data to match ActivityTable expected format
  const activities: Activity[] = useMemo(() => {
    if (!activityData?.data) return [];

    const transformedData = activityData.data.map((item: ActivityHistoryType & { device_name?: string; issuer_name?: string }) => {
      // Tạo Date object từ UTC string và đảm bảo múi giờ đúng
      const createDateFromUTC = (utcString: string) => {
        // SQLite datetime('now') trả về UTC string format: YYYY-MM-DD HH:MM:SS
        // Thêm 'Z' để đảm bảo JavaScript parse như UTC
        const isoString = utcString.includes('T') ? utcString : utcString.replace(' ', 'T') + 'Z';
        return new Date(isoString);
      };

      return {
        id: item.id,
        type: item.item_type.replace("_", "-") as "walkie-talkie" | "access-card",
        deviceName: item.device_name ?
          (item.item_type === 'walkie_talkie' ? `${item.device_name}` : `${item.device_name}`) :
          (item.item_type === 'walkie_talkie' ? `Bộ đàm #${item.item_id}` : `Thẻ từ #${item.item_id}`),
        action: item.action === "assign" ? "borrowed" :
                item.action === "return" ? "returned" :
                item.action === "update" ? "updated" : "borrowed",
        borrower: item.action === "assign" || item.action === "return"
          ? (item.borrower_name || "Không xác định")
          : item.action === "update" ? "-" : "-",
        issuer: item.issuer_name || item.user_id || "Không xác định",
        borrowTime: item.action === "assign" ? createDateFromUTC(item.created_at) :
                    item.action === "update" ? createDateFromUTC(item.created_at) : undefined,
        description: item.description,
        returnTime: item.action === "return" ? createDateFromUTC(item.created_at) : undefined,
        // Thêm created_at để sắp xếp
        created_at: createDateFromUTC(item.created_at),
      };
    });

    // Sắp xếp lại theo thời gian tạo mới nhất (DESC) để đảm bảo thứ tự đúng
    return transformedData.sort((a: Activity, b: Activity) => b.created_at.getTime() - a.created_at.getTime());
  }, [activityData]);

  // Filter data based on date and type (search is handled by ActivityTable)
  const filteredActivities = useMemo(() => {
    return activities.filter(activity => {
      const matchesType = filterType === "all" || activity.type === filterType;

      // Check if activity matches selected date
      const activityDate = activity.borrowTime || activity.returnTime;

      // If no date is selected, show all activities
      if (!selectedDate) {
        return matchesType;
      }

      // If a date is selected, only show activities that have a date and match the selected date
      // Convert both dates to Vietnam timezone for accurate comparison
      const matchesDate = activityDate && (() => {
        // Convert activity date to Vietnam timezone
        const activityDateVN = new Date(activityDate.toLocaleString("en-US", { timeZone: "Asia/Ho_Chi_Minh" }));
        // Convert selected date to Vietnam timezone (it's already in local time)
        const selectedDateVN = new Date(selectedDate.toLocaleString("en-US", { timeZone: "Asia/Ho_Chi_Minh" }));

        return activityDateVN.toDateString() === selectedDateVN.toDateString();
      })();

      return matchesType && matchesDate;
    });
  }, [activities, filterType, selectedDate]);

  const exportToCSV = () => {
    const headers = ["Loại", "Thiết bị", "Hành động", "Người mượn", "Người giao", "Thời gian", "Thời gian trả"];
    const csvContent = [
      headers.join(","),
      ...filteredActivities.map(activity => [
        activity.type === "walkie-talkie" ? "Bộ đàm" : "Thẻ từ",
        activity.deviceName,
        activity.action === "borrowed" ? "Cho mượn" :
        activity.action === "returned" ? "Trả về" :
        activity.description ? activity.description.match(/thành (.+)$/)?.[1] || "Cập nhật" : "Cập nhật",
        activity.borrower,
        activity.issuer,
        activity.borrowTime ? activity.borrowTime.toLocaleString('vi-VN', { timeZone: 'Asia/Ho_Chi_Minh' }) : "",
        activity.returnTime ? activity.returnTime.toLocaleString('vi-VN', { timeZone: 'Asia/Ho_Chi_Minh' }) : ""
      ].join(","))
    ].join("\n");

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `lich-su-hoat-dong-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Đang tải lịch sử hoạt động...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
            <h2 className="text-red-800 font-semibold mb-2">Lỗi tải dữ liệu</h2>
            <p className="text-red-600 text-sm mb-4">
              {error.message || 'Không thể tải lịch sử hoạt động'}
            </p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              size="sm"
            >
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="p-4 bg-white rounded-lg shadow-sm space-y-4">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium">Bộ lọc:</span>
        </div>

        {/* Mobile Layout */}
        {isMobile ? (
          <div className="space-y-3">
            {/* Date Picker - Full width on mobile */}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !selectedDate && "text-muted-foreground"
                  )}
                  size="sm"
                >
                  <CalendarIcon className="mr-2 h-3 w-3" />
                  {selectedDate ? format(selectedDate, "PPP", { locale: vi }) : "Chọn ngày"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="center">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  initialFocus
                  className="p-3 pointer-events-auto"
                />
              </PopoverContent>
            </Popover>

            {/* Filter Buttons - Grid layout on mobile */}
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant={filterType === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setFilterType("all");
                  setPage(1);
                }}
                className="text-xs"
              >
                Tất cả
              </Button>
              <Button
                variant={filterType === "walkie-talkie" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setFilterType("walkie-talkie");
                  setPage(1);
                }}
                className="text-xs"
              >
                Bộ đàm
              </Button>
              <Button
                variant={filterType === "access-card" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setFilterType("access-card");
                  setPage(1);
                }}
                className="text-xs"
              >
                Thẻ từ
              </Button>
            </div>

            {/* Action Buttons - Stack vertically on mobile */}
            <div className="space-y-2">
              {selectedDate && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedDate(undefined)}
                  className="w-full text-xs"
                >
                  Xóa bộ lọc ngày
                </Button>
              )}
              <Button
                onClick={exportToCSV}
                variant="outline"
                size="sm"
                className="w-full text-xs"
              >
                <Download className="h-3 w-3 mr-2" />
                Xuất Excel
              </Button>
            </div>
          </div>
        ) : (
          /* Desktop Layout */
          <div className="flex flex-wrap items-center gap-4">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    !selectedDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, "PPP", { locale: vi }) : "Chọn ngày"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  initialFocus
                  className="p-3 pointer-events-auto"
                />
              </PopoverContent>
            </Popover>

            <div className="flex space-x-2">
              <Button
                variant={filterType === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setFilterType("all");
                  setPage(1);
                }}
              >
                Tất cả
              </Button>
              <Button
                variant={filterType === "walkie-talkie" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setFilterType("walkie-talkie");
                  setPage(1);
                }}
              >
                Bộ đàm
              </Button>
              <Button
                variant={filterType === "access-card" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  setFilterType("access-card");
                  setPage(1);
                }}
              >
                Thẻ từ
              </Button>
            </div>

            {selectedDate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedDate(undefined)}
              >
                Xóa bộ lọc ngày
              </Button>
            )}

            <Button onClick={exportToCSV} variant="outline" size="sm" className="ml-auto">
              <Download className="h-4 w-4 mr-2" />
              Xuất Excel
            </Button>
          </div>
        )}
      </div>

      {/* Activity Table */}
      <ActivityTable data={filteredActivities} />

      {/* Pagination */}
      {activityData?.pagination && activityData.pagination.totalPages > 1 && (
        <div className={cn(
          "flex items-center justify-center py-4",
          isMobile ? "flex-col space-y-2" : "space-x-2"
        )}>
          {isMobile ? (
            <>
              <span className="text-xs text-gray-600">
                Trang {page} / {activityData.pagination.totalPages}
              </span>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page <= 1}
                  className="text-xs px-3"
                >
                  Trước
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page >= activityData.pagination.totalPages}
                  className="text-xs px-3"
                >
                  Sau
                </Button>
              </div>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page <= 1}
              >
                Trang trước
              </Button>
              <span className="text-sm text-gray-600">
                Trang {page} / {activityData.pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page >= activityData.pagination.totalPages}
              >
                Trang sau
              </Button>
            </>
          )}
        </div>
      )}
    </div>
  );
};
