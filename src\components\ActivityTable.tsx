import { useState, useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  createColumnHelper,
  flexRender,
  type SortingState,
  type ColumnFiltersState,
  type PaginationState,
} from "@tanstack/react-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import {
  Clock,
  Search,
  Radio,
  CreditCard,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from "lucide-react";

interface Activity {
  id: number;
  type: "walkie-talkie" | "access-card";
  deviceName: string;
  action: "borrowed" | "returned" | "updated";
  borrower: string;
  issuer: string;
  borrowTime?: Date;
  returnTime?: Date;
  description?: string; // Thêm description để hiển thị chi tiết
  created_at: Date; // Thêm để sắp xếp
}

interface ActivityTableProps {
  data: Activity[];
}

const columnHelper = createColumnHelper<Activity>();

export const ActivityTable = ({ data }: ActivityTableProps) => {
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'created_at', desc: true } // Mặc định sắp xếp theo thời gian tạo mới nhất
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const { isMobile } = useIsMobile();

  const columns = useMemo(
    () => [
      columnHelper.accessor("type", {
        header: "Loại",
        cell: (info) => (
          <div className="flex items-center space-x-2">
            {info.getValue() === "walkie-talkie" ? (
              <div className="bg-blue-100 p-1 rounded-full">
                <Radio className="h-3 w-3 text-blue-600" />
              </div>
            ) : (
              <div className="bg-green-100 p-1 rounded-full">
                <CreditCard className="h-3 w-3 text-green-600" />
              </div>
            )}
            <span className="text-sm">
              {info.getValue() === "walkie-talkie" ? "Bộ đàm" : "Thẻ từ"}
            </span>
          </div>
        ),
        filterFn: "includesString",
      }),
      columnHelper.accessor("deviceName", {
        header: "Thiết bị",
        cell: (info) => (
          <span className="font-medium">{info.getValue()}</span>
        ),
      }),
      columnHelper.accessor("action", {
        header: "Hành động",
        cell: (info) => {
          const action = info.getValue();
          const row = info.row.original;

          // Xử lý hiển thị cho action "updated"
          if (action === "updated" && row.description) {
            // Parse description để lấy thông tin trạng thái
            const statusMatch = row.description.match(/thành (.+)$/);
            const statusText = statusMatch ? statusMatch[1] : "Cập nhật";

            return (
              <Badge
                variant="outline"
                className="bg-blue-100 text-blue-800 border-blue-300"
              >
                {statusText}
              </Badge>
            );
          }

          return (
            <Badge
              variant={action === "borrowed" ? "default" : "secondary"}
              className={
                action === "borrowed"
                  ? "bg-orange-100 text-orange-800"
                  : "bg-green-100 text-green-800"
              }
            >
              {action === "borrowed" ? "Cho mượn" : "Trả về"}
            </Badge>
          );
        },
        filterFn: "includesString",
      }),
      columnHelper.accessor("borrower", {
        header: "Người mượn",
        cell: (info) => (
          <span className="text-sm">{info.getValue()}</span>
        ),
      }),
      columnHelper.accessor("issuer", {
        header: "Người giao",
        cell: (info) => (
          <span className="text-sm">{info.getValue()}</span>
        ),
      }),
      columnHelper.accessor("borrowTime", {
        header: "Thời gian",
        cell: (info) => {
          const value = info.getValue();
          return value ? (
            <span className="text-sm text-gray-600">
              {value.toLocaleString('vi-VN', {
                timeZone: 'Asia/Ho_Chi_Minh',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          ) : (
            <span className="text-sm text-gray-400">-</span>
          );
        },
        sortingFn: "datetime",
      }),
      columnHelper.accessor("returnTime", {
        header: "Thời gian trả",
        cell: (info) => {
          const value = info.getValue();
          return value ? (
            <span className="text-sm text-gray-600">
              {value.toLocaleString('vi-VN', {
                timeZone: 'Asia/Ho_Chi_Minh',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          ) : (
            <span className="text-sm text-gray-400">-</span>
          );
        },
        sortingFn: "datetime",
      }),
      // Cột ẩn để sắp xếp theo thời gian tạo
      columnHelper.accessor("created_at", {
        header: "Thời gian tạo",
        cell: () => null, // Không hiển thị
        sortingFn: "datetime",
        enableHiding: true,
      }),
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
      columnVisibility: {
        created_at: false, // Ẩn cột created_at
      },
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className={cn(isMobile && "pb-3")}>
        <CardTitle className="flex items-center space-x-2">
          <Clock className={cn(
            "text-purple-600",
            isMobile ? "h-4 w-4" : "h-5 w-5"
          )} />
          <span className={cn(isMobile ? "text-base" : "text-lg")}>Lịch sử hoạt động</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Global Search */}
        <div className="mb-4">
          <div className="relative">
            <Search className={cn(
              "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",
              isMobile ? "h-3 w-3" : "h-4 w-4"
            )} />
            <Input
              placeholder="Tìm kiếm trong lịch sử..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className={cn("pl-10", isMobile && "text-sm")}
              size={isMobile ? "sm" : "default"}
            />
          </div>
        </div>

        {/* Table/Cards */}
        {isMobile ? (
          /* Mobile Card Layout */
          <div className="space-y-3">
            {table.getRowModel().rows.map((row) => {
              const activity = row.original;
              return (
                <Card key={row.id} className="p-3 border border-gray-200">
                  <div className="space-y-2">
                    {/* Header with type and device */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {activity.type === "walkie-talkie" ? (
                          <div className="bg-blue-100 p-1 rounded-full">
                            <Radio className="h-3 w-3 text-blue-600" />
                          </div>
                        ) : (
                          <div className="bg-green-100 p-1 rounded-full">
                            <CreditCard className="h-3 w-3 text-green-600" />
                          </div>
                        )}
                        <span className="text-xs font-medium">
                          {activity.type === "walkie-talkie" ? "Bộ đàm" : "Thẻ từ"}
                        </span>
                      </div>
                      <div className="text-right">
                        {activity.action === "updated" && activity.description ? (
                          <Badge
                            variant="outline"
                            className="bg-blue-100 text-blue-800 border-blue-300 text-xs"
                          >
                            {activity.description.match(/thành (.+)$/)?.[1] || "Cập nhật"}
                          </Badge>
                        ) : (
                          <Badge
                            variant={activity.action === "borrowed" ? "default" : "secondary"}
                            className={cn(
                              "text-xs",
                              activity.action === "borrowed"
                                ? "bg-orange-100 text-orange-800"
                                : "bg-green-100 text-green-800"
                            )}
                          >
                            {activity.action === "borrowed" ? "Cho mượn" : "Trả về"}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Device name */}
                    <div>
                      <span className="font-medium text-sm">{activity.deviceName}</span>
                    </div>

                    {/* People info */}
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-gray-500">Người mượn:</span>
                        <div className="font-medium">{activity.borrower}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Người giao:</span>
                        <div className="font-medium">{activity.issuer}</div>
                      </div>
                    </div>

                    {/* Time info */}
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-gray-500">Thời gian:</span>
                        <div className="text-gray-600">
                          {activity.borrowTime ?
                            activity.borrowTime.toLocaleString('vi-VN', {
                              timeZone: 'Asia/Ho_Chi_Minh',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit'
                            }) : "-"
                          }
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">Thời gian trả:</span>
                        <div className="text-gray-600">
                          {activity.returnTime ?
                            activity.returnTime.toLocaleString('vi-VN', {
                              timeZone: 'Asia/Ho_Chi_Minh',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit'
                            }) : "-"
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        ) : (
          /* Desktop Table Layout */
          <div className="rounded-md border">
            <table className="w-full">
              <thead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id} className="border-b bg-gray-50">
                    {headerGroup.headers.map((header) => (
                      <th
                        key={header.id}
                        className="px-4 py-3 text-left text-sm font-medium text-gray-900"
                      >
                        {header.isPlaceholder ? null : (
                          <div
                            className={`flex items-center space-x-1 ${
                              header.column.getCanSort() ? "cursor-pointer select-none" : ""
                            }`}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            <span>
                              {flexRender(header.column.columnDef.header, header.getContext())}
                            </span>
                            {header.column.getCanSort() && (
                              <span className="ml-1">
                                {header.column.getIsSorted() === "asc" ? (
                                  <ChevronUp className="h-4 w-4" />
                                ) : header.column.getIsSorted() === "desc" ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <div className="h-4 w-4" />
                                )}
                              </span>
                            )}
                          </div>
                        )}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody>
                {table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="border-b hover:bg-gray-50">
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="px-4 py-3">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {isMobile ? (
          /* Mobile Pagination */
          <div className="flex flex-col items-center space-y-3 mt-4">
            <div className="text-xs text-gray-700 text-center">
              Hiển thị {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} - {Math.min(
                (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length
              )} / {table.getFilteredRowModel().rows.length}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
                className="px-2"
              >
                <ChevronsLeft className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
                className="px-2"
              >
                <ChevronLeft className="h-3 w-3" />
              </Button>
              <span className="text-xs px-2">
                {table.getState().pagination.pageIndex + 1} / {table.getPageCount()}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
                className="px-2"
              >
                <ChevronRight className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
                className="px-2"
              >
                <ChevronsRight className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ) : (
          /* Desktop Pagination */
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-700">
              Hiển thị {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} đến{" "}
              {Math.min(
                (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length
              )}{" "}
              trong tổng số {table.getFilteredRowModel().rows.length} kết quả
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">
                Trang {table.getState().pagination.pageIndex + 1} / {table.getPageCount()}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
