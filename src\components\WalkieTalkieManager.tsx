
import { useState } from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Radio, Plus, UserCheck, UserX, Search, Loader2, Trash2, Settings } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "@tanstack/react-form";
import { addDeviceSchema, borrowDeviceSchema, type AddDeviceFormData, type BorrowDeviceFormData } from "@/lib/schemas";
import { useWalkieTalkies, useCreateWalkieTalkie, useUpdateWalkieTalkie, useDeleteWalkieTalkie, useCreateActivityRecord } from "@/hooks/api";
import type { WalkieTalkie } from "@/lib/api";
import { useAuth } from "@/contexts/AuthContext";

export const WalkieTalkieManager = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBorrowDialogOpen, setIsBorrowDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isUpdateStatusDialogOpen, setIsUpdateStatusDialogOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<WalkieTalkie | null>(null);
  const [deviceToDelete, setDeviceToDelete] = useState<WalkieTalkie | null>(null);
  const [deviceToUpdate, setDeviceToUpdate] = useState<WalkieTalkie | null>(null);
  const [newStatus, setNewStatus] = useState<WalkieTalkie['status']>('available');
  const { toast } = useToast();
  const { user } = useAuth();

  // API hooks
  const { data: walkieTalkies = [], isLoading, error } = useWalkieTalkies();
  const createWalkieTalkie = useCreateWalkieTalkie();
  const updateWalkieTalkie = useUpdateWalkieTalkie();
  const deleteWalkieTalkie = useDeleteWalkieTalkie();
  const createActivityRecord = useCreateActivityRecord();

  // TanStack Form cho thêm thiết bị
  const addDeviceForm = useForm({
    defaultValues: {
      name: '',
    } as AddDeviceFormData,
    validators: {
      onChange: ({ value }) => {
        const result = addDeviceSchema.safeParse(value);
        if (!result.success) {
          // Convert Zod v4 errors to field errors format
          const fieldErrors: Record<string, string[]> = {};
          result.error.issues.forEach(issue => {
            const path = issue.path.join('.');
            if (!fieldErrors[path]) {
              fieldErrors[path] = [];
            }
            fieldErrors[path].push(issue.message);
          });
          return fieldErrors;
        }
        return undefined;
      },
    },
    onSubmit: async ({ value }) => {
      try {
        await createWalkieTalkie.mutateAsync({
          name: value.name,
          status: 'available',
        });

        setIsAddDialogOpen(false);
        addDeviceForm.reset();
      } catch (error) {
        // Error handling is done in the hook
      }
    },
  });

  // TanStack Form cho cho mượn thiết bị
  const borrowDeviceForm = useForm({
    defaultValues: {
      borrowerName: '',
    } as BorrowDeviceFormData,
    validators: {
      onChange: ({ value }) => {
        const result = borrowDeviceSchema.safeParse(value);
        if (!result.success) {
          // Convert Zod v4 errors to field errors format
          const fieldErrors: Record<string, string[]> = {};
          result.error.issues.forEach(issue => {
            const path = issue.path.join('.');
            if (!fieldErrors[path]) {
              fieldErrors[path] = [];
            }
            fieldErrors[path].push(issue.message);
          });
          return fieldErrors;
        }
        return undefined;
      },
    },
    onSubmit: async ({ value }) => {
      if (!selectedDevice) return;

      try {
        await updateWalkieTalkie.mutateAsync({
          id: selectedDevice.id,
          data: {
            status: 'assigned',
            assigned_to: value.borrowerName,
          }
        });

        // Create activity record
        await createActivityRecord.mutateAsync({
          item_type: 'walkie_talkie',
          item_id: selectedDevice.id,
          action: 'assign',
          description: `Giao bộ đàm ${selectedDevice.name} cho ${value.borrowerName}`,
          user_id: user?.username || 'admin',
          borrower_name: value.borrowerName,
        });

        setIsBorrowDialogOpen(false);
        setSelectedDevice(null);
        borrowDeviceForm.reset();
      } catch (error) {
        // Error handling is done in the hook
      }
    },
  });

  const filteredDevices = walkieTalkies.filter(device =>
    device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (device.assigned_to && device.assigned_to.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const returnDevice = async (device: WalkieTalkie) => {
    try {
      await updateWalkieTalkie.mutateAsync({
        id: device.id,
        data: {
          status: 'available',
          assigned_to: null,
        }
      });

      // Create activity record
      await createActivityRecord.mutateAsync({
        item_type: 'walkie_talkie',
        item_id: device.id,
        action: 'return',
        description: `Thu hồi bộ đàm ${device.name} từ ${device.assigned_to}`,
        user_id: user?.username || 'admin',
        borrower_name: device.assigned_to || undefined,
      });
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const openBorrowDialog = (device: WalkieTalkie) => {
    setSelectedDevice(device);
    borrowDeviceForm.reset(); // Reset form khi mở dialog
    setIsBorrowDialogOpen(true);
  };

  const openDeleteDialog = (device: WalkieTalkie) => {
    setDeviceToDelete(device);
    setIsDeleteDialogOpen(true);
  };

  const openUpdateStatusDialog = (device: WalkieTalkie) => {
    setDeviceToUpdate(device);
    setNewStatus(device.status);
    setIsUpdateStatusDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!deviceToDelete) return;

    try {
      await deleteWalkieTalkie.mutateAsync(deviceToDelete.id);

      // Create activity record
      await createActivityRecord.mutateAsync({
        item_type: 'walkie_talkie',
        item_id: deviceToDelete.id,
        action: 'delete',
        description: `Xóa bộ đàm ${deviceToDelete.name}`,
        user_id: user?.username || 'admin',
      });

      setIsDeleteDialogOpen(false);
      setDeviceToDelete(null);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleUpdateStatus = async () => {
    if (!deviceToUpdate) return;

    try {
      await updateWalkieTalkie.mutateAsync({
        id: deviceToUpdate.id,
        data: {
          status: newStatus,
          // Clear assigned_to if status is not assigned
          assigned_to: newStatus === 'assigned' ? deviceToUpdate.assigned_to : null
        }
      });

      // Create activity record
      await createActivityRecord.mutateAsync({
        item_type: 'walkie_talkie',
        item_id: deviceToUpdate.id,
        action: 'update',
        description: `Cập nhật trạng thái bộ đàm ${deviceToUpdate.name} thành ${
          newStatus === 'available' ? 'Có sẵn' :
          newStatus === 'assigned' ? 'Đã giao' :
          newStatus === 'maintenance' ? 'Bảo trì' : 'Mất'
        }`,
        user_id: user?.username || 'admin',
      });

      setIsUpdateStatusDialogOpen(false);
      setDeviceToUpdate(null);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Đang tải dữ liệu bộ đàm...</span>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-500 mb-2">Lỗi khi tải dữ liệu bộ đàm</p>
            <p className="text-sm text-gray-500">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Radio className="h-5 w-5 text-blue-600" />
              <span>Quản lý Bộ đàm</span>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm bộ đàm
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Thêm bộ đàm mới</DialogTitle>
                </DialogHeader>
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    addDeviceForm.handleSubmit()
                  }}
                  className="space-y-4"
                >
                  <addDeviceForm.Field
                    name="name"
                    children={(field) => (
                      <div>
                        <Label htmlFor="device-name">Tên bộ đàm</Label>
                        <Input
                          id="device-name"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                          placeholder="VD: Bộ đàm 004"
                        />
                        {field.state.meta.errors.length > 0 && (
                          <p className="text-sm text-red-500 mt-1">
                            {field.state.meta.errors[0] as string}
                          </p>
                        )}
                      </div>
                    )}
                  />
                  <addDeviceForm.Subscribe
                    selector={(state) => [state.canSubmit, state.isSubmitting]}
                    children={([canSubmit, isSubmitting]) => (
                      <Button type="submit" disabled={!canSubmit || isSubmitting} className="w-full">
                        {isSubmitting ? 'Đang thêm...' : 'Thêm'}
                      </Button>
                    )}
                  />
                </form>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4 space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Tìm kiếm bộ đàm hoặc người mượn..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredDevices.map((device) => (
              <Card key={device.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-gray-900">{device.name}</h3>
                    </div>
                    <Badge
                      variant={device.status === "available" ? "default" : "destructive"}
                      className={device.status === "available" ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}
                    >
                      {device.status === "available" ? "Có sẵn" :
                       device.status === "assigned" ? "Đã giao" :
                       device.status === "maintenance" ? "Bảo trì" : "Mất"}
                    </Badge>
                  </div>
                  
                  {device.status === "assigned" && device.assigned_to && (
                    <div className="mb-3 text-sm text-gray-600">
                      <p><strong>Người được giao:</strong> {device.assigned_to}</p>
                    </div>
                  )}
                  
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      {device.status === "available" ? (
                        <Button
                          onClick={() => openBorrowDialog(device)}
                          className="flex-1 bg-blue-600 hover:bg-blue-700"
                          size="sm"
                        >
                          <UserCheck className="h-4 w-4 mr-1" />
                          Giao thiết bị
                        </Button>
                      ) : device.status === "assigned" ? (
                        <Button
                          onClick={() => returnDevice(device)}
                          variant="outline"
                          className="flex-1"
                          size="sm"
                        >
                          <UserX className="h-4 w-4 mr-1" />
                          Thu hồi
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          className="flex-1"
                          size="sm"
                          disabled
                        >
                          {device.status === "maintenance" ? "Đang bảo trì" : "Không khả dụng"}
                        </Button>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        onClick={() => openUpdateStatusDialog(device)}
                        variant="outline"
                        className="flex-1"
                        size="sm"
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Cập nhật trạng thái
                      </Button>
                      <Button
                        onClick={() => openDeleteDialog(device)}
                        variant="destructive"
                        size="sm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Borrow Dialog */}
      <Dialog open={isBorrowDialogOpen} onOpenChange={setIsBorrowDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Giao thiết bị {selectedDevice?.name}</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              borrowDeviceForm.handleSubmit()
            }}
            className="space-y-4"
          >
            <borrowDeviceForm.Field
              name="borrowerName"
              children={(field) => (
                <div>
                  <Label htmlFor="borrower-name">Tên người được giao</Label>
                  <Input
                    id="borrower-name"
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    placeholder="Nhập tên người được giao thiết bị"
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="text-sm text-red-500 mt-1">
                      {field.state.meta.errors[0] as string}
                    </p>
                  )}
                </div>
              )}
            />
            <borrowDeviceForm.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || isSubmitting} className="w-full">
                  {isSubmitting ? 'Đang xử lý...' : 'Xác nhận giao thiết bị'}
                </Button>
              )}
            />
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa bộ đàm</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Bạn có chắc chắn muốn xóa bộ đàm <strong>{deviceToDelete?.name}</strong>?</p>
            <p className="text-sm text-gray-500">Hành động này không thể hoàn tác.</p>
            <div className="flex space-x-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Hủy
              </Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleteWalkieTalkie.isPending}
              >
                {deleteWalkieTalkie.isPending ? 'Đang xóa...' : 'Xóa'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Update Status Dialog */}
      <Dialog open={isUpdateStatusDialogOpen} onOpenChange={setIsUpdateStatusDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cập nhật trạng thái {deviceToUpdate?.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status-select">Trạng thái mới</Label>
              <Select value={newStatus} onValueChange={(value: WalkieTalkie['status']) => setNewStatus(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="available">Có sẵn</SelectItem>
                  <SelectItem value="assigned">Đã giao</SelectItem>
                  <SelectItem value="maintenance">Bảo trì</SelectItem>
                  <SelectItem value="lost">Mất</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex space-x-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setIsUpdateStatusDialogOpen(false)}
              >
                Hủy
              </Button>
              <Button
                onClick={handleUpdateStatus}
                disabled={updateWalkieTalkie.isPending}
              >
                {updateWalkieTalkie.isPending ? 'Đang cập nhật...' : 'Cập nhật'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
