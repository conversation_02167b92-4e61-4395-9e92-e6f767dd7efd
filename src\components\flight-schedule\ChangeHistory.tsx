import { useState } from 'react'
import { useFlightChanges } from '@/hooks/useFlightMutations'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  History, 
  User, 
  Clock, 
  ArrowRight, 
  RefreshCw,
  Filter,
  Calendar
} from 'lucide-react'

import { formatDateVN, createDateFromUTC } from '@/lib/timezone-utils'

interface ChangeHistoryProps {
  flightId?: string
  className?: string
}

interface FlightChange {
  id: string
  flight_id: string
  field_name: string
  old_value: string | null
  new_value: string | null
  changed_by: string
  changer_name?: string
  changed_at: string
  date?: string
  stt?: number
  arr_flt?: string
  dep_flt?: string
}

const fieldDisplayNames: Record<string, string> = {
  arr_flt: '<PERSON><PERSON> hiệu bay đến',
  arr_from: '<PERSON><PERSON><PERSON><PERSON> đến',
  arr_reg: '<PERSON><PERSON> đăng ký máy bay đến',
  arr_time: '<PERSON><PERSON><PERSON> đến',
  arr_staff: 'Nhân viên đến',
  dep_flt: 'Số hiệu bay đi',
  dep_to: 'Điểm đi',
  dep_reg: 'Số đăng ký máy bay đi',
  dep_time: 'Giờ đi',
  dep_staff: 'Nhân viên đi',
  remark: 'Ghi chú',
  stt: 'STT'
}

export const ChangeHistory: React.FC<ChangeHistoryProps> = ({ 
  flightId, 
  className 
}) => {
  const [showFilters, setShowFilters] = useState(false)
  const { data: changes, isLoading, error, refetch } = useFlightChanges(flightId)

  const formatValue = (value: string | null): string => {
    if (value === null || value === '') return '(trống)'
    return value
  }

  const formatChangeTime = (timestamp: string): string => {
    try {
      const date = createDateFromUTC(timestamp)
      return date.toLocaleTimeString('vi-VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    } catch {
      return formatDateVN(timestamp, { includeTime: true })
    }
  }

  const getChangeTypeColor = (fieldName: string): string => {
    if (fieldName.startsWith('arr_')) return 'bg-blue-50 text-blue-700 border-blue-200'
    if (fieldName.startsWith('dep_')) return 'bg-orange-50 text-orange-700 border-orange-200'
    return 'bg-gray-50 text-gray-700 border-gray-200'
  }

  const getFlightInfo = (change: FlightChange): string => {
    const parts = []
    if (change.date) parts.push(change.date)
    if (change.stt) parts.push(`STT ${change.stt}`)
    if (change.arr_flt) parts.push(`ARR ${change.arr_flt}`)
    if (change.dep_flt) parts.push(`DEP ${change.dep_flt}`)
    return parts.join(' • ')
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <History className="h-5 w-5" />
            Lịch sử thay đổi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">Đang tải...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <History className="h-5 w-5" />
            Lịch sử thay đổi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Không thể tải lịch sử thay đổi</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => refetch()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Thử lại
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const changesList = changes?.data || []

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <History className="h-5 w-5" />
            Lịch sử thay đổi
            {changesList.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {changesList.length}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refetch()}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {changesList.length === 0 ? (
          <div className="text-center py-8">
            <History className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <p className="text-gray-500">Chưa có thay đổi nào</p>
            <p className="text-sm text-gray-400">
              Các thay đổi sẽ được ghi lại tại đây
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-4">
              {changesList.map((change: FlightChange, index: number) => (
                <div key={change.id}>
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getChangeTypeColor(change.field_name)}`}
                        >
                          {fieldDisplayNames[change.field_name] || change.field_name}
                        </Badge>
                        
                        {!flightId && (
                          <span className="text-xs text-gray-500">
                            {getFlightInfo(change)}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm mb-2">
                        <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded">
                          {formatValue(change.old_value)}
                        </span>
                        <ArrowRight className="h-3 w-3 text-gray-400" />
                        <span className="text-gray-900 bg-green-100 px-2 py-1 rounded">
                          {formatValue(change.new_value)}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>{change.changer_name || change.changed_by}</span>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{formatChangeTime(change.changed_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {index < changesList.length - 1 && (
                    <Separator className="my-4" />
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}
