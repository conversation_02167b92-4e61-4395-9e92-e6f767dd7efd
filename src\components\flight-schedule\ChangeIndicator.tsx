import { useState, useRef, useEffect } from 'react'
import { History, Clock, User, ArrowRight } from 'lucide-react'
import { cn } from '@/lib/utils'

import { formatDateVN, createDateFromUTC } from '@/lib/timezone-utils'

interface FieldChange {
  id: string
  old_value: string | null
  new_value: string | null
  changed_by: string
  changer_name?: string
  changed_at: string
}

interface ChangeIndicatorProps {
  changes: FieldChange[]
  className?: string
}

export const ChangeIndicator: React.FC<ChangeIndicatorProps> = ({
  changes,
  className
}) => {
  const [showTooltip, setShowTooltip] = useState(false)
  const tooltipRef = useRef<HTMLDivElement>(null)

  if (!changes || changes.length === 0) {
    return null
  }

  // <PERSON>le click outside to close tooltip
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
        setShowTooltip(false)
      }
    }

    if (showTooltip) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showTooltip])

  const formatValue = (value: string | null): string => {
    if (value === null || value === '') return '(trống)'
    return value
  }

  const formatChangeTime = (timestamp: string): string => {
    try {
      const date = createDateFromUTC(timestamp)
      return date.toLocaleTimeString('vi-VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    } catch {
      return formatDateVN(timestamp, { includeTime: true })
    }
  }

  const getChangeCount = () => {
    return changes.length
  }

  const handleToggleTooltip = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowTooltip(!showTooltip)
  }

  return (
    <div className="relative inline-block" ref={tooltipRef}>
      <button
        className={cn(
          'inline-flex items-center justify-center',
          'w-4 h-4 rounded-full',
          'bg-blue-100 hover:bg-blue-200',
          'text-blue-600 hover:text-blue-700',
          'transition-colors duration-200',
          'cursor-pointer',
          className
        )}
        onClick={handleToggleTooltip}
        title={`${getChangeCount()} thay đổi - Click để xem chi tiết`}
      >
        <History className="w-2.5 h-2.5" />
      </button>

      {showTooltip && (
        <div className="absolute z-50 bottom-full left-1/2 transform -translate-x-1/2 mb-2">
          <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-[300px] max-w-[400px]">
            {/* Header */}
            <div className="flex items-center gap-2 mb-3 pb-2 border-b border-gray-100">
              <History className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Lịch sử thay đổi ({getChangeCount()})
              </span>
            </div>

            {/* Timeline */}
            <div className="space-y-3 max-h-[200px] overflow-y-auto">
              {changes.map((change, index) => (
                <div key={change.id} className="relative">
                  {/* Timeline line */}
                  {index < changes.length - 1 && (
                    <div className="absolute left-2 top-6 w-px h-full bg-gray-200"></div>
                  )}
                  
                  <div className="flex items-start gap-3">
                    {/* Timeline dot */}
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    </div>
                    
                    {/* Change content */}
                    <div className="flex-1 min-w-0">
                      {/* Value change */}
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs text-gray-600 bg-red-50 px-2 py-0.5 rounded border">
                          {formatValue(change.old_value)}
                        </span>
                        <ArrowRight className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-900 bg-green-50 px-2 py-0.5 rounded border">
                          {formatValue(change.new_value)}
                        </span>
                      </div>
                      
                      {/* Meta info */}
                      <div className="flex items-center gap-3 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          <span>{change.changer_name || change.changed_by}</span>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{formatChangeTime(change.changed_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Tooltip arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2">
              <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
              <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-px"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Compact version for smaller spaces
export const ChangeIndicatorCompact: React.FC<ChangeIndicatorProps> = ({
  changes,
  className
}) => {
  if (!changes || changes.length === 0) {
    return null
  }

  return (
    <div
      className={cn(
        'inline-flex items-center justify-center',
        'w-2 h-2 rounded-full',
        'bg-blue-500 hover:bg-blue-600',
        'cursor-pointer transition-colors duration-200',
        className
      )}
      title={`${changes.length} thay đổi`}
    />
  )
}

// Badge version showing change count
export const ChangeIndicatorBadge: React.FC<ChangeIndicatorProps> = ({
  changes,
  className
}) => {
  const [showTooltip, setShowTooltip] = useState(false)
  const tooltipRef = useRef<HTMLDivElement>(null)

  if (!changes || changes.length === 0) {
    return null
  }

  // Handle click outside to close tooltip
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
        setShowTooltip(false)
      }
    }

    if (showTooltip) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showTooltip])

  const handleToggleTooltip = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowTooltip(!showTooltip)
  }

  return (
    <div className="relative inline-block" ref={tooltipRef}>
      <span
        className={cn(
          'inline-flex items-center justify-center',
          'min-w-[16px] h-4 px-1',
          'bg-blue-100 text-blue-700',
          'text-xs font-medium rounded-full',
          'cursor-pointer hover:bg-blue-200',
          'transition-colors duration-200',
          className
        )}
        onClick={handleToggleTooltip}
        title={`${changes.length} thay đổi - Click để xem chi tiết`}
      >
        {changes.length}
      </span>

      {showTooltip && (
        <div className="absolute z-50 bottom-full left-1/2 transform -translate-x-1/2 mb-2">
          <div className="bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
            {changes.length} thay đổi
            <div className="absolute top-full left-1/2 transform -translate-x-1/2">
              <div className="w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
