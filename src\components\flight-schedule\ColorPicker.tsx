import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Check, Palette, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { PRESET_COLORS, isValidHexColor, getColorName } from '@/lib/cell-colors-service'

interface ColorPickerProps {
  currentColor?: string
  onColorSelect: (color: string) => void
  onClose: () => void
  onRemoveColor?: () => void
  className?: string
}

export const ColorPicker: React.FC<ColorPickerProps> = ({
  currentColor,
  onColorSelect,
  onClose,
  onRemoveColor,
  className
}) => {
  const [customColor, setCustomColor] = useState(currentColor || '#ffffff')
  const [isValidCustom, setIsValidCustom] = useState(true)

  const handlePresetColorClick = (color: string) => {
    onColorSelect(color)
  }

  const handleCustomColorChange = (value: string) => {
    setCustomColor(value)
    const isValid = isValidHexColor(value)
    setIsValidCustom(isValid)
    
    if (isValid) {
      onColorSelect(value)
    }
  }

  const handleCustomColorSubmit = () => {
    if (isValidCustom && isValidHexColor(customColor)) {
      onColorSelect(customColor)
    }
  }

  return (
    <div className={cn("p-4 bg-white border border-gray-200 rounded-lg shadow-lg min-w-[280px]", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Palette className="h-4 w-4 text-gray-600" />
          <span className="font-medium text-sm text-gray-900">Chọn màu ô</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0 hover:bg-gray-100"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>

      {/* Current Color Display */}
      {currentColor && (
        <div className="mb-3">
          <Label className="text-xs text-gray-600 mb-1 block">Màu hiện tại</Label>
          <div className="flex items-center gap-2">
            <div
              className="w-6 h-6 rounded border border-gray-300"
              style={{ backgroundColor: currentColor }}
            />
            <span className="text-xs text-gray-700">
              {getColorName(currentColor)} ({currentColor})
            </span>
          </div>
        </div>
      )}

      <Separator className="mb-3" />

      {/* Preset Colors */}
      <div className="mb-4">
        <Label className="text-xs text-gray-600 mb-2 block">Màu có sẵn</Label>
        <div className="grid grid-cols-6 gap-1">
          {PRESET_COLORS.map((color) => (
            <button
              key={color}
              onClick={() => handlePresetColorClick(color)}
              className={cn(
                "w-8 h-8 rounded border-2 transition-all hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                currentColor === color 
                  ? "border-blue-500 ring-2 ring-blue-500 ring-offset-1" 
                  : "border-gray-300 hover:border-gray-400"
              )}
              style={{ backgroundColor: color }}
              title={getColorName(color)}
            >
              {currentColor === color && (
                <Check className="h-3 w-3 mx-auto text-white drop-shadow-sm" />
              )}
            </button>
          ))}
        </div>
      </div>

      <Separator className="mb-3" />

      {/* Custom Color */}
      <div className="mb-4">
        <Label className="text-xs text-gray-600 mb-2 block">Màu tùy chỉnh</Label>
        <div className="flex items-center gap-2">
          <div
            className="w-8 h-8 rounded border border-gray-300 flex-shrink-0"
            style={{ backgroundColor: isValidCustom ? customColor : '#ffffff' }}
          />
          <Input
            type="text"
            value={customColor}
            onChange={(e) => handleCustomColorChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleCustomColorSubmit()
              }
            }}
            placeholder="#ffffff"
            className={cn(
              "text-xs font-mono",
              !isValidCustom && "border-red-500 focus:border-red-500"
            )}
          />
        </div>
        {!isValidCustom && (
          <p className="text-xs text-red-600 mt-1">
            Vui lòng nhập mã màu hex hợp lệ (ví dụ: #ff0000)
          </p>
        )}
      </div>

      <Separator className="mb-3" />

      {/* Action Buttons */}
      <div className="flex gap-2">
        {onRemoveColor && currentColor && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRemoveColor}
            className="flex-1 text-xs"
          >
            Xóa màu
          </Button>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
          className="flex-1 text-xs"
        >
          Hủy
        </Button>
      </div>

      {/* Color Tips */}
      <div className="mt-3 pt-3 border-t border-gray-100">
        <p className="text-xs text-gray-500 leading-relaxed">
          💡 <strong>Mẹo:</strong> Nhấp vào màu có sẵn để chọn nhanh, hoặc nhập mã hex để tùy chỉnh màu.
        </p>
      </div>
    </div>
  )
}

// Preset color groups for better organization
export const COLOR_GROUPS = {
  neutral: ['#ffffff', '#f3f4f6', '#e5e7eb', '#d1d5db'],
  red: ['#fef2f2', '#fee2e2', '#fca5a5', '#ef4444'],
  green: ['#f0fdf4', '#dcfce7', '#86efac', '#22c55e'],
  blue: ['#eff6ff', '#dbeafe', '#93c5fd', '#3b82f6'],
  yellow: ['#fefce8', '#fef3c7', '#fde047', '#eab308'],
  purple: ['#fdf4ff', '#f3e8ff', '#c084fc', '#a855f7'],
} as const

// Alternative ColorPicker with grouped colors
export const GroupedColorPicker: React.FC<ColorPickerProps> = ({
  currentColor,
  onColorSelect,
  onClose,
  onRemoveColor,
  className
}) => {
  const [customColor, setCustomColor] = useState(currentColor || '#ffffff')
  const [isValidCustom, setIsValidCustom] = useState(true)

  const handleCustomColorChange = (value: string) => {
    setCustomColor(value)
    const isValid = isValidHexColor(value)
    setIsValidCustom(isValid)
    
    if (isValid) {
      onColorSelect(value)
    }
  }

  return (
    <div className={cn("p-4 bg-white border border-gray-200 rounded-lg shadow-lg min-w-[320px]", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Palette className="h-4 w-4 text-gray-600" />
          <span className="font-medium text-sm text-gray-900">Chọn màu ô</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0 hover:bg-gray-100"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>

      {/* Grouped Colors */}
      <div className="space-y-3 mb-4">
        {Object.entries(COLOR_GROUPS).map(([groupName, colors]) => (
          <div key={groupName}>
            <Label className="text-xs text-gray-600 mb-1 block capitalize">
              {groupName === 'neutral' ? 'Trung tính' : 
               groupName === 'red' ? 'Đỏ' :
               groupName === 'green' ? 'Xanh lá' :
               groupName === 'blue' ? 'Xanh dương' :
               groupName === 'yellow' ? 'Vàng' :
               groupName === 'purple' ? 'Tím' : groupName}
            </Label>
            <div className="flex gap-1">
              {colors.map((color) => (
                <button
                  key={color}
                  onClick={() => onColorSelect(color)}
                  className={cn(
                    "w-8 h-8 rounded border-2 transition-all hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                    currentColor === color 
                      ? "border-blue-500 ring-2 ring-blue-500 ring-offset-1" 
                      : "border-gray-300 hover:border-gray-400"
                  )}
                  style={{ backgroundColor: color }}
                  title={getColorName(color)}
                >
                  {currentColor === color && (
                    <Check className="h-3 w-3 mx-auto text-white drop-shadow-sm" />
                  )}
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      <Separator className="mb-3" />

      {/* Custom Color */}
      <div className="mb-4">
        <Label className="text-xs text-gray-600 mb-2 block">Màu tùy chỉnh</Label>
        <div className="flex items-center gap-2">
          <div
            className="w-8 h-8 rounded border border-gray-300 flex-shrink-0"
            style={{ backgroundColor: isValidCustom ? customColor : '#ffffff' }}
          />
          <Input
            type="text"
            value={customColor}
            onChange={(e) => handleCustomColorChange(e.target.value)}
            placeholder="#ffffff"
            className={cn(
              "text-xs font-mono",
              !isValidCustom && "border-red-500 focus:border-red-500"
            )}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        {onRemoveColor && currentColor && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRemoveColor}
            className="flex-1 text-xs"
          >
            Xóa màu
          </Button>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
          className="flex-1 text-xs"
        >
          Đóng
        </Button>
      </div>
    </div>
  )
}
