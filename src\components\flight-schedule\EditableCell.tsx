import { useState, useEffect, useRef } from 'react'
import { Input } from '@/components/ui/input'

import { AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { z } from 'zod/v4'
import { ChangeIndicator } from './ChangeIndicator'
import { useHasFieldChanges } from '@/hooks/useFieldChanges'
import { HighlightedText } from '@/components/ui/highlighted-text'
import { getCellColorStyle } from '@/lib/cell-colors-service'

interface EditableCellProps {
  value: string | undefined
  onSave: (newValue: string) => Promise<void>
  onCancel?: () => void
  validation?: z.ZodSchema
  placeholder?: string
  className?: string
  disabled?: boolean
  type?: 'text' | 'time' | 'flight-time'
  flightId?: string
  fieldName?: string
  showChangeIndicator?: boolean
  searchTerm?: string // Add searchTerm prop for highlighting
  cellColor?: string // Add cellColor prop for background color
}

export const EditableCell: React.FC<EditableCellProps> = ({
  value = '',
  onSave,
  onCancel,
  validation,
  placeholder,
  className,
  disabled = false,
  type = 'text',
  flightId,
  fieldName,
  showChangeIndicator = true,
  searchTerm = '', // Add searchTerm prop
  cellColor // Add cellColor prop
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(value)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Get field changes for change indicator
  const { hasChanges, changes } = useHasFieldChanges(
    flightId || '',
    fieldName || ''
  )

  // Helper function để render staff name với styling cho first flight indicator và highlighting
  const renderStaffValue = (staffValue: string) => {
    // Check if this is a staff field
    const isStaffField = fieldName?.includes('staff');

    if (!isStaffField) {
      // For REG fields, apply highlighting if searchTerm exists
      const isRegField = fieldName?.includes('reg');
      if (isRegField && searchTerm) {
        return <HighlightedText text={staffValue} searchTerm={searchTerm} />;
      }
      return staffValue;
    }

    // Split staff names by "/" and render each name with appropriate styling
    const names = staffValue.split('/');

    return (
      <>
        {names.map((name, index) => (
          <span key={index}>
            <span className={name.includes('*') ? 'text-blue-600 font-medium' : 'text-gray-700'}>
              {searchTerm ? (
                <HighlightedText text={name} searchTerm={searchTerm} />
              ) : (
                name
              )}
            </span>
            {index < names.length - 1 && <span className="text-gray-700">/</span>}
          </span>
        ))}
      </>
    );
  }

  useEffect(() => {
    setEditValue(value)
  }, [value])

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  const validateValue = (val: string): string | null => {
    if (!validation) return null

    try {
      // Handle empty string for optional fields
      const processedValue = val === '' ? undefined : val.trim()

      // For staff fields, allow empty values
      if (fieldName?.includes('staff') && !processedValue) {
        return null
      }

      validation.parse(processedValue)
      return null
    } catch (err) {
      if (err instanceof z.ZodError) {
        return err.errors[0]?.message || 'Giá trị không hợp lệ'
      }
      return 'Giá trị không hợp lệ'
    }
  }

  const handleStartEdit = () => {
    if (disabled) return
    setIsEditing(true)
    setError(null)
  }

  const handleSave = async () => {
    const validationError = validateValue(editValue || '')
    if (validationError) {
      setError(validationError)
      return
    }

    setIsSaving(true)
    setError(null)

    try {
      await onSave(editValue || '')
      setIsEditing(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Lỗi khi lưu dữ liệu')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setEditValue(value)
    setIsEditing(false)
    setError(null)
    onCancel?.()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancel()
    }
  }

  const handleBlur = () => {
    // Auto-save on blur if value changed
    if (editValue !== value && !error) {
      handleSave()
    } else {
      handleCancel()
    }
  }

  // Get cell color styles with CSS custom properties
  const cellColorStyles: React.CSSProperties = cellColor ? {
    ...getCellColorStyle(cellColor),
    '--cell-bg-color': cellColor,
    '--cell-text-color': getCellColorStyle(cellColor).color || '#000000'
  } as React.CSSProperties : {}

  if (!isEditing) {
    // Debug log for cell colors
    if (process.env.NODE_ENV === 'development' && cellColor) {
      console.log(`EditableCell - Applying color ${cellColor} to ${flightId}:${fieldName}`)
    }

    return (
      <div
        className={cn(
          'min-h-[32px] px-2 py-1 rounded cursor-pointer transition-colors',
          'border border-transparent hover:border-gray-200',
          'flex items-center justify-between gap-2',
          disabled && 'cursor-not-allowed opacity-50',
          // Only apply hover if no custom cell color
          !cellColor && 'hover:bg-gray-50',
          className
        )}
        style={cellColor ? cellColorStyles : {}}
        onClick={handleStartEdit}
        title={disabled ? 'Không thể chỉnh sửa' : 'Nhấp để chỉnh sửa'}
      >
        <span className="flex-1">
          {value ? renderStaffValue(value) : (
            <span className="text-gray-400 italic">
              {placeholder || 'Nhấp để thêm...'}
            </span>
          )}
        </span>

        {/* Change Indicator */}
        {showChangeIndicator && hasChanges && flightId && fieldName && (
          <ChangeIndicator
            changes={changes}
            className="flex-shrink-0"
          />
        )}
      </div>
    )
  }

  return (
    <div className="relative">
      <Input
        ref={inputRef}
        type={type === 'flight-time' ? 'text' : type}
        value={editValue}
        onChange={(e) => {
          setEditValue(e.target.value)
          setError(null)
        }}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={cn(
          'h-8 text-sm',
          error && 'border-red-500 focus:border-red-500'
        )}
        disabled={isSaving}
      />

      {error && (
        <div className="absolute top-full left-0 mt-1 flex items-center gap-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded border border-red-200 z-10">
          <AlertCircle className="h-3 w-3" />
          {error}
        </div>
      )}
    </div>
  )
}
