import React, { useState, useMemo } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Search, Replace, AlertCircle, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

// Helper functions for advanced name matching and replacement
const escapeRegExp = (string: string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

const findNameInCombinedField = (fieldValue: string, searchName: string, matchCase: boolean = false) => {
  if (!fieldValue || !searchName) return false

  // Handle asterisk (*) in names by escaping it properly
  const escapedSearchName = escapeRegExp(searchName)

  // Create regex for word boundary matching that handles "/" separator and asterisk
  const flags = matchCase ? 'g' : 'gi'
  const regex = new RegExp(`(^|/)\\s*(${escapedSearchName})(\\*?)\\s*(?=/|$)`, flags)

  return regex.test(fieldValue)
}

const replaceNameInCombinedField = (fieldValue: string, searchName: string, replaceName: string, matchCase: boolean = false) => {
  if (!fieldValue || !searchName) return fieldValue

  // Handle asterisk (*) in names by escaping it properly
  const escapedSearchName = escapeRegExp(searchName)

  // Create regex for word boundary matching that handles "/" separator and asterisk
  const flags = matchCase ? 'g' : 'gi'
  const regex = new RegExp(`(^|/)\\s*(${escapedSearchName})(\\*?)\\s*(?=/|$)`, flags)

  return fieldValue.replace(regex, (match, prefix, name, asterisk) => {
    // Preserve the prefix (start of string or "/") and any asterisk
    const newName = replaceName + asterisk
    return prefix + newName
  })
}

const getPreviewReplacement = (currentValue: string, searchName: string, replaceName: string, matchCase: boolean = false) => {
  return replaceNameInCombinedField(currentValue, searchName, replaceName, matchCase)
}

type FlightData = {
  id: string
  date: string
  stt: number
  arr_flt?: string
  arr_staff?: string
  arr_reg?: string
  dep_flt?: string
  dep_staff?: string
  dep_reg?: string
  remark?: string
}

type ScopeType = 'staff' | 'reg' | 'both'
type OperationType = 'replace' | 'swap'

interface FindReplaceMatch {
  flightId: string
  field: string
  currentValue: string
  newValue?: string // For preview purposes
  stt: number
  flightNumber: string
}

interface FindReplaceModalProps {
  isOpen: boolean
  onClose: () => void
  flights: FlightData[]
  onReplace: (matches: FindReplaceMatch[], replaceValue: string) => Promise<void>
  onSwap?: (valueA: string, valueB: string, scope: ScopeType) => Promise<void>
}

export const FindReplaceModal: React.FC<FindReplaceModalProps> = ({
  isOpen,
  onClose,
  flights,
  onReplace,
  onSwap,
}) => {
  const [findValue, setFindValue] = useState('')
  const [replaceValue, setReplaceValue] = useState('')
  const [scope, setScope] = useState<ScopeType>('staff')
  const [operationType, setOperationType] = useState<OperationType>('replace')
  const [matchCase, setMatchCase] = useState(false)
  const [matchWholeWord, setMatchWholeWord] = useState(true) // Enable by default for better name handling
  const [isReplacing, setIsReplacing] = useState(false)

  // Tìm kiếm matches cho Replace mode
  const matches = useMemo(() => {
    if (!findValue.trim() || operationType === 'swap') return []

    const searchValue = matchCase ? findValue : findValue.toLowerCase()
    const results: FindReplaceMatch[] = []

    flights.forEach((flight) => {
      const fieldsToSearch: Array<{ key: keyof FlightData; label: string }> = []

      // Xác định fields cần tìm kiếm dựa trên scope
      if (scope === 'staff' || scope === 'both') {
        fieldsToSearch.push(
          { key: 'arr_staff', label: 'ARR STAFF' },
          { key: 'dep_staff', label: 'DEP STAFF' }
        )
      }
      if (scope === 'reg' || scope === 'both') {
        fieldsToSearch.push(
          { key: 'arr_reg', label: 'ARR REG' },
          { key: 'dep_reg', label: 'DEP REG' }
        )
      }

      fieldsToSearch.forEach(({ key, label }) => {
        const fieldValue = flight[key] as string
        if (!fieldValue) return

        let isMatch = false

        if (matchWholeWord) {
          // Use advanced name matching for combined fields and asterisk handling
          isMatch = findNameInCombinedField(fieldValue, findValue, matchCase)
        } else {
          // Tìm kiếm substring (fallback for non-whole-word searches)
          const compareValue = matchCase ? fieldValue : fieldValue.toLowerCase()
          isMatch = compareValue.includes(searchValue)
        }

        if (isMatch) {
          const newValue = matchWholeWord
            ? getPreviewReplacement(fieldValue, findValue, replaceValue, matchCase)
            : replaceValue

          results.push({
            flightId: flight.id,
            field: key,
            currentValue: fieldValue,
            newValue,
            stt: flight.stt,
            flightNumber: flight.arr_flt || flight.dep_flt || `STT ${flight.stt}`,
          })
        }
      })
    })

    return results
  }, [findValue, scope, matchCase, matchWholeWord, flights, operationType])

  // Tìm kiếm matches cho Swap mode
  const swapMatches = useMemo(() => {
    if (operationType !== 'swap' || !findValue.trim() || !replaceValue.trim()) return { valueAMatches: [], valueBMatches: [] }

    const searchValueA = matchCase ? findValue : findValue.toLowerCase()
    const searchValueB = matchCase ? replaceValue : replaceValue.toLowerCase()
    const valueAMatches: FindReplaceMatch[] = []
    const valueBMatches: FindReplaceMatch[] = []

    flights.forEach((flight) => {
      const fieldsToSearch: Array<{ key: keyof FlightData; label: string }> = []

      // Xác định fields cần tìm kiếm dựa trên scope
      if (scope === 'staff' || scope === 'both') {
        fieldsToSearch.push(
          { key: 'arr_staff', label: 'ARR STAFF' },
          { key: 'dep_staff', label: 'DEP STAFF' }
        )
      }
      if (scope === 'reg' || scope === 'both') {
        fieldsToSearch.push(
          { key: 'arr_reg', label: 'ARR REG' },
          { key: 'dep_reg', label: 'DEP REG' }
        )
      }

      fieldsToSearch.forEach(({ key, label }) => {
        const fieldValue = flight[key] as string
        if (!fieldValue) return

        // Check for Value A matches
        let isMatchA = false
        let isMatchB = false

        if (matchWholeWord) {
          // Use advanced name matching for combined fields and asterisk handling
          isMatchA = findNameInCombinedField(fieldValue, findValue, matchCase)
          isMatchB = findNameInCombinedField(fieldValue, replaceValue, matchCase)
        } else {
          const compareValue = matchCase ? fieldValue : fieldValue.toLowerCase()
          isMatchA = compareValue.includes(searchValueA)
          isMatchB = compareValue.includes(searchValueB)
        }

        if (isMatchA) {
          const newValue = matchWholeWord
            ? getPreviewReplacement(fieldValue, findValue, replaceValue, matchCase)
            : replaceValue

          valueAMatches.push({
            flightId: flight.id,
            field: key,
            currentValue: fieldValue,
            newValue,
            stt: flight.stt,
            flightNumber: flight.arr_flt || flight.dep_flt || `STT ${flight.stt}`,
          })
        }

        if (isMatchB) {
          const newValue = matchWholeWord
            ? getPreviewReplacement(fieldValue, replaceValue, findValue, matchCase)
            : findValue

          valueBMatches.push({
            flightId: flight.id,
            field: key,
            currentValue: fieldValue,
            newValue,
            stt: flight.stt,
            flightNumber: flight.arr_flt || flight.dep_flt || `STT ${flight.stt}`,
          })
        }
      })
    })

    return { valueAMatches, valueBMatches }
  }, [findValue, replaceValue, scope, matchCase, matchWholeWord, flights, operationType])

  const handleReplace = async (replaceAll: boolean = false) => {
    if (operationType === 'replace') {
      if (!replaceValue.trim() || matches.length === 0) return

      setIsReplacing(true)
      try {
        const matchesToReplace = replaceAll ? matches : matches.slice(0, 1)
        await onReplace(matchesToReplace, replaceValue)

        // Reset form sau khi thành công
        setFindValue('')
        setReplaceValue('')
        onClose()
      } catch (error) {
        console.error('Error replacing values:', error)
      } finally {
        setIsReplacing(false)
      }
    } else if (operationType === 'swap') {
      if (!findValue.trim() || !replaceValue.trim() || !onSwap) return

      setIsReplacing(true)
      try {
        await onSwap(findValue, replaceValue, scope)

        // Reset form sau khi thành công
        setFindValue('')
        setReplaceValue('')
        onClose()
      } catch (error) {
        console.error('Error swapping values:', error)
      } finally {
        setIsReplacing(false)
      }
    }
  }

  const handleClose = () => {
    setFindValue('')
    setReplaceValue('')
    setOperationType('replace')
    setIsReplacing(false)
    onClose()
  }

  const getScopeLabel = (scopeValue: ScopeType) => {
    switch (scopeValue) {
      case 'staff': return 'Cột STAFF (Nhân viên)'
      case 'reg': return 'Cột REG (Số hiệu máy bay)'
      case 'both': return 'Cả STAFF và REG'
      default: return ''
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>{operationType === 'swap' ? 'Swap Values' : 'Find & Replace'}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Operation Type Selection */}
          <div className="space-y-2">
            <Label>Loại thao tác</Label>
            <Select value={operationType} onValueChange={(value: OperationType) => setOperationType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="replace">Find & Replace (Tìm và thay thế)</SelectItem>
                <SelectItem value="swap">Swap Values (Hoán đổi giá trị)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Find & Replace/Swap Inputs */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="find-input">
                {operationType === 'swap' ? 'Giá trị A' : 'Tìm kiếm'}
              </Label>
              <Input
                id="find-input"
                placeholder={operationType === 'swap' ? 'Nhập giá trị A (VD: Minh)' : 'Nhập giá trị cần tìm...'}
                value={findValue}
                onChange={(e) => setFindValue(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="replace-input">
                {operationType === 'swap' ? 'Giá trị B' : 'Thay thế bằng'}
              </Label>
              <Input
                id="replace-input"
                placeholder={operationType === 'swap' ? 'Nhập giá trị B (VD: Tu)' : 'Nhập giá trị mới...'}
                value={replaceValue}
                onChange={(e) => setReplaceValue(e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {operationType === 'swap' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="bg-blue-500 rounded-full p-1 mt-0.5">
                  <Replace className="h-3 w-3 text-white" />
                </div>
                <div className="text-sm text-blue-800">
                  <p className="font-medium">Chế độ hoán đổi:</p>
                  <p>Tất cả "{findValue || 'A'}" sẽ thành "{replaceValue || 'B'}" và ngược lại trong một thao tác duy nhất.</p>
                </div>
              </div>
            </div>
          )}

          {/* Scope Selection */}
          <div className="space-y-2">
            <Label>Phạm vi tìm kiếm</Label>
            <Select value={scope} onValueChange={(value: ScopeType) => setScope(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="staff">Cột STAFF (Nhân viên)</SelectItem>
                <SelectItem value="reg">Cột REG (Số hiệu máy bay)</SelectItem>
                <SelectItem value="both">Cả STAFF và REG</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Options */}
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-6">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="match-case"
                checked={matchCase}
                onCheckedChange={(checked) => setMatchCase(checked as boolean)}
              />
              <Label htmlFor="match-case" className="text-sm">
                Phân biệt hoa thường
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="match-whole-word"
                checked={matchWholeWord}
                onCheckedChange={(checked) => setMatchWholeWord(checked as boolean)}
              />
              <Label htmlFor="match-whole-word" className="text-sm">
                Khớp tên riêng lẻ (xử lý "/" và "*")
              </Label>
            </div>
          </div>

          {matchWholeWord && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="bg-amber-500 rounded-full p-1 mt-0.5">
                  <AlertCircle className="h-3 w-3 text-white" />
                </div>
                <div className="text-sm text-amber-800">
                  <p className="font-medium">Chế độ khớp tên riêng lẻ:</p>
                  <p>• Xử lý tên kết hợp với "/" (VD: "TÚ/NAM" → "MINH/NAM")</p>
                  <p>• Bảo toàn ký hiệu "*" cho chuyến bay đầu tiên</p>
                  <p>• Chỉ thay thế tên chính xác, không ảnh hưởng tên khác</p>
                </div>
              </div>
            </div>
          )}

          {/* Preview Results */}
          {findValue && (
            <div className="space-y-3">
              {operationType === 'replace' ? (
                <>
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">
                      Kết quả tìm kiếm ({matches.length} kết quả)
                    </Label>
                    <Badge variant={matches.length > 0 ? "default" : "secondary"}>
                      {getScopeLabel(scope)}
                    </Badge>
                  </div>

                  {matches.length > 0 ? (
                    <div className="max-h-40 overflow-y-auto border rounded-md p-3 bg-gray-50">
                      <div className="space-y-2">
                        {matches.map((match, index) => (
                          <div
                            key={`${match.flightId}-${match.field}-${index}`}
                            className="flex items-center justify-between text-sm bg-white p-2 rounded border"
                          >
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className="text-xs">
                                STT {match.stt}
                              </Badge>
                              <span className="text-gray-600">{match.field.toUpperCase()}:</span>
                              <span className="font-medium">{match.currentValue}</span>
                            </div>
                            <div className="flex items-center space-x-1 text-xs text-gray-500">
                              <Replace className="h-3 w-3" />
                              <span>{match.newValue || replaceValue || '...'}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      <p>Không tìm thấy kết quả nào</p>
                    </div>
                  )}
                </>
              ) : (
                // Swap mode preview
                replaceValue && (
                  <>
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">
                        Xem trước hoán đổi
                      </Label>
                      <Badge variant="default">
                        {getScopeLabel(scope)}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Value A matches */}
                      <div className="space-y-2">
                        <div className="text-sm font-medium text-blue-600">
                          "{findValue}" → "{replaceValue}" ({swapMatches.valueAMatches.length} kết quả)
                        </div>
                        {swapMatches.valueAMatches.length > 0 ? (
                          <div className="max-h-32 overflow-y-auto border rounded-md p-2 bg-blue-50">
                            <div className="space-y-1">
                              {swapMatches.valueAMatches.map((match, index) => (
                                <div
                                  key={`a-${match.flightId}-${match.field}-${index}`}
                                  className="flex items-center space-x-2 text-xs bg-white p-1 rounded"
                                >
                                  <Badge variant="outline" className="text-xs">
                                    STT {match.stt}
                                  </Badge>
                                  <span className="text-gray-600">{match.field.toUpperCase()}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div className="text-xs text-gray-500 italic">Không có kết quả</div>
                        )}
                      </div>

                      {/* Value B matches */}
                      <div className="space-y-2">
                        <div className="text-sm font-medium text-green-600">
                          "{replaceValue}" → "{findValue}" ({swapMatches.valueBMatches.length} kết quả)
                        </div>
                        {swapMatches.valueBMatches.length > 0 ? (
                          <div className="max-h-32 overflow-y-auto border rounded-md p-2 bg-green-50">
                            <div className="space-y-1">
                              {swapMatches.valueBMatches.map((match, index) => (
                                <div
                                  key={`b-${match.flightId}-${match.field}-${index}`}
                                  className="flex items-center space-x-2 text-xs bg-white p-1 rounded"
                                >
                                  <Badge variant="outline" className="text-xs">
                                    STT {match.stt}
                                  </Badge>
                                  <span className="text-gray-600">{match.field.toUpperCase()}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div className="text-xs text-gray-500 italic">Không có kết quả</div>
                        )}
                      </div>
                    </div>

                    {(swapMatches.valueAMatches.length === 0 && swapMatches.valueBMatches.length === 0) && (
                      <div className="text-center py-6 text-gray-500">
                        <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p>Không tìm thấy giá trị nào để hoán đổi</p>
                      </div>
                    )}
                  </>
                )
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
          <Button variant="outline" onClick={handleClose} disabled={isReplacing}>
            Hủy
          </Button>

          {operationType === 'replace' ? (
            <>
              <Button
                variant="outline"
                onClick={() => handleReplace(false)}
                disabled={!findValue || !replaceValue || matches.length === 0 || isReplacing}
              >
                {isReplacing ? 'Đang xử lý...' : 'Thay thế đầu tiên'}
              </Button>
              <Button
                onClick={() => handleReplace(true)}
                disabled={!findValue || !replaceValue || matches.length === 0 || isReplacing}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isReplacing ? 'Đang xử lý...' : `Thay thế tất cả (${matches.length})`}
              </Button>
            </>
          ) : (
            <Button
              onClick={() => handleReplace(true)}
              disabled={
                !findValue ||
                !replaceValue ||
                (swapMatches.valueAMatches.length === 0 && swapMatches.valueBMatches.length === 0) ||
                isReplacing ||
                !onSwap
              }
              className="bg-green-600 hover:bg-green-700"
            >
              {isReplacing ? 'Đang hoán đổi...' : `Hoán đổi (${swapMatches.valueAMatches.length + swapMatches.valueBMatches.length} thay đổi)`}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
