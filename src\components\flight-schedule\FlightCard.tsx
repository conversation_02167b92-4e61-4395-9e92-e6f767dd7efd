import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { EditableCell } from "./EditableCell";
import { HighlightedText } from "@/components/ui/highlighted-text";
import { formatFlightTimeVN } from "@/lib/timezone-utils";
import { editableFieldSchemas, type EditableField } from "@/lib/flight-validation";
import { useCellColor } from "@/hooks/useCellColors";
import { getCellColorStyle } from "@/lib/cell-colors-service";
import {
  Plane,
  MapPin,
  Clock,
  User,
  UserCheck,
  CheckCircle,
  Users,
  ArrowRight
} from "lucide-react";

type FlightData = {
  id: string;
  date: string;
  stt: number;
  arr_flt?: string;
  arr_from?: string;
  arr_reg?: string;
  arr_time?: string;
  arr_staff?: string;
  arr_present?: boolean;
  arr_finished?: boolean;
  dep_flt?: string;
  dep_to?: string;
  dep_reg?: string;
  dep_time?: string;
  dep_staff?: string;
  dep_present?: boolean;
  dep_boarded?: boolean;
  dep_finished?: boolean;
  remark?: string;
};

interface FlightCardProps {
  flight: FlightData;
  onStatusUpdate?: (flightId: string, field: string, value: boolean) => void;
  showStatusControls?: boolean;
  isPublicView?: boolean;
  searchTerm?: string;
  onCellUpdate?: (flightId: string, field: EditableField, value: string) => void;
  isUpdating?: boolean;
}

export const FlightCard = ({
  flight,
  onStatusUpdate,
  showStatusControls = false,
  isPublicView = false,
  searchTerm = "",
  onCellUpdate,
  isUpdating = false
}: FlightCardProps) => {
  const handleStatusClick = (field: string, currentValue: boolean) => {
    if (!onStatusUpdate) return;
    
    // Sequential status logic
    if (field === 'arr_present' && !currentValue) {
      onStatusUpdate(flight.id, field, true);
    } else if (field === 'arr_finished' && flight.arr_present && !currentValue) {
      onStatusUpdate(flight.id, field, true);
    } else if (field === 'dep_present' && !currentValue) {
      onStatusUpdate(flight.id, field, true);
    } else if (field === 'dep_boarded' && flight.dep_present && !currentValue) {
      onStatusUpdate(flight.id, field, true);
    } else if (field === 'dep_finished' && flight.dep_boarded && !currentValue) {
      onStatusUpdate(flight.id, field, true);
    } else if (currentValue) {
      // Allow reverse unchecking
      onStatusUpdate(flight.id, field, false);
    }
  };

  const renderStatusButton = (
    field: string,
    currentValue: boolean,
    label: string,
    icon: React.ReactNode,
    disabled: boolean = false
  ) => {
    if (isPublicView) return null;
    
    return (
      <Button
        variant={currentValue ? "default" : "outline"}
        size="sm"
        onClick={() => handleStatusClick(field, currentValue)}
        disabled={disabled || isUpdating}
        className="h-8 px-2 text-xs"
      >
        {icon}
        <span className="ml-1">{label}</span>
      </Button>
    );
  };

  const renderEditableField = (
    value: string | undefined,
    field: EditableField,
    placeholder: string
  ) => {
    // Get cell color for this field - always fetch for both admin and public view
    const { cellColor } = useCellColor(flight.id, field, true)

    if (isPublicView) {
      // For public view, show colored background but no editing capability
      const cellColorStyles = cellColor ? {
        backgroundColor: cellColor,
        color: getCellColorStyle(cellColor).color || '#000000',
        padding: '4px 8px',
        borderRadius: '4px'
      } : {}

      return (
        <div style={cellColorStyles}>
          <HighlightedText
            text={value || ""}
            searchTerm={searchTerm}
            className="text-sm"
          />
        </div>
      );
    }

    return (
      <EditableCell
        value={value || ""}
        onSave={(newValue) => onCellUpdate?.(flight.id, field, newValue)}
        validation={editableFieldSchemas[field]}
        placeholder={placeholder}
        className="text-sm"
        searchTerm={searchTerm}
        cellColor={cellColor}
        flightId={flight.id}
        fieldName={field}
      />
    );
  };

  const formatStaffName = (staff: string | undefined) => {
    if (!staff) return "";
    
    // Handle staff names with asterisk (first flight indicator)
    if (staff.includes("*")) {
      return (
        <span className="text-blue-600 font-medium">
          <HighlightedText text={staff} searchTerm={searchTerm} />
        </span>
      );
    }
    
    return <HighlightedText text={staff} searchTerm={searchTerm} />;
  };

  return (
    <Card className="mb-4 shadow-sm border border-gray-200">
      <CardContent className="p-4">
        {/* Header with STT */}
        <div className="flex items-center justify-between mb-3">
          <Badge variant="outline" className="text-xs">
            STT: {flight.stt}
          </Badge>
        </div>

        {/* Flight Information */}
        <div className="space-y-3">
          {/* Arrival Flight */}
          {flight.arr_flt && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Plane className="h-4 w-4 text-blue-600 rotate-180" />
                  <span className="font-medium text-blue-800">
                    <HighlightedText text={flight.arr_flt} searchTerm={searchTerm} />
                  </span>
                </div>
                <Badge variant="outline" className="text-xs bg-white">
                  Đến
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3 text-gray-500" />
                  <span className="text-gray-600">Từ:</span>
                  <span className="font-medium">{flight.arr_from}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3 text-gray-500" />
                  <span className="text-gray-600">Giờ:</span>
                  <span className="font-medium">
                    {flight.arr_time ? formatFlightTimeVN(flight.arr_time) : ""}
                  </span>
                </div>
              </div>

              <div className="mt-2 space-y-1">
                <div className="flex items-center space-x-1">
                  <span className="text-xs text-gray-600 w-8">REG:</span>
                  {renderEditableField(flight.arr_reg, "arr_reg", "REG")}
                </div>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3 text-gray-500" />
                  <span className="text-xs text-gray-600 w-8">NV:</span>
                  <div className="flex-1">
                    {renderEditableField(flight.arr_staff, "arr_staff", "Nhân viên")}
                  </div>
                </div>
              </div>

              {/* Arrival Status Controls */}
              {showStatusControls && (
                <div className="flex space-x-2 mt-3">
                  {renderStatusButton(
                    "arr_present",
                    flight.arr_present || false,
                    "Có mặt",
                    <UserCheck className="h-3 w-3" />
                  )}
                  {renderStatusButton(
                    "arr_finished",
                    flight.arr_finished || false,
                    "Hoàn thành",
                    <CheckCircle className="h-3 w-3" />,
                    !flight.arr_present
                  )}
                </div>
              )}
            </div>
          )}

          {/* Departure Flight */}
          {flight.dep_flt && (
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Plane className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-800">
                    <HighlightedText text={flight.dep_flt} searchTerm={searchTerm} />
                  </span>
                </div>
                <Badge variant="outline" className="text-xs bg-white">
                  Đi
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3 text-gray-500" />
                  <span className="text-gray-600">Đến:</span>
                  <span className="font-medium">{flight.dep_to}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3 text-gray-500" />
                  <span className="text-gray-600">Giờ:</span>
                  <span className="font-medium">
                    {flight.dep_time ? formatFlightTimeVN(flight.dep_time) : ""}
                  </span>
                </div>
              </div>

              <div className="mt-2 space-y-1">
                <div className="flex items-center space-x-1">
                  <span className="text-xs text-gray-600 w-8">REG:</span>
                  {renderEditableField(flight.dep_reg, "dep_reg", "REG")}
                </div>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3 text-gray-500" />
                  <span className="text-xs text-gray-600 w-8">NV:</span>
                  <div className="flex-1">
                    {renderEditableField(flight.dep_staff, "dep_staff", "Nhân viên")}
                  </div>
                </div>
              </div>

              {/* Departure Status Controls */}
              {showStatusControls && (
                <div className="flex space-x-2 mt-3">
                  {renderStatusButton(
                    "dep_present",
                    flight.dep_present || false,
                    "Có mặt",
                    <UserCheck className="h-3 w-3" />
                  )}
                  {renderStatusButton(
                    "dep_boarded",
                    flight.dep_boarded || false,
                    "Lên máy bay",
                    <Users className="h-3 w-3" />,
                    !flight.dep_present
                  )}
                  {renderStatusButton(
                    "dep_finished",
                    flight.dep_finished || false,
                    "Hoàn thành",
                    <CheckCircle className="h-3 w-3" />,
                    !flight.dep_boarded
                  )}
                </div>
              )}
            </div>
          )}

          {/* Remark Field */}
          {(flight.remark || !isPublicView) && (
            <div className="bg-gray-50 p-2 rounded">
              <div className="flex items-center space-x-1">
                <span className="text-xs text-gray-600 font-medium">Ghi chú:</span>
                {renderEditableField(flight.remark, "remark", "Thêm ghi chú...")}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
