import React, { useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { ColorPicker } from './ColorPicker'
import { 
  Palette, 
  Plus, 
  Minus, 
  ArrowUp, 
  ArrowDown,
  Plane,
  PlaneLanding,
  PlaneTakeoff
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CellContextInfo {
  flightId: string
  fieldName: string
  currentColor?: string
}

interface RowContextInfo {
  flightId: string
  rowType: 'arrival' | 'departure'
  stt: number
}

interface FlightContextMenuProps {
  isOpen: boolean
  position: { x: number; y: number }
  onClose: () => void
  contextType: 'cell' | 'row'
  cellInfo?: CellContextInfo
  rowInfo?: RowContextInfo
  onColorChange?: (color: string) => void
  onRemoveColor?: () => void
  onInsertRow?: (type: 'arrival' | 'departure', position: 'before' | 'after') => void
  onDeleteRow?: (type: 'arrival' | 'departure') => void
}

export const FlightContextMenu: React.FC<FlightContextMenuProps> = ({
  isOpen,
  position,
  onClose,
  contextType,
  cellInfo,
  rowInfo,
  onColorChange,
  onRemoveColor,
  onInsertRow,
  onDeleteRow
}) => {
  const menuRef = useRef<HTMLDivElement>(null)
  const [showColorPicker, setShowColorPicker] = React.useState(false)

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showColorPicker) {
          setShowColorPicker(false)
        } else {
          onClose()
        }
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, showColorPicker, onClose])

  // Calculate menu position to keep it within viewport
  const getMenuPosition = () => {
    const menuWidth = 200
    const menuHeight = contextType === 'cell' ? 120 : 200
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let x = position.x
    let y = position.y

    // Adjust horizontal position
    if (x + menuWidth > viewportWidth) {
      x = viewportWidth - menuWidth - 10
    }

    // Adjust vertical position
    if (y + menuHeight > viewportHeight) {
      y = viewportHeight - menuHeight - 10
    }

    // Ensure minimum distance from edges
    x = Math.max(10, x)
    y = Math.max(10, y)

    return { x, y }
  }

  const menuPosition = getMenuPosition()

  if (!isOpen) return null

  const handleColorPickerClose = () => {
    setShowColorPicker(false)
  }

  const handleColorSelect = (color: string) => {
    onColorChange?.(color)
    setShowColorPicker(false)
    onClose()
  }

  const handleRemoveColor = () => {
    onRemoveColor?.()
    onClose()
  }

  const handleInsertRow = (position: 'before' | 'after') => {
    if (rowInfo) {
      onInsertRow?.(rowInfo.rowType, position)
    }
    onClose()
  }

  const handleDeleteRow = () => {
    if (rowInfo) {
      onDeleteRow?.(rowInfo.rowType)
    }
    onClose()
  }

  // Render cell context menu
  if (contextType === 'cell') {
    return createPortal(
      <div
        ref={menuRef}
        className="fixed z-50"
        style={{
          left: `${menuPosition.x}px`,
          top: `${menuPosition.y}px`,
        }}
      >
        {showColorPicker ? (
          <ColorPicker
            currentColor={cellInfo?.currentColor}
            onColorSelect={handleColorSelect}
            onClose={handleColorPickerClose}
            onRemoveColor={cellInfo?.currentColor ? handleRemoveColor : undefined}
          />
        ) : (
          <div className="bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[180px]">
            <div className="px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-100">
              Tùy chọn ô
            </div>
            
            <button
              onClick={() => setShowColorPicker(true)}
              className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
            >
              <Palette className="h-4 w-4 text-gray-600" />
              Đổi màu ô
            </button>

            {cellInfo?.currentColor && (
              <button
                onClick={handleRemoveColor}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
              >
                <Minus className="h-4 w-4" />
                Xóa màu
              </button>
            )}
          </div>
        )}
      </div>,
      document.body
    )
  }

  // Render row context menu
  if (contextType === 'row') {
    const isArrival = rowInfo?.rowType === 'arrival'
    const Icon = isArrival ? PlaneLanding : PlaneTakeoff
    const typeLabel = isArrival ? 'Đến' : 'Đi'

    return createPortal(
      <div
        ref={menuRef}
        className="fixed z-50"
        style={{
          left: `${menuPosition.x}px`,
          top: `${menuPosition.y}px`,
        }}
      >
        <div className="bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[200px]">
          <div className="px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-100 flex items-center gap-2">
            <Icon className="h-3 w-3" />
            Tùy chọn hàng {typeLabel}
          </div>
          
          <button
            onClick={() => handleInsertRow('before')}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            <ArrowUp className="h-4 w-4 text-green-600" />
            Chèn hàng phía trên
          </button>

          <button
            onClick={() => handleInsertRow('after')}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            <ArrowDown className="h-4 w-4 text-green-600" />
            Chèn hàng phía dưới
          </button>

          <div className="border-t border-gray-100 my-1" />

          <button
            onClick={handleDeleteRow}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
          >
            <Minus className="h-4 w-4" />
            Xóa hàng {typeLabel}
          </button>
        </div>
      </div>,
      document.body
    )
  }

  return null
}

// Hook để manage context menu state
export const useFlightContextMenu = () => {
  const [contextMenu, setContextMenu] = React.useState<{
    isOpen: boolean
    position: { x: number; y: number }
    type: 'cell' | 'row'
    cellInfo?: CellContextInfo
    rowInfo?: RowContextInfo
  }>({
    isOpen: false,
    position: { x: 0, y: 0 },
    type: 'cell'
  })

  const openCellContextMenu = (
    event: React.MouseEvent,
    cellInfo: CellContextInfo
  ) => {
    event.preventDefault()
    event.stopPropagation()
    
    setContextMenu({
      isOpen: true,
      position: { x: event.clientX, y: event.clientY },
      type: 'cell',
      cellInfo
    })
  }

  const openRowContextMenu = (
    event: React.MouseEvent,
    rowInfo: RowContextInfo
  ) => {
    event.preventDefault()
    event.stopPropagation()
    
    setContextMenu({
      isOpen: true,
      position: { x: event.clientX, y: event.clientY },
      type: 'row',
      rowInfo
    })
  }

  const closeContextMenu = () => {
    setContextMenu(prev => ({ ...prev, isOpen: false }))
  }

  return {
    contextMenu,
    openCellContextMenu,
    openRowContextMenu,
    closeContextMenu
  }
}
