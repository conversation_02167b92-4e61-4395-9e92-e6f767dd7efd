import React from 'react'
import { Search, Calendar, Filter, Download, Upload, Plus, Replace } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Link } from '@tanstack/react-router'

interface FlightScheduleToolbarProps {
  searchTerm: string
  selectedDate: string
  onSearchChange: (term: string) => void
  onDateChange: (date: string) => void
  onExport?: () => void
  onAddFlight?: () => void
  onFilter?: () => void
  onFindReplace?: () => void
  placeholder?: string
  className?: string
}

export const FlightScheduleToolbar: React.FC<FlightScheduleToolbarProps> = ({
  searchTerm,
  selectedDate,
  onSearchChange,
  onDateChange,
  onExport,
  onAddFlight,
  onFilter,
  onFindReplace,
  placeholder = "T<PERSON><PERSON> kiếm nhân viên, s<PERSON> đăng ký... (phân cách bằng dấu phẩy)",
  className
}) => {
  return (
    <div className={cn("bg-white rounded-lg shadow-sm border", className)}>
      <div className="p-4">
        <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between space-y-4 xl:space-y-0 gap-4">
          {/* Left side - Search and date controls */}
          <div className="flex flex-col lg:flex-row items-start lg:items-center space-y-3 lg:space-y-0 lg:space-x-4 flex-1">
            {/* Search input */}
            <div className="relative w-full lg:w-auto lg:min-w-[280px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={placeholder}
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 w-full"
              />
            </div>
            
            {/* Date picker */}
            <div className="flex items-center space-x-2 w-full lg:w-auto">
              <Calendar className="h-4 w-4 text-gray-500 flex-shrink-0" />
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => onDateChange(e.target.value)}
                className="w-full lg:w-40"
              />
            </div>

            {/* Filter button */}
            <Button
              variant="outline"
              size="sm"
              onClick={onFilter}
              className="w-full lg:w-auto"
            >
              <Filter className="h-4 w-4 mr-2" />
              Bộ lọc
            </Button>

            {/* Find & Replace button */}
            <Button
              variant="outline"
              size="sm"
              onClick={onFindReplace}
              className="w-full lg:w-auto"
            >
              <Replace className="h-4 w-4 mr-2" />
              Find & Replace
            </Button>
          </div>

          {/* Right side - Action buttons */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 lg:space-x-3">
            {/* Import button */}
            <Link to="/flight-schedule/import" className="w-full sm:w-auto">
              <Button 
                variant="outline" 
                size="sm"
                className="w-full sm:w-auto"
              >
                <Upload className="h-4 w-4 mr-2" />
                Import Excel
              </Button>
            </Link>

            {/* Export button */}
            <Button 
              variant="outline" 
              size="sm"
              onClick={onExport}
              className="w-full sm:w-auto"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>

            {/* Add flights button */}
            <Button 
              size="sm" 
              onClick={onAddFlight}
              className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
            >
              <Plus className="h-4 w-4 mr-2" />
              Thêm chuyến bay
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
