import React, { Suspense } from 'react'
import { useFlights, useFlightStats } from '@/hooks/useFlights'
import { FlightQueryErrorBoundary } from './error-boundaries/QueryErrorBoundary'
import { FlightErrorBoundary } from './error-boundaries/FlightErrorBoundary'
import { 
  FlightTableSkeleton, 
  FlightStatsSkeleton,
  FlightSearchSkeleton 
} from './skeletons'
import { FlightTable } from './FlightTable'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { RefreshCw, AlertTriangle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface FlightScheduleWithErrorHandlingProps {
  date?: string
  showStatusControls?: boolean
  compact?: boolean
}

// Loading wrapper component
const LoadingWrapper: React.FC<{ 
  children: React.ReactNode
  fallback: React.ReactNode
}> = ({ children, fallback }) => {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  )
}

// Stats component with error handling
const FlightStatsWithErrorHandling: React.FC<{ date?: string }> = ({ date }) => {
  const { data: stats, isLoading, isError, error, refetch } = useFlightStats(date)
  const { toast } = useToast()

  const handleRetry = async () => {
    try {
      await refetch()
      toast({
        title: 'Thành công',
        description: 'Đã tải lại thống kê thành công'
      })
    } catch (err) {
      toast({
        title: 'Lỗi',
        description: 'Không thể tải lại thống kê',
        variant: 'destructive'
      })
    }
  }

  if (isLoading) {
    return <FlightStatsSkeleton />
  }

  if (isError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
            <div>
              <h3 className="font-medium text-red-700">Lỗi tải thống kê</h3>
              <p className="text-sm text-red-600 mt-1">
                {error?.message || 'Không thể tải dữ liệu thống kê'}
              </p>
            </div>
            <Button onClick={handleRetry} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Thử lại
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            Không có dữ liệu thống kê
          </div>
        </CardContent>
      </Card>
    )
  }

  // Render actual stats here
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Tổng chuyến bay</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalFlights}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Chuyến đến</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">{stats.arrivalCount}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Chuyến đi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.departureCount}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Nhân viên</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-purple-600">{stats.uniqueStaff}</div>
        </CardContent>
      </Card>
    </div>
  )
}

// Table component with error handling
const FlightTableWithErrorHandling: React.FC<{
  date?: string
  showStatusControls?: boolean
}> = ({ date, showStatusControls }) => {
  const {
    data: flightData,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useFlights({
    date,
    limit: 2000 // Increase limit to show all flights (up to 2000)
  })
  
  const { toast } = useToast()

  const handleRetry = async () => {
    try {
      await refetch()
      toast({
        title: 'Thành công',
        description: 'Đã tải lại dữ liệu thành công'
      })
    } catch (err) {
      toast({
        title: 'Lỗi',
        description: 'Không thể tải lại dữ liệu',
        variant: 'destructive'
      })
    }
  }

  if (isLoading) {
    return <FlightTableSkeleton showStatusControls={showStatusControls} />
  }

  if (isError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
            <div>
              <h3 className="font-medium text-red-700">Lỗi tải dữ liệu lịch bay</h3>
              <p className="text-sm text-red-600 mt-1">
                {error?.message || 'Không thể tải dữ liệu lịch bay'}
              </p>
            </div>
            <Button onClick={handleRetry} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Thử lại
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="relative">
      {isFetching && (
        <div className="absolute top-0 right-0 z-10">
          <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-lg text-sm flex items-center gap-2">
            <RefreshCw className="h-3 w-3 animate-spin" />
            Đang cập nhật...
          </div>
        </div>
      )}
      <FlightTable
        data={flightData?.data || []}
        showStatusControls={showStatusControls}
        date={date}
      />
    </div>
  )
}

// Main component with comprehensive error handling
export const FlightScheduleWithErrorHandling: React.FC<FlightScheduleWithErrorHandlingProps> = ({
  date,
  showStatusControls = false,
  compact = false
}) => {
  return (
    <FlightErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
      <div className="space-y-6">
        {/* Search/Filter Section */}
        <FlightQueryErrorBoundary compact={compact}>
          <LoadingWrapper fallback={<FlightSearchSkeleton />}>
            {/* SearchFilter component sẽ được thêm vào đây khi cần */}
            <div className="text-sm text-gray-600 p-4 bg-gray-50 rounded-lg">
              SearchFilter component đã được tạo và sẵn sàng sử dụng
            </div>
          </LoadingWrapper>
        </FlightQueryErrorBoundary>

        {/* Stats Section */}
        <FlightQueryErrorBoundary compact={compact}>
          <FlightStatsWithErrorHandling date={date} />
        </FlightQueryErrorBoundary>

        {/* Table Section */}
        <FlightQueryErrorBoundary compact={compact}>
          <FlightTableWithErrorHandling 
            date={date} 
            showStatusControls={showStatusControls}
          />
        </FlightQueryErrorBoundary>
      </div>
    </FlightErrorBoundary>
  )
}
