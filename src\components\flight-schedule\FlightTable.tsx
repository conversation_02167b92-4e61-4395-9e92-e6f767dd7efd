import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  createColumnHelper,
  flexRender,
} from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { EditableCell } from "./EditableCell";
import { useFlightMutations } from "@/hooks/useFlightMutations";
import { editableFieldSchemas, type EditableField } from "@/lib/flight-validation";
import { FlightTableLoadingOverlay } from "./FlightTableLoadingOverlay";
import { HighlightedText } from "@/components/ui/highlighted-text";
import { formatFlightTimeVN } from "@/lib/timezone-utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileFlightList } from "./MobileFlightList";
import { FlightContextMenu, useFlightContextMenu } from "./FlightContextMenu";
import { useCellColor } from "@/hooks/useCellColors";
import { useCellColorMutations } from "@/hooks/useCellColorMutations";
import { useRowOperations } from "@/hooks/useRowOperations";
import { getCellColorStyle } from "@/lib/cell-colors-service";
import {
  Plane,
  MapPin,
  Clock,
  User,
  UserCheck,
  CheckCircle,
  Users
} from "lucide-react";

type FlightData = {
  id: string;
  date: string;
  stt: number;
  arr_flt?: string;
  arr_from?: string;
  arr_reg?: string;
  arr_time?: string;
  arr_staff?: string;
  arr_present?: boolean;
  arr_finished?: boolean;
  dep_flt?: string;
  dep_to?: string;
  dep_reg?: string;
  dep_time?: string;
  dep_staff?: string;
  dep_present?: boolean;
  dep_boarded?: boolean;
  dep_finished?: boolean;
  remark?: string;
};

interface FlightTableProps {
  data: FlightData[];
  onStatusUpdate?: (flightId: string, field: string, value: boolean) => void;
  showStatusControls?: boolean;
  isPublicView?: boolean;
  searchTerm?: string; // Add searchTerm prop for highlighting
  date?: string; // Add date prop for row operations
}

const columnHelper = createColumnHelper<FlightData>();

// Helper component to fetch cell color individually (like mobile)
const EditableCellWithColor: React.FC<{
  value: string | undefined
  onSave: (newValue: string) => Promise<void>
  validation: any
  placeholder: string
  className: string
  flightId: string
  fieldName: string
  searchTerm: string
  type?: 'text' | 'time' | 'flight-time'
}> = ({ value, onSave, validation, placeholder, className, flightId, fieldName, searchTerm, type }) => {
  const { cellColor } = useCellColor(flightId, fieldName, true)

  return (
    <EditableCell
      value={value}
      onSave={onSave}
      validation={validation}
      placeholder={placeholder}
      className={className}
      flightId={flightId}
      fieldName={fieldName}
      searchTerm={searchTerm}
      cellColor={cellColor}
      type={type}
    />
  )
}

const FlightTable = ({ data, onStatusUpdate, showStatusControls = true, isPublicView = false, searchTerm = '', date }: FlightTableProps) => {
  // Call ALL hooks first before any conditional logic
  const { updateFlightField, isUpdating } = useFlightMutations()
  const { isMobile, isInitialized } = useIsMobile()

  // Cell colors hooks - use individual queries like mobile for consistency
  const { updateCellColor, deleteCellColor } = useCellColorMutations()

  // Helper function to get cell color using individual query (like mobile)
  const getCellColor = (flightId: string, fieldName: string): string | undefined => {
    // This will be handled by individual useCellColor hooks in each cell
    return undefined
  }

  // Row operations hooks
  const { insertFlightRowSafe, deleteFlightRowSafe, isLoading: isRowOperationLoading } = useRowOperations()

  // Context menu hook
  const { contextMenu, openCellContextMenu, openRowContextMenu, closeContextMenu } = useFlightContextMenu()

  // Helper function để handle field updates
  const handleFieldUpdate = async (flightId: string, field: EditableField, newValue: string, oldValue?: string) => {
    await updateFlightField.mutateAsync({
      flightId,
      field,
      value: newValue,
      oldValue
    })
  }

  // Context menu handlers
  const handleCellRightClick = (
    event: React.MouseEvent,
    flightId: string,
    fieldName: string
  ) => {
    // Only show context menu for editable fields and non-public view
    if (isPublicView) return

    const editableFields = ['arr_reg', 'arr_time', 'arr_staff', 'dep_reg', 'dep_time', 'dep_staff', 'remark']
    if (!editableFields.includes(fieldName)) return

    event.stopPropagation() // Prevent row context menu
    const currentColor = getCellColor(flightId, fieldName)

    openCellContextMenu(event, {
      flightId,
      fieldName,
      currentColor
    })
  }

  // Helper function to create cell wrapper with context menu
  const createEditableCellWrapper = (
    value: string | undefined,
    flightId: string,
    fieldName: string,
    onSave: (newValue: string) => Promise<void>,
    validation: any,
    placeholder: string,
    className: string,
    type?: 'text' | 'time' | 'flight-time'
  ) => {
    return (
      <div
        onContextMenu={(e) => handleCellRightClick(e, flightId, fieldName)}
      >
        <EditableCellWithColor
          value={value}
          onSave={onSave}
          validation={validation}
          placeholder={placeholder}
          className={className}
          flightId={flightId}
          fieldName={fieldName}
          searchTerm={searchTerm}
          type={type}
        />
      </div>
    )
  }

  // Helper component for public view cells with individual color fetching
  const PublicViewCellWithColor: React.FC<{
    value: string | undefined
    flightId: string
    fieldName: string
    className: string
    children: React.ReactNode
  }> = ({ value, flightId, fieldName, className, children }) => {
    const { cellColor } = useCellColor(flightId, fieldName, true)

    if (cellColor) {
      // When cell has color, use inline styles and remove conflicting text color classes
      const cellColorStyles = {
        backgroundColor: cellColor,
        color: getCellColorStyle(cellColor).color || '#000000',
        padding: '4px 8px',
        borderRadius: '4px'
      }

      // Remove text color classes that might conflict with inline styles
      const cleanClassName = className.replace(/text-\w+-\d+/g, '').trim()

      return (
        <div className={cleanClassName} style={cellColorStyles}>
          {children}
        </div>
      )
    }

    // No cell color, use original className
    return (
      <div className={className}>
        {children}
      </div>
    )
  }

  // Helper function to create public view cell with color support
  const createPublicViewCell = (
    value: string | undefined,
    flightId: string,
    fieldName: string,
    className: string,
    children?: React.ReactNode
  ) => {
    return (
      <PublicViewCellWithColor
        value={value}
        flightId={flightId}
        fieldName={fieldName}
        className={className}
      >
        {children}
      </PublicViewCellWithColor>
    )
  }

  const handleRowRightClick = (
    event: React.MouseEvent,
    flightId: string,
    rowType: 'arrival' | 'departure',
    stt: number
  ) => {
    // Only show context menu for non-public view
    if (isPublicView) return

    openRowContextMenu(event, {
      flightId,
      rowType,
      stt
    })
  }

  // Context menu action handlers
  const handleColorChange = async (color: string) => {
    if (contextMenu.cellInfo) {
      await updateCellColor.mutateAsync({
        flightId: contextMenu.cellInfo.flightId,
        fieldName: contextMenu.cellInfo.fieldName,
        colorValue: color
      })
    }
  }

  const handleRemoveColor = async () => {
    if (contextMenu.cellInfo) {
      await deleteCellColor.mutateAsync({
        flightId: contextMenu.cellInfo.flightId,
        fieldName: contextMenu.cellInfo.fieldName
      })
    }
  }

  const handleInsertRow = async (type: 'arrival' | 'departure', position: 'before' | 'after') => {
    if (contextMenu.rowInfo) {
      try {
        // Use date prop or fallback to current date
        const operationDate = date || (data.length > 0 ? data[0].date : new Date().toISOString().split('T')[0])

        console.log('Insert row operation:', {
          flightId: contextMenu.rowInfo.flightId,
          type,
          position,
          date: operationDate
        })

        await insertFlightRowSafe(
          contextMenu.rowInfo.flightId,
          type,
          position,
          operationDate
        )
      } catch (error) {
        console.error('Error inserting row:', error)
      }
    }
  }

  const handleDeleteRow = async (type: 'arrival' | 'departure') => {
    if (contextMenu.rowInfo) {
      try {
        // Use date prop or fallback to current date
        const operationDate = date || (data.length > 0 ? data[0].date : new Date().toISOString().split('T')[0])

        console.log('Delete row operation:', {
          flightId: contextMenu.rowInfo.flightId,
          type,
          date: operationDate
        })

        await deleteFlightRowSafe(
          contextMenu.rowInfo.flightId,
          type,
          operationDate
        )
      } catch (error) {
        console.error('Error deleting row:', error)
      }
    }
  }

  // Helper function để render staff name với styling cho first flight indicator và highlighting
  const renderStaffName = (staffName: string | undefined, shouldHighlight: boolean = false) => {
    if (!staffName) return '-';

    // Split staff names by "/" and render each name with appropriate styling
    const names = staffName.split('/');

    return (
      <>
        {names.map((name, index) => (
          <span key={index}>
            <span className={name.includes('*') ? 'text-blue-600 font-medium' : 'text-gray-700'}>
              {shouldHighlight ? (
                <HighlightedText text={name} searchTerm={searchTerm} />
              ) : (
                name
              )}
            </span>
            {index < names.length - 1 && <span className="text-gray-700">/</span>}
          </span>
        ))}
      </>
    );
  }

  // Helper functions for sequential status logic
  const getArrivalButtonStates = (row: any) => {
    const presentChecked = Boolean(row.arr_present);
    const finishChecked = Boolean(row.arr_finished);

    return {
      present: {
        enabled: true, // Always clickable
        canCheck: true,
        canUncheck: !finishChecked || presentChecked // Can uncheck if finish is not checked, or if present is already checked
      },
      finish: {
        enabled: presentChecked, // Only enabled if present is checked
        canCheck: presentChecked,
        canUncheck: true // Always can uncheck
      }
    };
  };

  const getDepartureButtonStates = (row: any) => {
    const presentChecked = Boolean(row.dep_present);
    const boardChecked = Boolean(row.dep_boarded);
    const finishChecked = Boolean(row.dep_finished);

    return {
      present: {
        enabled: true, // Always clickable
        canCheck: true,
        canUncheck: !boardChecked || presentChecked // Can uncheck if board is not checked, or if present is already checked
      },
      board: {
        enabled: presentChecked, // Only enabled if present is checked
        canCheck: presentChecked,
        canUncheck: !finishChecked || boardChecked // Can uncheck if finish is not checked, or if board is already checked
      },
      finish: {
        enabled: boardChecked, // Only enabled if board is checked
        canCheck: boardChecked,
        canUncheck: true // Always can uncheck
      }
    };
  };

  const columns = useMemo(() => [
    // STT Column
    columnHelper.accessor('stt', {
      header: 'STT',
      cell: (info) => (
        <div className="text-center font-medium text-gray-900">
          {info.getValue()}
        </div>
      ),
      size: 50,
    }),

    // ARR Group
    columnHelper.group({
      id: 'arr_group',
      header: () => (
        <div className="text-center bg-blue-50 py-2 px-4 rounded-t-lg">
          <div className="flex items-center justify-center space-x-2">
            <Plane className="h-4 w-4 text-blue-600 transform rotate-180" />
            <span className="font-semibold text-blue-700">ARR</span>
          </div>
        </div>
      ),
      columns: [
        columnHelper.accessor('arr_flt', {
          header: 'FLT',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (!value) return null;

            const arrivalStates = getArrivalButtonStates(row);

            const handleStatusClick = (field: string, currentValue: boolean) => {
              if (!onStatusUpdate) return;

              // Check if the action is allowed based on sequential logic
              if (field === 'arr_present') {
                const canPerformAction = currentValue ? arrivalStates.present.canUncheck : arrivalStates.present.canCheck;
                if (!canPerformAction) return;
              } else if (field === 'arr_finished') {
                const canPerformAction = currentValue ? arrivalStates.finish.canUncheck : arrivalStates.finish.canCheck;
                if (!canPerformAction) return;
              }

              onStatusUpdate(row.id, field, !currentValue);
            };

            return (
              <div className="space-y-2">
                <span className="font-medium text-blue-700 block">
                  <HighlightedText text={value} searchTerm={searchTerm} />
                </span>
                {showStatusControls && (
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => handleStatusClick('arr_present', row.arr_present || false)}
                      disabled={!arrivalStates.present.enabled}
                      className={`p-1 rounded-full transition-colors ${
                        !arrivalStates.present.enabled
                          ? 'bg-gray-50 text-gray-300 cursor-not-allowed'
                          : row.arr_present
                          ? 'bg-green-100 text-green-600 hover:bg-green-200'
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      title={!arrivalStates.present.enabled ? "Không thể thực hiện" : "Có mặt"}
                    >
                      <UserCheck className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleStatusClick('arr_finished', row.arr_finished || false)}
                      disabled={!arrivalStates.finish.enabled}
                      className={`p-1 rounded-full transition-colors ${
                        !arrivalStates.finish.enabled
                          ? 'bg-gray-50 text-gray-300 cursor-not-allowed'
                          : row.arr_finished
                          ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      title={!arrivalStates.finish.enabled ? "Phải có mặt trước" : "Hoàn thành"}
                    >
                      <CheckCircle className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            );
          },
          size: 100,
        }),
        columnHelper.accessor('arr_from', {
          header: 'FROM',
          cell: (info) => {
            const value = info.getValue();
            return value ? (
              <div className="flex items-center space-x-1">
                <MapPin className="h-3 w-3 text-gray-500" />
                <span className="text-gray-700">
                  <HighlightedText text={value} searchTerm={searchTerm} />
                </span>
              </div>
            ) : null;
          },
          size: 70,
        }),
        columnHelper.accessor('arr_reg', {
          header: 'REG',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (isPublicView) {
              return createPublicViewCell(
                value,
                row.id,
                'arr_reg',
                "min-w-[70px] text-gray-700",
                value ? <HighlightedText text={value} searchTerm={searchTerm} /> : '-'
              );
            }

            return createEditableCellWrapper(
              value,
              row.id,
              'arr_reg',
              (newValue) => handleFieldUpdate(row.id, 'arr_reg', newValue, value),
              editableFieldSchemas.arr_reg,
              "Số đăng ký",
              "min-w-[70px]"
            );
          },
          size: 70,
        }),
        columnHelper.accessor('arr_time', {
          header: 'TIME',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (isPublicView) {
              return createPublicViewCell(
                value,
                row.id,
                'arr_time',
                "min-w-[70px] text-gray-700 flex items-center space-x-1",
                <>
                  {value && <Clock className="h-3 w-3 text-gray-500" />}
                  <span>{formatFlightTimeVN(value || '', row.date)}</span>
                </>
              );
            }

            return createEditableCellWrapper(
              value,
              row.id,
              'arr_time',
              (newValue) => handleFieldUpdate(row.id, 'arr_time', newValue, value),
              editableFieldSchemas.arr_time,
              "HH:MM hoặc HH:MM+",
              "min-w-[70px]",
              "flight-time"
            );
          },
          size: 70,
        }),
        columnHelper.accessor('arr_staff', {
          header: 'STAFF',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (isPublicView) {
              return createPublicViewCell(
                value,
                row.id,
                'arr_staff',
                "min-w-[110px] flex items-center space-x-1",
                <>
                  {value && <User className="h-3 w-3 text-gray-500" />}
                  {renderStaffName(value, true)}
                </>
              );
            }

            return createEditableCellWrapper(
              value,
              row.id,
              'arr_staff',
              (newValue) => handleFieldUpdate(row.id, 'arr_staff', newValue, value),
              editableFieldSchemas.arr_staff,
              "Tên nhân viên",
              "min-w-[110px]"
            );
          },
          size: 110,
        }),
      ],
    }),

    // DEP Group
    columnHelper.group({
      id: 'dep_group',
      header: () => (
        <div className="text-center bg-orange-50 py-2 px-4 rounded-t-lg">
          <div className="flex items-center justify-center space-x-2">
            <Plane className="h-4 w-4 text-orange-600" />
            <span className="font-semibold text-orange-700">DEP</span>
          </div>
        </div>
      ),
      columns: [
        columnHelper.accessor('dep_flt', {
          header: 'FLT',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (!value) return null;

            const departureStates = getDepartureButtonStates(row);

            const handleStatusClick = (field: string, currentValue: boolean) => {
              if (!onStatusUpdate) return;

              // Check if the action is allowed based on sequential logic
              if (field === 'dep_present') {
                const canPerformAction = currentValue ? departureStates.present.canUncheck : departureStates.present.canCheck;
                if (!canPerformAction) return;
              } else if (field === 'dep_boarded') {
                const canPerformAction = currentValue ? departureStates.board.canUncheck : departureStates.board.canCheck;
                if (!canPerformAction) return;
              } else if (field === 'dep_finished') {
                const canPerformAction = currentValue ? departureStates.finish.canUncheck : departureStates.finish.canCheck;
                if (!canPerformAction) return;
              }

              onStatusUpdate(row.id, field, !currentValue);
            };

            return (
              <div className="space-y-2">
                <span className="font-medium text-orange-700 block">
                  <HighlightedText text={value} searchTerm={searchTerm} />
                </span>
                {showStatusControls && (
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => handleStatusClick('dep_present', row.dep_present || false)}
                      disabled={!departureStates.present.enabled}
                      className={`p-1 rounded-full transition-colors ${
                        !departureStates.present.enabled
                          ? 'bg-gray-50 text-gray-300 cursor-not-allowed'
                          : row.dep_present
                          ? 'bg-green-100 text-green-600 hover:bg-green-200'
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      title={!departureStates.present.enabled ? "Không thể thực hiện" : "Có mặt"}
                    >
                      <UserCheck className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleStatusClick('dep_boarded', row.dep_boarded || false)}
                      disabled={!departureStates.board.enabled}
                      className={`p-1 rounded-full transition-colors ${
                        !departureStates.board.enabled
                          ? 'bg-gray-50 text-gray-300 cursor-not-allowed'
                          : row.dep_boarded
                          ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200'
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      title={!departureStates.board.enabled ? "Phải có mặt trước" : "Boarding"}
                    >
                      <Users className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => handleStatusClick('dep_finished', row.dep_finished || false)}
                      disabled={!departureStates.finish.enabled}
                      className={`p-1 rounded-full transition-colors ${
                        !departureStates.finish.enabled
                          ? 'bg-gray-50 text-gray-300 cursor-not-allowed'
                          : row.dep_finished
                          ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                          : 'bg-gray-100 text-gray-400 hover:bg-gray-200'
                      }`}
                      title={!departureStates.finish.enabled ? "Phải boarding trước" : "Hoàn thành"}
                    >
                      <CheckCircle className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            );
          },
          size: 100,
        }),
        columnHelper.accessor('dep_to', {
          header: 'TO',
          cell: (info) => {
            const value = info.getValue();
            return value ? (
              <div className="flex items-center space-x-1">
                <MapPin className="h-3 w-3 text-gray-500" />
                <span className="text-gray-700">
                  <HighlightedText text={value} searchTerm={searchTerm} />
                </span>
              </div>
            ) : null;
          },
          size: 70,
        }),
        columnHelper.accessor('dep_reg', {
          header: 'REG',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (isPublicView) {
              return createPublicViewCell(
                value,
                row.id,
                'dep_reg',
                "min-w-[70px] text-gray-700",
                value ? <HighlightedText text={value} searchTerm={searchTerm} /> : '-'
              );
            }

            return createEditableCellWrapper(
              value,
              row.id,
              'dep_reg',
              (newValue) => handleFieldUpdate(row.id, 'dep_reg', newValue, value),
              editableFieldSchemas.dep_reg,
              "Số đăng ký",
              "min-w-[70px]"
            );
          },
          size: 70,
        }),
        columnHelper.accessor('dep_time', {
          header: 'TIME',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (isPublicView) {
              return createPublicViewCell(
                value,
                row.id,
                'dep_time',
                "min-w-[70px] text-gray-700 flex items-center space-x-1",
                <>
                  {value && <Clock className="h-3 w-3 text-gray-500" />}
                  <span>{formatFlightTimeVN(value || '', row.date)}</span>
                </>
              );
            }

            return createEditableCellWrapper(
              value,
              row.id,
              'dep_time',
              (newValue) => handleFieldUpdate(row.id, 'dep_time', newValue, value),
              editableFieldSchemas.dep_time,
              "HH:MM hoặc HH:MM+",
              "min-w-[70px]",
              "flight-time"
            );
          },
          size: 70,
        }),
        columnHelper.accessor('dep_staff', {
          header: 'STAFF',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (isPublicView) {
              return createPublicViewCell(
                value,
                row.id,
                'dep_staff',
                "min-w-[110px] flex items-center space-x-1",
                <>
                  {value && <User className="h-3 w-3 text-gray-500" />}
                  {renderStaffName(value, true)}
                </>
              );
            }

            return createEditableCellWrapper(
              value,
              row.id,
              'dep_staff',
              (newValue) => handleFieldUpdate(row.id, 'dep_staff', newValue, value),
              editableFieldSchemas.dep_staff,
              "Tên nhân viên",
              "min-w-[110px]"
            );
          },
          size: 110,
        }),
        columnHelper.accessor('remark', {
          header: 'REMARK',
          cell: (info) => {
            const value = info.getValue();
            const row = info.row.original;

            if (isPublicView) {
              return createPublicViewCell(
                value,
                row.id,
                'remark',
                "min-w-[120px] text-gray-700",
                value || '-'
              );
            }

            return createEditableCellWrapper(
              value,
              row.id,
              'remark',
              (newValue) => handleFieldUpdate(row.id, 'remark', newValue, value),
              editableFieldSchemas.remark,
              "Ghi chú",
              "min-w-[120px]"
            );
          },
          size: 120,
        }),
      ],
    }),
  ], [searchTerm]); // Add searchTerm to dependency array for highlighting updates

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  // Show loading state while determining mobile status to prevent hooks inconsistency
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Đang tải...</div>
      </div>
    );
  }

  // Render mobile view for small screens - moved conditional logic to render section
  if (isMobile) {
    return (
      <MobileFlightList
        data={data}
        onStatusUpdate={onStatusUpdate}
        showStatusControls={showStatusControls}
        isPublicView={isPublicView}
        searchTerm={searchTerm}
      />
    );
  }

  // Desktop view
  return (
    <div className="relative w-full overflow-x-auto lg:overflow-x-visible flight-table">
      <FlightTableLoadingOverlay
        isVisible={isUpdating || isRowOperationLoading}
        message={isRowOperationLoading ? "Đang thực hiện thao tác hàng..." : "Đang cập nhật dữ liệu..."}
      />
      <table className="w-full border-collapse min-w-[1200px] lg:min-w-full">
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  colSpan={header.colSpan}
                  className="border border-gray-200 bg-gray-50 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  style={{ width: `${header.getSize()}px` }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {table.getRowModel().rows.map((row, index) => (
            <tr
              key={row.id}
              className={`hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}
            >
              {row.getVisibleCells().map((cell) => {
                // Determine if this cell belongs to arrival or departure group
                const columnId = cell.column.id
                const isArrivalCell = columnId.startsWith('arr_') || cell.column.parent?.id === 'arr_group'
                const isDepartureCell = columnId.startsWith('dep_') || cell.column.parent?.id === 'dep_group'

                return (
                  <td
                    key={cell.id}
                    className="border border-gray-200 px-3 py-2 whitespace-nowrap text-sm"
                    style={{ width: `${cell.column.getSize()}px` }}
                    onContextMenu={(e) => {
                      // Right-click on cell for row operations (if not editable cell)
                      if (!isPublicView && (isArrivalCell || isDepartureCell)) {
                        const editableFields = ['arr_reg', 'arr_time', 'arr_staff', 'dep_reg', 'dep_time', 'dep_staff', 'remark']
                        const isEditableCell = editableFields.includes(columnId)

                        // Only show row context menu if this is not an editable cell
                        if (!isEditableCell) {
                          e.preventDefault()
                          e.stopPropagation()

                          const rowType = isArrivalCell ? 'arrival' : 'departure'
                          handleRowRightClick(e, row.original.id, rowType, row.original.stt)
                        }
                      }
                    }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                )
              })}
            </tr>
          ))}
        </tbody>
      </table>

      {data.length === 0 && (
        <div className="text-center py-12">
          <Plane className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <p className="text-gray-500">Không có dữ liệu lịch bay</p>
          <p className="text-sm text-gray-400">Vui lòng chọn ngày khác hoặc import dữ liệu</p>
        </div>
      )}

      {/* Context Menu */}
      <FlightContextMenu
        isOpen={contextMenu.isOpen}
        position={contextMenu.position}
        onClose={closeContextMenu}
        contextType={contextMenu.type}
        cellInfo={contextMenu.cellInfo}
        rowInfo={contextMenu.rowInfo}
        onColorChange={handleColorChange}
        onRemoveColor={handleRemoveColor}
        onInsertRow={handleInsertRow}
        onDeleteRow={handleDeleteRow}
      />
    </div>
  );
};

export default FlightTable;
