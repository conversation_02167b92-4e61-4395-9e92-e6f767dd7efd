import { RefreshCw, Save } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FlightTableLoadingOverlayProps {
  isVisible: boolean
  message?: string
  className?: string
}

export const FlightTableLoadingOverlay: React.FC<FlightTableLoadingOverlayProps> = ({
  isVisible,
  message = 'Đang cập nhật...',
  className
}) => {
  if (!isVisible) return null

  return (
    <div className={cn(
      'absolute inset-0 bg-white/80 backdrop-blur-sm z-50',
      'flex items-center justify-center',
      'transition-opacity duration-200',
      className
    )}>
      <div className="flex items-center gap-3 bg-white px-6 py-4 rounded-lg shadow-lg border">
        <RefreshCw className="h-5 w-5 animate-spin text-blue-600" />
        <span className="text-sm font-medium text-gray-700">{message}</span>
      </div>
    </div>
  )
}

interface SaveIndicatorProps {
  isVisible: boolean
  success?: boolean
  message?: string
  className?: string
}

export const SaveIndicator: React.FC<SaveIndicatorProps> = ({
  isVisible,
  success = true,
  message,
  className
}) => {
  if (!isVisible) return null

  const defaultMessage = success ? 'Đã lưu' : 'Lỗi lưu'
  const displayMessage = message || defaultMessage

  return (
    <div className={cn(
      'fixed top-4 right-4 z-50',
      'flex items-center gap-2 px-4 py-2 rounded-lg shadow-lg',
      'transition-all duration-300 transform',
      success 
        ? 'bg-green-100 text-green-800 border border-green-200' 
        : 'bg-red-100 text-red-800 border border-red-200',
      className
    )}>
      {success ? (
        <Save className="h-4 w-4" />
      ) : (
        <RefreshCw className="h-4 w-4" />
      )}
      <span className="text-sm font-medium">{displayMessage}</span>
    </div>
  )
}
