import React, { useState, useCallback } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Upload, FileSpreadsheet, AlertCircle, CheckCircle, Download, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/AuthContext'
import { parseExcelFile, validateExcelFile, generateExcelTemplate, getExcelSheetNames } from '@/lib/excel-parser'
import { validateExcelImport } from '@/lib/flight-validator'
import type { ExcelParseResult } from '@/lib/excel-parser'
import type { ExcelImportData, PreviewChangesResponse } from '@/lib/flight-schemas'
import { ImportPreviewChanges } from './ImportPreviewChanges'

interface ImportExcelProps {
  onImportSuccess?: () => void
  onClose?: () => void
}

export const ImportExcel: React.FC<ImportExcelProps> = ({ onImportSuccess, onClose }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [importDate, setImportDate] = useState<string>(new Date().toISOString().split('T')[0])
  const [importMode, setImportMode] = useState<'replace' | 'append' | 'update'>('replace')
  const [selectedSheet, setSelectedSheet] = useState<string>('')
  const [availableSheets, setAvailableSheets] = useState<string[]>([])
  const [skipRows, setSkipRows] = useState<number>(0)
  const [validateOnly, setValidateOnly] = useState<boolean>(false)
  const [parseResult, setParseResult] = useState<ExcelParseResult | null>(null)
  const [isProcessing, setIsProcessing] = useState<boolean>(false)
  const [previewChanges, setPreviewChanges] = useState<PreviewChangesResponse | null>(null)
  const [showPreview, setShowPreview] = useState<boolean>(false)
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [fileSizeWarning, setFileSizeWarning] = useState<string>('')

  const { toast } = useToast()
  const { accessToken } = useAuth()
  const queryClient = useQueryClient()

  // File upload handler
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file
    const validation = validateExcelFile(file)
    if (!validation.valid) {
      toast({
        title: 'File không hợp lệ',
        description: validation.error,
        variant: 'destructive'
      })
      return
    }

    setSelectedFile(file)
    setParseResult(null)

    // Check file size and show warning if needed
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > 10) {
      setFileSizeWarning(`File lớn (${fileSizeMB.toFixed(1)}MB). Quá trình xử lý có thể mất thời gian.`)
    } else if (fileSizeMB > 5) {
      setFileSizeWarning(`File khá lớn (${fileSizeMB.toFixed(1)}MB). Sẽ sử dụng chế độ tối ưu hiệu suất.`)
    } else {
      setFileSizeWarning('')
    }

    try {
      // Get available sheets
      const sheets = await getExcelSheetNames(file)
      setAvailableSheets(sheets)
      setSelectedSheet(sheets[0] || '')
    } catch (error) {
      toast({
        title: 'Lỗi đọc file',
        description: 'Không thể đọc danh sách sheet từ file Excel',
        variant: 'destructive'
      })
    }
  }, [toast])

  // Parse Excel file
  const handleParseFile = useCallback(async () => {
    if (!selectedFile) return

    setIsProcessing(true)
    setUploadProgress(0)

    try {
      const result = await parseExcelFile(selectedFile, {
        sheetName: selectedSheet,
        skipRows,
        validateData: true,
        onProgress: (progress, processed, total) => {
          setUploadProgress(Math.round(progress))
        }
      })

      setParseResult(result)

      if (result.success) {
        toast({
          title: 'Phân tích file thành công',
          description: `Đã phân tích ${result.data?.length || 0} dòng dữ liệu hợp lệ`
        })
      } else {
        toast({
          title: 'Có lỗi trong file Excel',
          description: `Tìm thấy ${result.errors?.length || 0} lỗi cần khắc phục`,
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Lỗi phân tích file',
        description: error instanceof Error ? error.message : 'Lỗi không xác định',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setTimeout(() => setUploadProgress(0), 1000)
    }
  }, [selectedFile, selectedSheet, skipRows, toast])

  // Import mutation
  const importMutation = useMutation({
    mutationFn: async (data: ExcelImportData) => {
      const response = await fetch('/api/flights/import', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
        body: data as any // FormData will be handled by the API
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Import failed')
      }

      return response.json()
    },
    onSuccess: (result) => {
      toast({
        title: 'Import thành công',
        description: `Đã import ${result.successRows} dòng dữ liệu`
      })
      
      // Invalidate flight queries
      queryClient.invalidateQueries({ queryKey: ['flights'] })
      
      onImportSuccess?.()
    },
    onError: (error) => {
      toast({
        title: 'Lỗi import',
        description: error instanceof Error ? error.message : 'Import thất bại',
        variant: 'destructive'
      })
    }
  })

  // Handle preview changes for update mode
  const handlePreviewChanges = useCallback(async () => {
    if (!selectedFile || !parseResult?.success || importMode !== 'update') return

    setIsProcessing(true)
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('date', importDate)
      formData.append('mode', importMode)
      if (selectedSheet) formData.append('sheetName', selectedSheet)
      if (skipRows > 0) formData.append('skipRows', skipRows.toString())

      const response = await fetch('/api/flights/preview-changes', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
        body: formData
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Preview failed')
      }

      const previewData = await response.json()
      setPreviewChanges(previewData)
      setShowPreview(true)

      toast({
        title: 'Preview thành công',
        description: `Tìm thấy ${previewData.summary.changedFieldsCount} thay đổi`
      })
    } catch (error) {
      toast({
        title: 'Lỗi preview',
        description: error instanceof Error ? error.message : 'Preview thất bại',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }, [selectedFile, parseResult, importDate, importMode, selectedSheet, skipRows, accessToken, toast])

  // Handle import
  const handleImport = useCallback(async () => {
    if (!selectedFile || !parseResult?.success) return

    const formData = new FormData()
    formData.append('file', selectedFile)
    formData.append('date', importDate)
    formData.append('mode', importMode)
    formData.append('validateOnly', validateOnly.toString())
    if (selectedSheet) formData.append('sheetName', selectedSheet)
    if (skipRows > 0) formData.append('skipRows', skipRows.toString())

    importMutation.mutate(formData as any)
  }, [selectedFile, parseResult, importDate, importMode, validateOnly, selectedSheet, skipRows, importMutation])

  // Download template
  const handleDownloadTemplate = useCallback(() => {
    const template = generateExcelTemplate()
    const url = URL.createObjectURL(template)
    const a = document.createElement('a')
    a.href = url
    a.download = 'flight-schedule-template.xlsx'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }, [])

  // Handle confirm import from preview
  const handleConfirmImport = useCallback(() => {
    setShowPreview(false)
    handleImport()
  }, [handleImport])

  // Handle cancel preview
  const handleCancelPreview = useCallback(() => {
    setShowPreview(false)
    setPreviewChanges(null)
  }, [])

  // Reset form
  const handleReset = useCallback(() => {
    setSelectedFile(null)
    setParseResult(null)
    setSelectedSheet('')
    setAvailableSheets([])
    setSkipRows(0)
    setValidateOnly(false)
    setUploadProgress(0)
    setFileSizeWarning('')
    setPreviewChanges(null)
    setShowPreview(false)
  }, [])

  return (
    <div className="space-y-6 animate-in fade-in-50 duration-500">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="space-y-1">
          <h2 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Import lịch bay từ Excel
          </h2>
          <p className="text-sm sm:text-base text-muted-foreground">
            Upload file Excel để import dữ liệu lịch bay vào hệ thống
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={handleDownloadTemplate}
            className="w-full sm:w-auto transition-all hover:scale-105"
          >
            <Download className="w-4 h-4 mr-2" />
            Tải template
          </Button>
          {onClose && (
            <Button
              variant="ghost"
              onClick={onClose}
              className="w-full sm:w-auto"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* File Upload Section */}
      <Card className="border-2 border-dashed border-gray-200 hover:border-blue-300 transition-colors duration-200">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Upload className="w-5 h-5 text-blue-600" />
            </div>
            Chọn file Excel
          </CardTitle>
          <CardDescription className="text-sm">
            Hỗ trợ file .xlsx, .xls và .csv. Kích thước tối đa 10MB.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="file-upload">File Excel</Label>
            <Input
              id="file-upload"
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileSelect}
              disabled={isProcessing || importMutation.isPending}
            />
          </div>

          {selectedFile && (
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg animate-in slide-in-from-top-2 duration-300">
                <div className="p-2 bg-green-100 rounded-lg">
                  <FileSpreadsheet className="w-5 h-5 text-green-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-green-900 truncate">{selectedFile.name}</p>
                  <p className="text-sm text-green-700">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleReset}
                  className="hover:bg-green-100 text-green-600"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {fileSizeWarning && (
                <Alert className="border-yellow-200 bg-yellow-50">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    {fileSizeWarning}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Processing Progress */}
          {isProcessing && uploadProgress > 0 && (
            <div className="space-y-3 p-4 bg-blue-50 border border-blue-200 rounded-lg animate-in slide-in-from-top-2 duration-300">
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                  <span className="font-medium text-blue-900">Đang phân tích file...</span>
                </div>
                <span className="font-mono text-blue-700">{uploadProgress}%</span>
              </div>
              <Progress
                value={uploadProgress}
                className="h-2 bg-blue-100"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration Section */}
      {selectedFile && availableSheets.length > 0 && (
        <Card className="animate-in slide-in-from-bottom-4 duration-500">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <Upload className="w-5 h-5 text-indigo-600" />
              </div>
              Cấu hình import
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="import-date">Ngày lịch bay</Label>
                <Input
                  id="import-date"
                  type="date"
                  value={importDate}
                  onChange={(e) => setImportDate(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="import-mode">Chế độ import</Label>
                <Select value={importMode} onValueChange={(value: any) => setImportMode(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="replace">Thay thế (xóa dữ liệu cũ)</SelectItem>
                    <SelectItem value="append">Thêm mới (giữ dữ liệu cũ)</SelectItem>
                    <SelectItem value="update">Cập nhật (merge dữ liệu)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {availableSheets.length > 1 && (
                <div>
                  <Label htmlFor="sheet-select">Sheet</Label>
                  <Select value={selectedSheet} onValueChange={setSelectedSheet}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {availableSheets.map(sheet => (
                        <SelectItem key={sheet} value={sheet}>{sheet}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div>
                <Label htmlFor="skip-rows">Bỏ qua dòng đầu</Label>
                <Input
                  id="skip-rows"
                  type="number"
                  min="0"
                  max="10"
                  value={skipRows}
                  onChange={(e) => setSkipRows(parseInt(e.target.value) || 0)}
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 pt-2">
              <Button
                onClick={handleParseFile}
                disabled={!selectedFile || isProcessing}
                className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transition-all duration-200"
              >
                {isProcessing ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Đang phân tích...
                  </div>
                ) : (
                  'Phân tích file'
                )}
              </Button>

              <label className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  checked={validateOnly}
                  onChange={(e) => setValidateOnly(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium">Chỉ kiểm tra (không import)</span>
              </label>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Preview Section */}
      {parseResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {parseResult.success ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-600" />
              )}
              Kết quả phân tích
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Summary */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{parseResult.metadata.totalRows}</div>
                <div className="text-sm text-muted-foreground">Tổng dòng</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{parseResult.data?.length || 0}</div>
                <div className="text-sm text-muted-foreground">Hợp lệ</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{parseResult.errors?.length || 0}</div>
                <div className="text-sm text-muted-foreground">Lỗi</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{parseResult.warnings?.length || 0}</div>
                <div className="text-sm text-muted-foreground">Cảnh báo</div>
              </div>
            </div>

            {/* Errors */}
            {parseResult.errors && parseResult.errors.length > 0 && (
              <div>
                <h4 className="font-medium text-red-600 mb-2">Lỗi cần khắc phục:</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {parseResult.errors.slice(0, 10).map((error, index) => (
                    <Alert key={index} variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Dòng {error.row}:</strong> {error.message}
                        {error.field && <span className="ml-2 text-xs">({error.field})</span>}
                      </AlertDescription>
                    </Alert>
                  ))}
                  {parseResult.errors.length > 10 && (
                    <p className="text-sm text-muted-foreground">
                      ... và {parseResult.errors.length - 10} lỗi khác
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Warnings */}
            {parseResult.warnings && parseResult.warnings.length > 0 && (
              <div>
                <h4 className="font-medium text-yellow-600 mb-2">Cảnh báo:</h4>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {parseResult.warnings.slice(0, 5).map((warning, index) => (
                    <Alert key={index}>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Dòng {warning.row}:</strong> {warning.message}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            )}

            {/* Data Preview */}
            {parseResult.data && parseResult.data.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Xem trước dữ liệu (5 dòng đầu):</h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm border rounded-lg">
                    <thead className="bg-muted">
                      <tr>
                        <th className="p-2 text-left border-r">STT</th>
                        <th className="p-2 text-left border-r">FLT (Đến)</th>
                        <th className="p-2 text-left border-r">FROM</th>
                        <th className="p-2 text-left border-r">STA</th>
                        <th className="p-2 text-left border-r">FLT (Đi)</th>
                        <th className="p-2 text-left border-r">TO</th>
                        <th className="p-2 text-left border-r">STD</th>
                        <th className="p-2 text-left">REMARK</th>
                      </tr>
                    </thead>
                    <tbody>
                      {parseResult.data.slice(0, 5).map((row, index) => (
                        <tr key={index} className="border-t">
                          <td className="p-2 border-r">{row.stt}</td>
                          <td className="p-2 border-r">{row.arr_flt || '-'}</td>
                          <td className="p-2 border-r">{row.arr_from || '-'}</td>
                          <td className="p-2 border-r">{row.arr_time || '-'}</td>
                          <td className="p-2 border-r">{row.dep_flt || '-'}</td>
                          <td className="p-2 border-r">{row.dep_to || '-'}</td>
                          <td className="p-2 border-r">{row.dep_time || '-'}</td>
                          <td className="p-2">{row.remark || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Import Actions */}
            {parseResult.success && parseResult.data && parseResult.data.length > 0 && (
              <div className="flex gap-2 pt-4 border-t">
                {importMode === 'update' && !validateOnly ? (
                  <Button
                    onClick={handlePreviewChanges}
                    disabled={isProcessing || importMutation.isPending}
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    {isProcessing ? 'Đang phân tích...' : 'Xem thay đổi'}
                  </Button>
                ) : (
                  <Button
                    onClick={handleImport}
                    disabled={importMutation.isPending}
                    className="flex-1"
                  >
                    {importMutation.isPending ? 'Đang import...' :
                     validateOnly ? 'Kiểm tra dữ liệu' : `Import ${parseResult.data.length} dòng`}
                  </Button>
                )}
                <Button variant="outline" onClick={handleReset}>
                  Chọn file khác
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Preview Changes Modal */}
      {showPreview && previewChanges && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold">Xem trước thay đổi - Chế độ Update</h3>
              <p className="text-sm text-gray-600 mt-1">
                Kiểm tra các thay đổi trước khi import vào hệ thống
              </p>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <ImportPreviewChanges
                previewData={previewChanges}
                onConfirmImport={handleConfirmImport}
                onCancel={handleCancelPreview}
                isImporting={importMutation.isPending}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
