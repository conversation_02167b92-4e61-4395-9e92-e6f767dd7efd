import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowRight, 
  Plus, 
  Edit3, 
  AlertCircle, 
  CheckCircle,
  FileText,
  TrendingUp
} from 'lucide-react'
import type { PreviewChangesResponse, PreviewChangeItem, PreviewFieldChange } from '@/lib/flight-schemas'

interface ImportPreviewChangesProps {
  previewData: PreviewChangesResponse
  onConfirmImport: () => void
  onCancel: () => void
  isImporting?: boolean
}

export const ImportPreviewChanges: React.FC<ImportPreviewChangesProps> = ({
  previewData,
  onConfirmImport,
  onCancel,
  isImporting = false
}) => {
  const { changes = [], summary } = previewData

  // Validate changes array
  const validChanges = Array.isArray(changes) ? changes : []

  // Filter changes that have actual modifications
  const changesWithModifications = validChanges.filter(change => change?.hasChanges)
  const insertsOnly = validChanges.filter(change => change?.type === 'insert')
  const updatesOnly = validChanges.filter(change => change?.type === 'update' && change?.hasChanges)

  const formatValue = (value: string | null) => {
    if (value === null || value === undefined || value === '') {
      return <span className="text-gray-400 italic">Trống</span>
    }
    return <span className="font-medium">{value}</span>
  }

  const getChangeTypeIcon = (type: 'update' | 'insert') => {
    switch (type) {
      case 'insert':
        return <Plus className="w-4 h-4 text-green-600" />
      case 'update':
        return <Edit3 className="w-4 h-4 text-blue-600" />
    }
  }

  const getChangeTypeColor = (type: 'update' | 'insert') => {
    switch (type) {
      case 'insert':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'update':
        return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const renderFieldChange = (fieldChange: PreviewFieldChange) => {
    if (!fieldChange.hasChanged) return null

    return (
      <div key={fieldChange.field} className="flex items-center gap-3 py-2">
        <div className="min-w-[100px]">
          <Badge variant="outline" className="text-xs">
            {fieldChange.fieldDisplayName}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2 flex-1">
          <div className="bg-red-50 border border-red-200 rounded px-2 py-1 text-sm">
            {formatValue(fieldChange.oldValue)}
          </div>
          <ArrowRight className="w-3 h-3 text-gray-400" />
          <div className="bg-green-50 border border-green-200 rounded px-2 py-1 text-sm">
            {formatValue(fieldChange.newValue)}
          </div>
        </div>
      </div>
    )
  }

  const renderChangeItem = (change: PreviewChangeItem) => {
    // Validate fieldChanges array
    const fieldChanges = Array.isArray(change?.fieldChanges) ? change.fieldChanges : []
    const changedFields = fieldChanges.filter(fc => fc?.hasChanged)

    return (
      <Card key={`${change.type}-${change.stt}`} className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getChangeTypeIcon(change.type)}
              <CardTitle className="text-base">
                STT {change.stt}
              </CardTitle>
              <Badge className={getChangeTypeColor(change.type)}>
                {change.type === 'insert' ? 'Thêm mới' : 'Cập nhật'}
              </Badge>
            </div>

            {change.type === 'update' && (
              <Badge variant="outline" className="text-xs">
                {changedFields.length} thay đổi
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {change.type === 'insert' ? (
            <div className="space-y-2">
              <p className="text-sm text-gray-600 mb-3">Chuyến bay mới sẽ được thêm:</p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {fieldChanges.map(fc => (
                    fc?.newValue && (
                      <div key={fc.field} className="flex justify-between">
                        <span className="text-gray-600">{fc.fieldDisplayName}:</span>
                        <span className="font-medium">{fc.newValue}</span>
                      </div>
                    )
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-1">
              {changedFields.map(renderFieldChange)}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            Tổng quan thay đổi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{summary.totalItems}</div>
              <div className="text-sm text-gray-600">Tổng dòng</div>
            </div>
            
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{summary.updateCount}</div>
              <div className="text-sm text-gray-600">Cập nhật</div>
            </div>
            
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{summary.insertCount}</div>
              <div className="text-sm text-gray-600">Thêm mới</div>
            </div>
            
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{summary.changedFieldsCount}</div>
              <div className="text-sm text-gray-600">Trường thay đổi</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Changes List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-gray-600" />
            Chi tiết thay đổi ({changesWithModifications.length} mục)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {changesWithModifications.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-4" />
              <p className="text-gray-500">Không có thay đổi nào</p>
              <p className="text-sm text-gray-400">
                Dữ liệu trong file Excel giống với dữ liệu hiện tại
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-4">
                {/* Show inserts first */}
                {insertsOnly.map(renderChangeItem)}
                
                {/* Then show updates */}
                {updatesOnly.map(renderChangeItem)}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-3 justify-end">
        <Button variant="outline" onClick={onCancel} disabled={isImporting}>
          Hủy bỏ
        </Button>
        <Button 
          onClick={onConfirmImport} 
          disabled={isImporting || changesWithModifications.length === 0}
          className="min-w-[120px]"
        >
          {isImporting ? 'Đang import...' : `Xác nhận import`}
        </Button>
      </div>
    </div>
  )
}
