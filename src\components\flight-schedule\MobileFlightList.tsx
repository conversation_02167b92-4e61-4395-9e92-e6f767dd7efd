import { FlightCard } from "./FlightCard";
import { useFlightMutations } from "@/hooks/useFlightMutations";
import { editableFieldSchemas, type EditableField } from "@/lib/flight-validation";
import { FlightTableLoadingOverlay } from "./FlightTableLoadingOverlay";

type FlightData = {
  id: string;
  date: string;
  stt: number;
  arr_flt?: string;
  arr_from?: string;
  arr_reg?: string;
  arr_time?: string;
  arr_staff?: string;
  arr_present?: boolean;
  arr_finished?: boolean;
  dep_flt?: string;
  dep_to?: string;
  dep_reg?: string;
  dep_time?: string;
  dep_staff?: string;
  dep_present?: boolean;
  dep_boarded?: boolean;
  dep_finished?: boolean;
  remark?: string;
};

interface MobileFlightListProps {
  data: FlightData[];
  onStatusUpdate?: (flightId: string, field: string, value: boolean) => void;
  showStatusControls?: boolean;
  isPublicView?: boolean;
  searchTerm?: string;
}

export const MobileFlightList = ({
  data,
  onStatusUpdate,
  showStatusControls = false,
  isPublicView = false,
  searchTerm = ""
}: MobileFlightListProps) => {
  const { updateFlightField, isUpdating } = useFlightMutations();

  const handleCellUpdate = async (flightId: string, field: EditableField, value: string) => {
    if (isPublicView) return;

    try {
      // Update the flight data using the correct mutation
      await updateFlightField.mutateAsync({
        flightId,
        field,
        value
      });
    } catch (error) {
      console.error(`Error updating ${field}:`, error);
      // Error handling is already done in the mutation
    }
  };

  if (data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-gray-500 text-lg mb-2">Không có chuyến bay nào</div>
        <div className="text-gray-400 text-sm">
          {searchTerm ? "Thử thay đổi từ khóa tìm kiếm" : "Chọn ngày khác để xem lịch bay"}
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Loading Overlay */}
      {isUpdating && (
        <FlightTableLoadingOverlay message="Đang cập nhật..." />
      )}

      {/* Flight Cards List */}
      <div className="space-y-4">
        {data.map((flight) => (
          <FlightCard
            key={flight.id}
            flight={flight}
            onStatusUpdate={onStatusUpdate}
            showStatusControls={showStatusControls}
            isPublicView={isPublicView}
            searchTerm={searchTerm}
            onCellUpdate={handleCellUpdate}
            isUpdating={isUpdating}
          />
        ))}
      </div>

      {/* Results Summary */}
      <div className="mt-6 text-center text-sm text-gray-500">
        Hiển thị {data.length} chuyến bay
        {searchTerm && (
          <span className="ml-1">
            cho "{searchTerm}"
          </span>
        )}
      </div>
    </div>
  );
};
