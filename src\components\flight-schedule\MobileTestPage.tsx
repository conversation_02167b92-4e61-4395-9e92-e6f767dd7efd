import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FlightTable } from './FlightTable';
import { SearchFilter } from './SearchFilter';
import { useIsMobile } from '@/hooks/use-mobile';
import { Smartphone, Monitor, RefreshCw } from 'lucide-react';

// Mock flight data for testing
const mockFlightData = [
  {
    id: '1',
    date: '2024-01-15',
    stt: 1,
    arr_flt: 'VN123',
    arr_from: 'HAN',
    arr_reg: 'VN-A123',
    arr_time: '08:30',
    arr_staff: '<PERSON>uy<PERSON>n Văn A*',
    arr_present: true,
    arr_finished: false,
    dep_flt: 'VN124',
    dep_to: 'SGN',
    dep_reg: 'VN-A123',
    dep_time: '10:15',
    dep_staff: '<PERSON><PERSON><PERSON><PERSON> Thị B/Lê Văn C',
    dep_present: false,
    dep_boarded: false,
    dep_finished: false,
    remark: 'Chuyến bay quan trọng'
  },
  {
    id: '2',
    date: '2024-01-15',
    stt: 2,
    arr_flt: 'VJ456',
    arr_from: 'DAD',
    arr_reg: 'VN-B456',
    arr_time: '14:20+',
    arr_staff: 'Phạm Văn D',
    arr_present: true,
    arr_finished: true,
    dep_flt: 'VJ457',
    dep_to: 'HUI',
    dep_reg: 'VN-B456',
    dep_time: '16:45',
    dep_staff: 'Hoàng Thị E*',
    dep_present: true,
    dep_boarded: true,
    dep_finished: false,
    remark: ''
  },
  {
    id: '3',
    date: '2024-01-15',
    stt: 3,
    arr_flt: '',
    arr_from: '',
    arr_reg: '',
    arr_time: '',
    arr_staff: '',
    arr_present: false,
    arr_finished: false,
    dep_flt: 'QH789',
    dep_to: 'CXR',
    dep_reg: 'VN-C789',
    dep_time: '18:30',
    dep_staff: 'Vũ Văn F/Đỗ Thị G*',
    dep_present: false,
    dep_boarded: false,
    dep_finished: false,
    remark: 'Chuyến bay đêm'
  }
];

export const MobileTestPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDate, setSelectedDate] = useState('2024-01-15');
  const [showStatusControls, setShowStatusControls] = useState(true);
  const [isPublicView, setIsPublicView] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  
  const { isMobile } = useIsMobile();

  const handleStatusUpdate = (flightId: string, field: string, value: boolean) => {
    console.log(`Status update: Flight ${flightId}, Field ${field}, Value ${value}`);
    // In real app, this would update the data
  };

  const handleRefresh = () => {
    setLastUpdated(new Date());
    console.log('Refreshing data...');
  };

  const filteredData = mockFlightData.filter(flight => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      flight.arr_flt?.toLowerCase().includes(searchLower) ||
      flight.dep_flt?.toLowerCase().includes(searchLower) ||
      flight.arr_staff?.toLowerCase().includes(searchLower) ||
      flight.dep_staff?.toLowerCase().includes(searchLower) ||
      flight.arr_reg?.toLowerCase().includes(searchLower) ||
      flight.dep_reg?.toLowerCase().includes(searchLower)
    );
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className={`w-full mx-auto py-4 ${isMobile ? 'px-2' : 'px-2 sm:px-4 lg:px-6'}`}>
          <div className={`flex items-center ${isMobile ? 'flex-col space-y-3' : 'justify-between'}`}>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                {isMobile ? (
                  <Smartphone className="h-5 w-5 text-blue-600" />
                ) : (
                  <Monitor className="h-6 w-6 text-blue-600" />
                )}
                <h1 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold text-gray-900`}>
                  Mobile Test Page
                </h1>
              </div>
            </div>
            
            {/* Controls */}
            <div className="flex items-center space-x-2">
              <Badge variant={isMobile ? 'default' : 'outline'}>
                {isMobile ? 'Mobile' : 'Desktop'}
              </Badge>
              <Badge variant={isPublicView ? 'secondary' : 'default'}>
                {isPublicView ? 'Public' : 'Admin'}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className={`w-full mx-auto py-6 ${isMobile ? 'px-2' : 'px-2 sm:px-4 lg:px-6'}`}>
        {/* Test Controls */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button
                variant={showStatusControls ? 'default' : 'outline'}
                onClick={() => setShowStatusControls(!showStatusControls)}
                className="w-full"
              >
                Status Controls: {showStatusControls ? 'ON' : 'OFF'}
              </Button>
              
              <Button
                variant={isPublicView ? 'default' : 'outline'}
                onClick={() => setIsPublicView(!isPublicView)}
                className="w-full"
              >
                View: {isPublicView ? 'Public' : 'Admin'}
              </Button>
              
              <Button
                variant="outline"
                onClick={handleRefresh}
                className="w-full flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Refresh</span>
              </Button>
              
              <div className="text-sm text-gray-600 flex items-center">
                Screen: {isMobile ? 'Mobile' : 'Desktop'}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search Filter */}
        <SearchFilter
          searchTerm={searchTerm}
          selectedDate={selectedDate}
          onSearchChange={setSearchTerm}
          onDateChange={setSelectedDate}
          showRefresh={true}
          onRefresh={handleRefresh}
          lastUpdated={lastUpdated}
          className="mb-6"
        />

        {/* Flight Schedule Content */}
        {isMobile ? (
          // Mobile: Direct list without card wrapper
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Lịch bay {selectedDate}
              </h2>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {filteredData.length} chuyến
                </Badge>
                <Badge variant="secondary" className="bg-green-100 text-green-600 text-xs">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                  TEST
                </Badge>
              </div>
            </div>

            {/* Content */}
            <FlightTable
              data={filteredData as any}
              onStatusUpdate={handleStatusUpdate}
              showStatusControls={showStatusControls}
              isPublicView={isPublicView}
              searchTerm={searchTerm}
              date={selectedDate}
            />
          </div>
        ) : (
          // Desktop: Card wrapper
          <Card className="bg-white">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Lịch bay ngày {selectedDate}</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">{filteredData.length} chuyến bay</Badge>
                  <Badge variant="secondary" className="bg-green-100 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                    TEST
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FlightTable
                data={filteredData as any}
                onStatusUpdate={handleStatusUpdate}
                showStatusControls={showStatusControls}
                isPublicView={isPublicView}
                searchTerm={searchTerm}
                date={selectedDate}
              />
            </CardContent>
          </Card>
        )}

        {/* Debug Info */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Screen Info:</strong>
                <ul className="mt-2 space-y-1">
                  <li>• Is Mobile: {isMobile ? 'Yes' : 'No'}</li>
                  <li>• Window Width: {typeof window !== 'undefined' ? window.innerWidth : 'N/A'}px</li>
                  <li>• Breakpoint: {isMobile ? '< 768px' : '>= 768px'}</li>
                </ul>
              </div>
              <div>
                <strong>Component State:</strong>
                <ul className="mt-2 space-y-1">
                  <li>• Status Controls: {showStatusControls ? 'Enabled' : 'Disabled'}</li>
                  <li>• View Mode: {isPublicView ? 'Public' : 'Admin'}</li>
                  <li>• Search Term: "{searchTerm}"</li>
                  <li>• Filtered Results: {filteredData.length}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
