import React from 'react';
import { FlightCard } from './FlightCard';
import { MobileFlightList } from './MobileFlightList';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Smartphone, Monitor } from 'lucide-react';

// Sample flight data for demo
const sampleFlights = [
  {
    id: '1',
    date: '2024-01-15',
    stt: 1,
    arr_flt: 'VN123',
    arr_from: 'HAN',
    arr_reg: 'VN-A123',
    arr_time: '08:30',
    arr_staff: '<PERSON><PERSON><PERSON><PERSON>n A*',
    arr_present: true,
    arr_finished: false,
    dep_flt: 'VN124',
    dep_to: 'SGN',
    dep_reg: 'VN-A123',
    dep_time: '10:15',
    dep_staff: 'Tr<PERSON><PERSON> Thị B/Lê <PERSON>ăn C',
    dep_present: false,
    dep_boarded: false,
    dep_finished: false,
    remark: 'Chuyến bay quan trọng'
  },
  {
    id: '2',
    date: '2024-01-15',
    stt: 2,
    arr_flt: 'VJ456',
    arr_from: 'DAD',
    arr_reg: 'VN-B456',
    arr_time: '14:20+',
    arr_staff: 'Phạm Văn D',
    arr_present: true,
    arr_finished: true,
    dep_flt: 'VJ457',
    dep_to: 'HUI',
    dep_reg: 'VN-B456',
    dep_time: '16:45',
    dep_staff: 'Hoàng Thị E*',
    dep_present: true,
    dep_boarded: true,
    dep_finished: false,
    remark: ''
  },
  {
    id: '3',
    date: '2024-01-15',
    stt: 3,
    arr_flt: '',
    arr_from: '',
    arr_reg: '',
    arr_time: '',
    arr_staff: '',
    arr_present: false,
    arr_finished: false,
    dep_flt: 'QH789',
    dep_to: 'CXR',
    dep_reg: 'VN-C789',
    dep_time: '18:30',
    dep_staff: 'Vũ Văn F/Đỗ Thị G*',
    dep_present: false,
    dep_boarded: false,
    dep_finished: false,
    remark: 'Chuyến bay đêm'
  }
];

interface MobileViewDemoProps {
  showStatusControls?: boolean;
  isPublicView?: boolean;
  searchTerm?: string;
}

export const MobileViewDemo: React.FC<MobileViewDemoProps> = ({
  showStatusControls = true,
  isPublicView = false,
  searchTerm = ''
}) => {
  const [viewMode, setViewMode] = React.useState<'mobile' | 'desktop'>('mobile');

  const handleStatusUpdate = (flightId: string, field: string, value: boolean) => {
    console.log(`Status update: ${flightId} - ${field}: ${value}`);
    // In real implementation, this would update the flight data
  };

  return (
    <div className="space-y-6">
      {/* View Mode Toggle */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Mobile Flight Schedule Demo</span>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'mobile' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('mobile')}
                className="flex items-center space-x-1"
              >
                <Smartphone className="h-4 w-4" />
                <span>Mobile</span>
              </Button>
              <Button
                variant={viewMode === 'desktop' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('desktop')}
                className="flex items-center space-x-1"
              >
                <Monitor className="h-4 w-4" />
                <span>Desktop</span>
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 mb-4">
            <p>
              <strong>Mobile View:</strong> Cards layout optimized for touch interaction
            </p>
            <p>
              <strong>Desktop View:</strong> Table layout for detailed data management
            </p>
          </div>

          {/* Demo Controls */}
          <div className="flex items-center space-x-4 mb-4">
            <Badge variant="outline">
              {sampleFlights.length} sample flights
            </Badge>
            <Badge variant={isPublicView ? 'secondary' : 'default'}>
              {isPublicView ? 'Public View' : 'Admin View'}
            </Badge>
            <Badge variant={showStatusControls ? 'default' : 'outline'}>
              Status Controls: {showStatusControls ? 'ON' : 'OFF'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Demo Content */}
      <div className={`${viewMode === 'mobile' ? 'max-w-md mx-auto' : 'w-full'}`}>
        {viewMode === 'mobile' ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Lịch bay 15/01/2024
              </h3>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {sampleFlights.length} chuyến
                </Badge>
                <Badge variant="secondary" className="bg-green-100 text-green-600 text-xs">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                  LIVE
                </Badge>
              </div>
            </div>

            <MobileFlightList
              data={sampleFlights}
              onStatusUpdate={handleStatusUpdate}
              showStatusControls={showStatusControls}
              isPublicView={isPublicView}
              searchTerm={searchTerm}
            />
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Lịch bay ngày 15/01/2024</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">{sampleFlights.length} chuyến bay</Badge>
                  <Badge variant="secondary" className="bg-green-100 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                    LIVE
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                Desktop table view would be displayed here
                <br />
                <small>(Use the actual FlightTable component for full desktop experience)</small>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Feature Highlights */}
      <Card>
        <CardHeader>
          <CardTitle>Mobile Optimization Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Touch Optimizations</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• 44px minimum touch targets</li>
                <li>• Touch feedback animations</li>
                <li>• Swipe-friendly interactions</li>
                <li>• No hover states on mobile</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Layout Adaptations</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Card-based layout for flights</li>
                <li>• Stacked information display</li>
                <li>• Responsive typography</li>
                <li>• Optimized spacing</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Performance</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Reduced DOM complexity</li>
                <li>• Optimized animations</li>
                <li>• Efficient re-renders</li>
                <li>• Memory-conscious design</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Accessibility</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Screen reader friendly</li>
                <li>• High contrast support</li>
                <li>• Reduced motion respect</li>
                <li>• Focus management</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
