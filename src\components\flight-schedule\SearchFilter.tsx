import React from 'react'
import { Search, Calendar, RefreshCw } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useIsMobile } from '@/hooks/use-mobile'

interface SearchFilterProps {
  searchTerm: string
  selectedDate: string
  onSearchChange: (term: string) => void
  onDateChange: (date: string) => void
  placeholder?: string
  showRefresh?: boolean
  onRefresh?: () => void
  lastUpdated?: Date
  className?: string
}

export const SearchFilter: React.FC<SearchFilterProps> = ({
  searchTerm,
  selectedDate,
  onSearchChange,
  onDateChange,
  placeholder = "Tìm kiếm nhân viên, số đăng ký... (phân cách bằng dấu phẩy)",
  showRefresh = false,
  onRefresh,
  lastUpdated,
  className
}) => {
  const { isMobile } = useIsMobile()

  return (
    <div className={cn("bg-white rounded-lg shadow-sm border", className)}>
      <div className={`${isMobile ? 'p-3' : 'p-4'}`}>
        <div className={`flex flex-col space-y-3 ${!isMobile ? 'lg:flex-row lg:items-center lg:justify-between lg:space-y-0' : ''}`}>
          {/* Left side - Search and date filter */}
          <div className={`flex flex-col space-y-3 ${!isMobile ? 'sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4' : ''}`}>
            {/* Search input */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={isMobile ? "Tìm kiếm..." : placeholder}
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className={`pl-10 ${isMobile ? 'w-full text-sm' : 'w-64'}`}
              />
            </div>

            {/* Date picker */}
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500 flex-shrink-0" />
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => onDateChange(e.target.value)}
                className={`${isMobile ? 'w-full text-sm' : 'w-40'}`}
              />
            </div>
          </div>

          {/* Right side - Refresh button and last updated (for public view) */}
          {showRefresh && (
            <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4'}`}>
              {lastUpdated && (
                <div className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  Cập nhật: {lastUpdated.toLocaleTimeString('vi-VN')}
                </div>
              )}
              <Button
                variant="outline"
                size={isMobile ? "sm" : "sm"}
                onClick={onRefresh}
                className={`flex items-center space-x-2 ${isMobile ? 'w-full justify-center' : ''}`}
              >
                <RefreshCw className="h-4 w-4" />
                <span>Làm mới</span>
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
