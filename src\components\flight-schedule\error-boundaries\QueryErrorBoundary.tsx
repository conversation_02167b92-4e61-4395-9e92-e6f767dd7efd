import React from 'react'
import { QueryErrorResetBoundary, useQueryErrorResetBoundary } from '@tanstack/react-query'
import { ErrorBoundary } from 'react-error-boundary'
import { Alert<PERSON>riangle, RefreshCw, Wifi, WifiOff, Server } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface QueryErrorFallbackProps {
  error: Error
  resetErrorBoundary: () => void
}

// Detect error types
const getErrorType = (error: Error) => {
  const message = error.message.toLowerCase()
  
  if (message.includes('network') || message.includes('fetch')) {
    return 'network'
  }
  if (message.includes('404') || message.includes('not found')) {
    return 'notFound'
  }
  if (message.includes('401') || message.includes('unauthorized')) {
    return 'unauthorized'
  }
  if (message.includes('403') || message.includes('forbidden')) {
    return 'forbidden'
  }
  if (message.includes('500') || message.includes('server')) {
    return 'server'
  }
  if (message.includes('timeout')) {
    return 'timeout'
  }
  
  return 'unknown'
}

// Get error details based on type
const getErrorDetails = (errorType: string, error: Error) => {
  switch (errorType) {
    case 'network':
      return {
        title: 'Lỗi kết nối mạng',
        description: 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối internet.',
        icon: WifiOff,
        color: 'text-orange-500'
      }
    case 'notFound':
      return {
        title: 'Không tìm thấy dữ liệu',
        description: 'Dữ liệu bạn yêu cầu không tồn tại hoặc đã bị xóa.',
        icon: AlertTriangle,
        color: 'text-yellow-500'
      }
    case 'unauthorized':
      return {
        title: 'Phiên đăng nhập hết hạn',
        description: 'Vui lòng đăng nhập lại để tiếp tục.',
        icon: AlertTriangle,
        color: 'text-red-500'
      }
    case 'forbidden':
      return {
        title: 'Không có quyền truy cập',
        description: 'Bạn không có quyền truy cập vào dữ liệu này.',
        icon: AlertTriangle,
        color: 'text-red-500'
      }
    case 'server':
      return {
        title: 'Lỗi server',
        description: 'Server đang gặp sự cố. Vui lòng thử lại sau.',
        icon: Server,
        color: 'text-red-500'
      }
    case 'timeout':
      return {
        title: 'Hết thời gian chờ',
        description: 'Yêu cầu mất quá nhiều thời gian. Vui lòng thử lại.',
        icon: AlertTriangle,
        color: 'text-orange-500'
      }
    default:
      return {
        title: 'Lỗi không xác định',
        description: error.message || 'Đã xảy ra lỗi không xác định.',
        icon: AlertTriangle,
        color: 'text-red-500'
      }
  }
}

// Main error fallback component
const QueryErrorFallback: React.FC<QueryErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary 
}) => {
  const errorType = getErrorType(error)
  const errorDetails = getErrorDetails(errorType, error)
  const Icon = errorDetails.icon

  const handleRetry = () => {
    resetErrorBoundary()
  }

  const handleReload = () => {
    window.location.reload()
  }

  const handleGoHome = () => {
    window.location.href = '/'
  }

  return (
    <div className="min-h-[300px] flex items-center justify-center p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Icon className={`h-12 w-12 ${errorDetails.color}`} />
          </div>
          <CardTitle className="text-lg">
            {errorDetails.title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {errorDetails.description}
            </AlertDescription>
          </Alert>

          {/* Network status indicator */}
          <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
            {navigator.onLine ? (
              <>
                <Wifi className="h-4 w-4 text-green-500" />
                Kết nối internet: Bình thường
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-red-500" />
                Kết nối internet: Mất kết nối
              </>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <Button 
              onClick={handleRetry}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Thử lại
            </Button>
            
            {errorType === 'network' && (
              <Button 
                variant="outline"
                onClick={handleReload}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Tải lại trang
              </Button>
            )}
            
            {errorType === 'unauthorized' && (
              <Button 
                variant="outline"
                onClick={handleGoHome}
                className="flex items-center gap-2"
              >
                Đăng nhập lại
              </Button>
            )}
          </div>

          {/* Error details for development */}
          {process.env.NODE_ENV === 'development' && (
            <details className="bg-gray-50 p-3 rounded text-xs">
              <summary className="cursor-pointer font-medium">
                Chi tiết lỗi (Development)
              </summary>
              <pre className="mt-2 whitespace-pre-wrap">
                {error.stack}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Compact error fallback for smaller components
const QueryErrorFallbackCompact: React.FC<QueryErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary 
}) => {
  const errorType = getErrorType(error)
  const errorDetails = getErrorDetails(errorType, error)
  const Icon = errorDetails.icon

  return (
    <div className="p-3 border border-red-200 rounded-lg bg-red-50">
      <div className="flex items-center gap-2">
        <Icon className={`h-4 w-4 ${errorDetails.color} flex-shrink-0`} />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-red-700 truncate">
            {errorDetails.title}
          </p>
          <p className="text-xs text-red-600">
            {errorDetails.description}
          </p>
        </div>
        <Button 
          size="sm" 
          variant="outline"
          onClick={resetErrorBoundary}
          className="flex items-center gap-1 flex-shrink-0"
        >
          <RefreshCw className="h-3 w-3" />
          Thử lại
        </Button>
      </div>
    </div>
  )
}

// Main QueryErrorBoundary component
interface QueryErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<QueryErrorFallbackProps>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  compact?: boolean
}

export const FlightQueryErrorBoundary: React.FC<QueryErrorBoundaryProps> = ({
  children,
  fallback,
  onError,
  compact = false
}) => {
  const FallbackComponent = fallback || (compact ? QueryErrorFallbackCompact : QueryErrorFallback)

  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          onReset={reset}
          onError={onError}
          fallbackRender={({ error, resetErrorBoundary }) => (
            <FallbackComponent 
              error={error} 
              resetErrorBoundary={resetErrorBoundary} 
            />
          )}
        >
          {children}
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  )
}

// Hook for manual error boundary reset
export const useFlightQueryErrorReset = () => {
  const { reset } = useQueryErrorResetBoundary()
  
  return {
    resetQueryErrors: reset
  }
}

// HOC for wrapping components with query error boundary
export function withQueryErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<QueryErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <FlightQueryErrorBoundary {...options}>
      <Component {...props} />
    </FlightQueryErrorBoundary>
  )

  WrappedComponent.displayName = `withQueryErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Export fallback components for custom usage
export { QueryErrorFallback, QueryErrorFallbackCompact }
