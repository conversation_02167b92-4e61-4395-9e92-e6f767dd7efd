import { Skeleton } from "@/components/ui/skeleton"

interface FlightTableSkeletonProps {
  rows?: number
  showStatusControls?: boolean
}

export const FlightTableSkeleton: React.FC<FlightTableSkeletonProps> = ({ 
  rows = 5,
  showStatusControls = false 
}) => {
  return (
    <div className="relative w-full overflow-x-auto lg:overflow-x-visible">
      <table className="w-full border-collapse min-w-[1200px] lg:min-w-full">
        {/* Header Skeleton */}
        <thead>
          <tr>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-16">
              <Skeleton className="h-4 w-8" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-20">
              <Skeleton className="h-4 w-12" />
            </th>
            
            {/* Arrival Columns */}
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-24">
              <Skeleton className="h-4 w-16" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-32">
              <Skeleton className="h-4 w-20" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-24">
              <Skeleton className="h-4 w-16" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-20">
              <Skeleton className="h-4 w-12" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-32">
              <Skeleton className="h-4 w-20" />
            </th>
            
            {/* Status Controls for Arrival */}
            {showStatusControls && (
              <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-24">
                <Skeleton className="h-4 w-16" />
              </th>
            )}
            
            {/* Departure Columns */}
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-24">
              <Skeleton className="h-4 w-16" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-32">
              <Skeleton className="h-4 w-20" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-24">
              <Skeleton className="h-4 w-16" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-20">
              <Skeleton className="h-4 w-12" />
            </th>
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-32">
              <Skeleton className="h-4 w-20" />
            </th>
            
            {/* Status Controls for Departure */}
            {showStatusControls && (
              <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-32">
                <Skeleton className="h-4 w-24" />
              </th>
            )}
            
            {/* Remark Column */}
            <th className="border border-gray-200 bg-gray-50 px-3 py-2 w-40">
              <Skeleton className="h-4 w-16" />
            </th>
          </tr>
        </thead>
        
        {/* Body Skeleton */}
        <tbody className="bg-white divide-y divide-gray-200">
          {Array.from({ length: rows }).map((_, index) => (
            <tr 
              key={index} 
              className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}
            >
              {/* STT */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-6" />
              </td>
              
              {/* Date */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-16" />
              </td>
              
              {/* Arrival Flight */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-16" />
              </td>
              
              {/* Arrival From */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-20" />
              </td>
              
              {/* Arrival REG */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-16" />
              </td>
              
              {/* Arrival Time */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-12" />
              </td>
              
              {/* Arrival Staff */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-20" />
              </td>
              
              {/* Arrival Status Controls */}
              {showStatusControls && (
                <td className="border border-gray-200 px-3 py-2">
                  <div className="flex gap-1">
                    <Skeleton className="h-6 w-6 rounded" />
                    <Skeleton className="h-6 w-6 rounded" />
                  </div>
                </td>
              )}
              
              {/* Departure Flight */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-16" />
              </td>
              
              {/* Departure To */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-20" />
              </td>
              
              {/* Departure REG */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-16" />
              </td>
              
              {/* Departure Time */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-12" />
              </td>
              
              {/* Departure Staff */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-20" />
              </td>
              
              {/* Departure Status Controls */}
              {showStatusControls && (
                <td className="border border-gray-200 px-3 py-2">
                  <div className="flex gap-1">
                    <Skeleton className="h-6 w-6 rounded" />
                    <Skeleton className="h-6 w-6 rounded" />
                    <Skeleton className="h-6 w-6 rounded" />
                  </div>
                </td>
              )}
              
              {/* Remark */}
              <td className="border border-gray-200 px-3 py-2">
                <Skeleton className="h-4 w-24" />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// Compact version for mobile/smaller screens
export const FlightTableSkeletonCompact: React.FC<FlightTableSkeletonProps> = ({ 
  rows = 3 
}) => {
  return (
    <div className="space-y-4">
      {Array.from({ length: rows }).map((_, index) => (
        <div key={index} className="border border-gray-200 rounded-lg p-4 bg-white">
          <div className="flex justify-between items-start mb-3">
            <Skeleton className="h-5 w-16" /> {/* STT */}
            <Skeleton className="h-4 w-20" /> {/* Date */}
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            {/* Arrival Section */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-12" /> {/* "Đến" label */}
              <div className="space-y-1">
                <Skeleton className="h-4 w-16" /> {/* Flight */}
                <Skeleton className="h-4 w-20" /> {/* From */}
                <Skeleton className="h-4 w-12" /> {/* Time */}
                <Skeleton className="h-4 w-18" /> {/* Staff */}
              </div>
            </div>
            
            {/* Departure Section */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-12" /> {/* "Đi" label */}
              <div className="space-y-1">
                <Skeleton className="h-4 w-16" /> {/* Flight */}
                <Skeleton className="h-4 w-20" /> {/* To */}
                <Skeleton className="h-4 w-12" /> {/* Time */}
                <Skeleton className="h-4 w-18" /> {/* Staff */}
              </div>
            </div>
          </div>
          
          {/* Remark */}
          <div className="mt-3 pt-3 border-t border-gray-100">
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      ))}
    </div>
  )
}

// Loading state for empty table
export const FlightTableEmptySkeleton: React.FC = () => {
  return (
    <div className="text-center py-12">
      <div className="flex flex-col items-center space-y-4">
        <Skeleton className="h-12 w-12 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-5 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
      </div>
    </div>
  )
}
