import React from 'react'
import { createHighlightedText } from '@/lib/search-utils'

interface HighlightedTextProps {
  text: string
  searchTerm: string
  className?: string
}

/**
 * Component để hiển thị text với highlighting cho search results
 * Sử dụng màu vàng để highlight các phần text khớp với search term
 */
export const HighlightedText: React.FC<HighlightedTextProps> = ({
  text,
  searchTerm,
  className = ''
}) => {
  // Nếu không có search term, hiển thị text bình thường
  if (!searchTerm || !text) {
    return <span className={className}>{text}</span>
  }

  // Tạo highlighted HTML
  const highlightedContent = createHighlightedText(text, searchTerm)

  return (
    <span 
      className={className}
      dangerouslySetInnerHTML={highlightedContent}
    />
  )
}

export default HighlightedText
