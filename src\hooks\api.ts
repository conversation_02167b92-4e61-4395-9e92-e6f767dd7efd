import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  apiClient, 
  type WalkieTalkie, 
  type AccessCard, 
  type ActivityHistory,
  type CreateWalkieTalkieRequest,
  type UpdateWalkieTalkieRequest,
  type CreateAccessCardRequest,
  type UpdateAccessCardRequest,
  type CreateActivityRequest
} from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

// Query Keys
export const queryKeys = {
  walkieTalkies: ['walkie-talkies'] as const,
  walkieTalkie: (id: number) => ['walkie-talkies', id] as const,
  accessCards: ['access-cards'] as const,
  accessCard: (id: number) => ['access-cards', id] as const,
  accessCardsByUser: (userId: string) => ['access-cards', 'user', userId] as const,
  activityHistory: (params?: any) => ['activity-history', params] as const,
  itemHistory: (type: string, id: number) => ['activity-history', 'item', type, id] as const,
  userHistory: (userId: string, params?: any) => ['activity-history', 'user', userId, params] as const,
};

// Walkie Talkies Hooks
export const useWalkieTalkies = () => {
  return useQuery({
    queryKey: queryKeys.walkieTalkies,
    queryFn: async () => {
      const response = await apiClient.getWalkieTalkies();
      if (!response.success) {
        throw new Error(response.error || 'Không thể lấy danh sách bộ đàm');
      }
      return response.data || [];
    },
  });
};

export const useWalkieTalkie = (id: number) => {
  return useQuery({
    queryKey: queryKeys.walkieTalkie(id),
    queryFn: async () => {
      const response = await apiClient.getWalkieTalkie(id);
      if (!response.success) {
        throw new Error(response.error || 'Không thể lấy thông tin bộ đàm');
      }
      return response.data;
    },
    enabled: !!id,
  });
};

export const useCreateWalkieTalkie = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: CreateWalkieTalkieRequest) => {
      const response = await apiClient.createWalkieTalkie(data);
      if (!response.success) {
        throw new Error(response.error || 'Không thể tạo bộ đàm');
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.walkieTalkies });
      toast({
        title: "Thành công",
        description: `Đã tạo bộ đàm ${data?.name} thành công`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateWalkieTalkie = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateWalkieTalkieRequest }) => {
      const response = await apiClient.updateWalkieTalkie(id, data);
      if (!response.success) {
        throw new Error(response.error || 'Không thể cập nhật bộ đàm');
      }
      return response;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.walkieTalkies });
      queryClient.invalidateQueries({ queryKey: queryKeys.walkieTalkie(id) });
      toast({
        title: "Thành công",
        description: "Đã cập nhật bộ đàm thành công",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteWalkieTalkie = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: number) => {
      const response = await apiClient.deleteWalkieTalkie(id);
      if (!response.success) {
        throw new Error(response.error || 'Không thể xóa bộ đàm');
      }
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.walkieTalkies });
      toast({
        title: "Thành công",
        description: "Đã xóa bộ đàm thành công",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Access Cards Hooks
export const useAccessCards = () => {
  return useQuery({
    queryKey: queryKeys.accessCards,
    queryFn: async () => {
      const response = await apiClient.getAccessCards();
      if (!response.success) {
        throw new Error(response.error || 'Không thể lấy danh sách thẻ từ');
      }
      return response.data || [];
    },
  });
};

export const useAccessCard = (id: number) => {
  return useQuery({
    queryKey: queryKeys.accessCard(id),
    queryFn: async () => {
      const response = await apiClient.getAccessCard(id);
      if (!response.success) {
        throw new Error(response.error || 'Không thể lấy thông tin thẻ từ');
      }
      return response.data;
    },
    enabled: !!id,
  });
};

export const useCreateAccessCard = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: CreateAccessCardRequest) => {
      const response = await apiClient.createAccessCard(data);
      if (!response.success) {
        throw new Error(response.error || 'Không thể tạo thẻ từ');
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.accessCards });
      toast({
        title: "Thành công",
        description: `Đã tạo thẻ từ ${data?.name} thành công`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateAccessCard = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateAccessCardRequest }) => {
      const response = await apiClient.updateAccessCard(id, data);
      if (!response.success) {
        throw new Error(response.error || 'Không thể cập nhật thẻ từ');
      }
      return response;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.accessCards });
      queryClient.invalidateQueries({ queryKey: queryKeys.accessCard(id) });
      toast({
        title: "Thành công",
        description: "Đã cập nhật thẻ từ thành công",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteAccessCard = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: number) => {
      const response = await apiClient.deleteAccessCard(id);
      if (!response.success) {
        throw new Error(response.error || 'Không thể xóa thẻ từ');
      }
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.accessCards });
      toast({
        title: "Thành công",
        description: "Đã xóa thẻ từ thành công",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Activity History Hooks
export const useActivityHistory = (params?: {
  page?: number;
  limit?: number;
  type?: 'walkie_talkie' | 'access_card';
  action?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.activityHistory(params),
    queryFn: async () => {
      const response = await apiClient.getActivityHistory(params);
      if (!response.success) {
        throw new Error(response.error || 'Không thể lấy lịch sử hoạt động');
      }
      return {
        data: response.data || [],
        pagination: response.pagination,
      };
    },
  });
};

export const useCreateActivityRecord = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateActivityRequest) => {
      const response = await apiClient.createActivityRecord(data);
      if (!response.success) {
        throw new Error(response.error || 'Không thể tạo bản ghi lịch sử');
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['activity-history'] });
    },
  });
};

export const useItemHistory = (type: 'walkie_talkie' | 'access_card', id: number) => {
  return useQuery({
    queryKey: queryKeys.itemHistory(type, id),
    queryFn: async () => {
      const response = await apiClient.getItemHistory(type, id);
      if (!response.success) {
        throw new Error(response.error || 'Không thể lấy lịch sử thiết bị');
      }
      return response.data || [];
    },
    enabled: !!id,
  });
};
