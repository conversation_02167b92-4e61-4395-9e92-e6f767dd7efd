import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { 
  updateCellColor, 
  deleteCellColor, 
  batchUpdateCellColors 
} from '@/lib/cell-colors-service'
import { cellColorQueryKeys } from './useCellColors'

// ============================================================================
// CELL COLOR MUTATIONS
// ============================================================================

/**
 * Hook để update cell color cho một field
 */
export const useUpdateCellColor = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({
      flightId,
      fieldName,
      colorValue
    }: {
      flightId: string
      fieldName: string
      colorValue: string
    }) => {
      const result = await updateCellColor(flightId, fieldName, colorValue)
      if (!result.success) {
        throw new Error(result.error || 'Failed to update cell color')
      }
      return result.data!
    },

    // Optimistic update
    onMutate: async ({ flightId, fieldName, colorValue }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: cellColorQueryKeys.flight(flightId) 
      })

      // Snapshot the previous value
      const previousColors = queryClient.getQueryData(
        cellColorQueryKeys.flight(flightId)
      )

      // Optimistically update to the new value
      queryClient.setQueryData(
        cellColorQueryKeys.flight(flightId),
        (old: any) => {
          if (!old) {
            return {
              flight_id: flightId,
              colors: { [fieldName]: colorValue },
              details: []
            }
          }
          
          return {
            ...old,
            colors: {
              ...old.colors,
              [fieldName]: colorValue
            }
          }
        }
      )

      // Also update batch queries that include this flight
      queryClient.setQueriesData(
        { queryKey: cellColorQueryKeys.all },
        (old: any) => {
          if (!old || typeof old !== 'object') return old

          // Check if this is a batch query result (format: { flightId: { fieldName: colorValue } })
          if (old[flightId]) {
            return {
              ...old,
              [flightId]: {
                ...old[flightId],
                [fieldName]: colorValue
              }
            }
          }

          return old
        }
      )

      // Force invalidate batch queries to ensure fresh data
      queryClient.invalidateQueries({
        queryKey: ['cellColors', 'batch'],
        exact: false
      })

      return { previousColors }
    },

    onError: (err, { flightId }, context) => {
      // Rollback optimistic update
      if (context?.previousColors) {
        queryClient.setQueryData(
          cellColorQueryKeys.flight(flightId),
          context.previousColors
        )
      }

      toast({
        title: 'Lỗi',
        description: err instanceof Error ? err.message : 'Không thể cập nhật màu ô',
        variant: 'destructive',
      })
    },

    onSuccess: (data, { flightId, fieldName }) => {
      // Invalidate individual flight query
      queryClient.invalidateQueries({
        queryKey: cellColorQueryKeys.flight(flightId)
      })

      // Invalidate all batch queries that might include this flight
      queryClient.invalidateQueries({
        queryKey: ['cellColors', 'batch'],
        exact: false
      })

      toast({
        title: 'Thành công',
        description: 'Đã cập nhật màu ô',
      })
    },
  })
}

/**
 * Hook để xóa cell color
 */
export const useDeleteCellColor = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({
      flightId,
      fieldName
    }: {
      flightId: string
      fieldName: string
    }) => {
      const result = await deleteCellColor(flightId, fieldName)
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete cell color')
      }
      return result.data!
    },

    // Optimistic update
    onMutate: async ({ flightId, fieldName }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: cellColorQueryKeys.flight(flightId) 
      })

      // Snapshot the previous value
      const previousColors = queryClient.getQueryData(
        cellColorQueryKeys.flight(flightId)
      )

      // Optimistically remove the color
      queryClient.setQueryData(
        cellColorQueryKeys.flight(flightId),
        (old: any) => {
          if (!old) return old
          
          const newColors = { ...old.colors }
          delete newColors[fieldName]
          
          return {
            ...old,
            colors: newColors
          }
        }
      )

      // Also update batch queries
      queryClient.setQueriesData(
        { queryKey: cellColorQueryKeys.all },
        (old: any) => {
          if (!old || typeof old !== 'object') return old

          if (old[flightId]) {
            const newFlightColors = { ...old[flightId] }
            delete newFlightColors[fieldName]

            return {
              ...old,
              [flightId]: newFlightColors
            }
          }

          return old
        }
      )

      // Force invalidate batch queries to ensure fresh data
      queryClient.invalidateQueries({
        queryKey: ['cellColors', 'batch'],
        exact: false
      })

      return { previousColors }
    },

    onError: (err, { flightId }, context) => {
      // Rollback optimistic update
      if (context?.previousColors) {
        queryClient.setQueryData(
          cellColorQueryKeys.flight(flightId),
          context.previousColors
        )
      }

      toast({
        title: 'Lỗi',
        description: err instanceof Error ? err.message : 'Không thể xóa màu ô',
        variant: 'destructive',
      })
    },

    onSuccess: (data, { flightId }) => {
      // Invalidate individual flight query
      queryClient.invalidateQueries({
        queryKey: cellColorQueryKeys.flight(flightId)
      })

      // Invalidate all batch queries that might include this flight
      queryClient.invalidateQueries({
        queryKey: ['cellColors', 'batch'],
        exact: false
      })

      toast({
        title: 'Thành công',
        description: 'Đã xóa màu ô',
      })
    },
  })
}

/**
 * Hook để batch update multiple cell colors
 */
export const useBatchUpdateCellColors = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (colors: Array<{
      flight_id: string
      field_name: string
      color_value: string
    }>) => {
      const result = await batchUpdateCellColors(colors)
      if (!result.success) {
        throw new Error(result.error || 'Failed to batch update cell colors')
      }
      return result.data!
    },

    onSuccess: (data, colors) => {
      // Get unique flight IDs
      const flightIds = [...new Set(colors.map(c => c.flight_id))]
      
      // Invalidate individual flight queries
      flightIds.forEach(flightId => {
        queryClient.invalidateQueries({
          queryKey: cellColorQueryKeys.flight(flightId)
        })
      })

      // Invalidate all batch queries
      queryClient.invalidateQueries({
        queryKey: ['cellColors', 'batch'],
        exact: false
      })

      const successCount = data.updated
      const totalCount = data.total
      const hasErrors = data.errors && data.errors.length > 0

      if (hasErrors) {
        toast({
          title: 'Cập nhật một phần',
          description: `Đã cập nhật ${successCount}/${totalCount} màu ô. Một số lỗi xảy ra.`,
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Thành công',
          description: `Đã cập nhật ${successCount} màu ô`,
        })
      }
    },

    onError: (err) => {
      toast({
        title: 'Lỗi',
        description: err instanceof Error ? err.message : 'Không thể cập nhật màu ô hàng loạt',
        variant: 'destructive',
      })
    },
  })
}

// ============================================================================
// COMBINED MUTATIONS HOOK
// ============================================================================

/**
 * Hook tổng hợp tất cả cell color mutations
 */
export const useCellColorMutations = () => {
  const updateCellColor = useUpdateCellColor()
  const deleteCellColor = useDeleteCellColor()
  const batchUpdateCellColors = useBatchUpdateCellColors()

  // Helper function để update hoặc delete color
  const setCellColor = (
    flightId: string,
    fieldName: string,
    colorValue: string | null
  ) => {
    if (colorValue === null) {
      return deleteCellColor.mutateAsync({ flightId, fieldName })
    } else {
      return updateCellColor.mutateAsync({ flightId, fieldName, colorValue })
    }
  }

  // Helper function để toggle color (set nếu chưa có, remove nếu đã có)
  const toggleCellColor = (
    flightId: string,
    fieldName: string,
    colorValue: string,
    currentColor?: string
  ) => {
    if (currentColor) {
      return deleteCellColor.mutateAsync({ flightId, fieldName })
    } else {
      return updateCellColor.mutateAsync({ flightId, fieldName, colorValue })
    }
  }

  // Check if any mutation is loading
  const isLoading = updateCellColor.isPending || 
                   deleteCellColor.isPending || 
                   batchUpdateCellColors.isPending

  return {
    updateCellColor,
    deleteCellColor,
    batchUpdateCellColors,
    setCellColor,
    toggleCellColor,
    isLoading,
  }
}
