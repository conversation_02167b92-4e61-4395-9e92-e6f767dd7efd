import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getBatchCellColors, getCellColors } from '@/lib/cell-colors-service'

// ============================================================================
// QUERY KEYS
// ============================================================================

export const cellColorQueryKeys = {
  all: ['cellColors'] as const,
  flight: (flightId: string) => [...cellColorQueryKeys.all, 'flight', flightId] as const,
  batch: (flightIds: string[]) => [...cellColorQueryKeys.all, 'batch', flightIds.sort().join(',')] as const,
}

// ============================================================================
// CELL COLORS HOOKS
// ============================================================================

/**
 * Hook để lấy cell colors cho một flight cụ thể
 */
export const useCellColors = (flightId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: cellColorQueryKeys.flight(flightId),
    queryFn: async () => {
      const result = await getCellColors(flightId)
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch cell colors')
      }
      return result.data!
    },
    enabled: enabled && !!flightId,
    staleTime: 5 * 60 * 1000, // 5 minutes - colors don't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes cache time
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
}

/**
 * Hook để lấy cell colors cho nhiều flights (batch)
 */
export const useBatchCellColors = (flightIds: string[], enabled: boolean = true) => {
  return useQuery({
    queryKey: cellColorQueryKeys.batch(flightIds),
    queryFn: async () => {
      if (flightIds.length === 0) {
        return {}
      }
      
      const result = await getBatchCellColors(flightIds)
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch batch cell colors')
      }
      return result.data!
    },
    enabled: enabled && flightIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes cache time
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
}

/**
 * Hook để lấy cell colors cho danh sách flights hiện tại
 * Tự động extract flight IDs từ flight data
 */
export const useFlightsCellColors = (
  flights: Array<{ id: string }>,
  enabled: boolean = true
) => {
  const flightIds = flights.map(flight => flight.id)
  
  return useBatchCellColors(flightIds, enabled)
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Hook để get cell color cho một field cụ thể
 */
export const useCellColor = (
  flightId: string,
  fieldName: string,
  enabled: boolean = true
) => {
  const { data: cellColors, ...rest } = useCellColors(flightId, enabled)
  
  const cellColor = cellColors?.colors?.[fieldName]
  
  return {
    ...rest,
    cellColor,
    hasColor: !!cellColor,
  }
}

/**
 * Hook để check xem có cell colors nào cho flight không
 */
export const useHasCellColors = (flightId: string, enabled: boolean = true) => {
  const { data: cellColors, isLoading, error } = useCellColors(flightId, enabled)
  
  const hasColors = cellColors ? Object.keys(cellColors.colors).length > 0 : false
  const colorCount = cellColors ? Object.keys(cellColors.colors).length : 0
  
  return {
    hasColors,
    colorCount,
    isLoading,
    error,
  }
}

/**
 * Hook để get all cell colors cho một flight với helper functions
 */
export const useFlightCellColors = (flightId: string, enabled: boolean = true) => {
  const queryClient = useQueryClient()
  const { data: cellColors, ...rest } = useCellColors(flightId, enabled)
  
  // Helper function để get color cho field
  const getFieldColor = (fieldName: string): string | undefined => {
    return cellColors?.colors?.[fieldName]
  }
  
  // Helper function để check field có color không
  const hasFieldColor = (fieldName: string): boolean => {
    return !!cellColors?.colors?.[fieldName]
  }
  
  // Helper function để get all colored fields
  const getColoredFields = (): string[] => {
    return cellColors ? Object.keys(cellColors.colors) : []
  }
  
  // Helper function để invalidate cache
  const invalidateColors = () => {
    queryClient.invalidateQueries({ 
      queryKey: cellColorQueryKeys.flight(flightId) 
    })
  }
  
  // Helper function để prefetch colors
  const prefetchColors = () => {
    queryClient.prefetchQuery({
      queryKey: cellColorQueryKeys.flight(flightId),
      queryFn: async () => {
        const result = await getCellColors(flightId)
        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch cell colors')
        }
        return result.data!
      },
      staleTime: 5 * 60 * 1000,
    })
  }
  
  return {
    ...rest,
    cellColors: cellColors?.colors || {},
    details: cellColors?.details || [],
    getFieldColor,
    hasFieldColor,
    getColoredFields,
    invalidateColors,
    prefetchColors,
  }
}

/**
 * Hook để manage cell colors cho table với multiple flights
 */
export const useTableCellColors = (
  flights: Array<{ id: string }>,
  enabled: boolean = true
) => {
  const queryClient = useQueryClient()
  const flightIds = flights.map(flight => flight.id)
  const { data: batchColors, ...rest } = useBatchCellColors(flightIds, enabled)
  
  // Helper function để get color cho specific flight + field
  const getCellColor = (flightId: string, fieldName: string): string | undefined => {
    return batchColors?.[flightId]?.[fieldName]
  }
  
  // Helper function để check cell có color không
  const hasCellColor = (flightId: string, fieldName: string): boolean => {
    return !!batchColors?.[flightId]?.[fieldName]
  }
  
  // Helper function để get all colors cho một flight
  const getFlightColors = (flightId: string): Record<string, string> => {
    return batchColors?.[flightId] || {}
  }
  
  // Helper function để count total colored cells
  const getTotalColoredCells = (): number => {
    if (!batchColors) return 0
    
    return Object.values(batchColors).reduce((total, flightColors) => {
      return total + Object.keys(flightColors).length
    }, 0)
  }
  
  // Helper function để invalidate all colors
  const invalidateAllColors = () => {
    queryClient.invalidateQueries({ 
      queryKey: cellColorQueryKeys.all 
    })
  }
  
  // Helper function để invalidate specific flight colors
  const invalidateFlightColors = (flightId: string) => {
    queryClient.invalidateQueries({ 
      queryKey: cellColorQueryKeys.flight(flightId) 
    })
  }
  
  return {
    ...rest,
    batchColors: batchColors || {},
    getCellColor,
    hasCellColor,
    getFlightColors,
    getTotalColoredCells,
    invalidateAllColors,
    invalidateFlightColors,
  }
}

// ============================================================================
// PREFETCH UTILITIES
// ============================================================================

/**
 * Utility function để prefetch cell colors cho multiple flights
 */
export const prefetchFlightsCellColors = (
  queryClient: ReturnType<typeof useQueryClient>,
  flightIds: string[]
) => {
  if (flightIds.length === 0) return
  
  queryClient.prefetchQuery({
    queryKey: cellColorQueryKeys.batch(flightIds),
    queryFn: async () => {
      const result = await getBatchCellColors(flightIds)
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch batch cell colors')
      }
      return result.data!
    },
    staleTime: 5 * 60 * 1000,
  })
}

/**
 * Utility function để invalidate all cell color queries
 */
export const invalidateAllCellColors = (
  queryClient: ReturnType<typeof useQueryClient>
) => {
  queryClient.invalidateQueries({ 
    queryKey: cellColorQueryKeys.all 
  })
}
