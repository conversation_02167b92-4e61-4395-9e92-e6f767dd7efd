import { useQuery } from '@tanstack/react-query'
import { apiClient } from '@/lib/api-client'

interface FieldChange {
  id: string
  flight_id: string
  field_name: string
  old_value: string | null
  new_value: string | null
  changed_by: string
  changer_name?: string
  changed_at: string
}

interface UseFieldChangesOptions {
  flightId: string
  fieldName: string
  enabled?: boolean
}

export const useFieldChanges = ({ flightId, fieldName, enabled = true }: UseFieldChangesOptions) => {
  return useQuery({
    queryKey: ['field-changes', flightId, fieldName],
    queryFn: async (): Promise<FieldChange[]> => {
      const response = await apiClient.get(`/api/flights/${flightId}/changes?field=${fieldName}`)

      if (!response.ok) {
        throw new Error('Không thể lấy lịch sử thay đổi')
      }

      const result = await response.json()
      return result.data || []
    },
    enabled: enabled && !!flightId && !!fieldName,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  })
}

// Hook để lấy tất cả changes của một flight, grouped by field
export const useFlightFieldChanges = (flightId: string, enabled = true) => {
  return useQuery({
    queryKey: ['flight-field-changes', flightId],
    queryFn: async (): Promise<Record<string, FieldChange[]>> => {
      const response = await apiClient.get(`/api/flights/${flightId}/changes`)

      if (!response.ok) {
        throw new Error('Không thể lấy lịch sử thay đổi')
      }

      const result = await response.json()
      const changes: FieldChange[] = result.data || []

      // Group changes by field_name
      const groupedChanges: Record<string, FieldChange[]> = {}

      changes.forEach(change => {
        if (!groupedChanges[change.field_name]) {
          groupedChanges[change.field_name] = []
        }
        groupedChanges[change.field_name].push(change)
      })

      // Sort changes within each field by changed_at (newest first)
      Object.keys(groupedChanges).forEach(fieldName => {
        groupedChanges[fieldName].sort((a, b) =>
          new Date(b.changed_at).getTime() - new Date(a.changed_at).getTime()
        )
      })

      return groupedChanges
    },
    enabled: enabled && !!flightId,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  })
}

// Hook để check xem field có changes hay không
export const useHasFieldChanges = (flightId: string, fieldName: string) => {
  const { data: fieldChanges } = useFlightFieldChanges(flightId)
  
  return {
    hasChanges: !!(fieldChanges?.[fieldName]?.length > 0),
    changeCount: fieldChanges?.[fieldName]?.length || 0,
    changes: fieldChanges?.[fieldName] || []
  }
}

// Hook để lấy latest change của một field
export const useLatestFieldChange = (flightId: string, fieldName: string) => {
  const { data: fieldChanges } = useFlightFieldChanges(flightId)
  
  const latestChange = fieldChanges?.[fieldName]?.[0] // First item is newest due to sorting
  
  return {
    latestChange,
    hasChange: !!latestChange,
    timeSinceChange: latestChange ? new Date().getTime() - new Date(latestChange.changed_at).getTime() : null
  }
}

// Utility function để invalidate field changes cache
export const invalidateFieldChanges = (queryClient: any, flightId?: string, fieldName?: string) => {
  if (flightId && fieldName) {
    queryClient.invalidateQueries({ queryKey: ['field-changes', flightId, fieldName] })
  } else if (flightId) {
    queryClient.invalidateQueries({ queryKey: ['flight-field-changes', flightId] })
    queryClient.invalidateQueries({ queryKey: ['field-changes', flightId] })
  } else {
    queryClient.invalidateQueries({ queryKey: ['field-changes'] })
    queryClient.invalidateQueries({ queryKey: ['flight-field-changes'] })
  }
}
