import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { flightQueryKeys } from '@/hooks/useFlights'
import { validateFlightField, type EditableField } from '@/lib/flight-validation'
import { apiClient } from '@/lib/api-client'

// Helper functions for advanced name matching and replacement
const escapeRegExp = (string: string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

const replaceNameInCombinedField = (fieldValue: string, searchName: string, replaceName: string, matchCase: boolean = false) => {
  if (!fieldValue || !searchName) return fieldValue

  // Handle asterisk (*) in names by escaping it properly
  const escapedSearchName = escapeRegExp(searchName)

  // Create regex for word boundary matching that handles "/" separator and asterisk
  const flags = matchCase ? 'g' : 'gi'
  const regex = new RegExp(`(^|/)\\s*(${escapedSearchName})(\\*?)\\s*(?=/|$)`, flags)

  return fieldValue.replace(regex, (match, prefix, name, asterisk) => {
    // Preserve the prefix (start of string or "/") and any asterisk
    const newName = replaceName + asterisk
    return prefix + newName
  })
}

interface FindReplaceMatch {
  flightId: string
  field: string
  currentValue: string
  newValue?: string
  stt: number
  flightNumber: string
}

interface FindReplaceUpdate {
  flightId: string
  field: EditableField
  value: string
  oldValue?: string
}

export const useFindReplace = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  const findReplaceMutation = useMutation({
    mutationFn: async ({ matches, replaceValue }: { matches: FindReplaceMatch[], replaceValue: string }) => {
      // Validate tất cả replace values trước khi gửi request
      const updates: FindReplaceUpdate[] = []

      for (const match of matches) {
        try {
          // Use newValue if available (for partial replacement), otherwise use replaceValue
          const finalValue = match.newValue || replaceValue

          // Validate field value theo schema
          validateFlightField(match.field as EditableField, finalValue)

          updates.push({
            flightId: match.flightId,
            field: match.field as EditableField,
            value: finalValue,
            oldValue: match.currentValue
          })
        } catch (error) {
          throw new Error(`Giá trị thay thế không hợp lệ cho ${match.field}: ${error.message}`)
        }
      }

      // Gửi batch update request sử dụng apiClient với authentication
      const response = await apiClient.post('/api/flights/batch-update', { updates })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || error.message || 'Cập nhật thất bại')
      }

      return response.json()
    },

    onMutate: async ({ matches, replaceValue }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: flightQueryKeys.lists() })

      // Snapshot previous data
      const previousQueries = queryClient.getQueriesData({ queryKey: flightQueryKeys.lists() })

      // Optimistically update all affected flights
      previousQueries.forEach(([queryKey, data]: [any, any]) => {
        if (data?.data) {
          const updatedData = {
            ...data,
            data: data.data.map((flight: any) => {
              const matchesForFlight = matches.filter(m => m.flightId === flight.id)
              if (matchesForFlight.length === 0) return flight

              const updatedFlight = { ...flight }
              matchesForFlight.forEach(match => {
                updatedFlight[match.field] = replaceValue
              })
              return updatedFlight
            })
          }
          queryClient.setQueryData(queryKey, updatedData)
        }
      })

      return { previousQueries, matches, replaceValue }
    },

    onError: (error, variables, context) => {
      // Revert optimistic updates on error
      if (context?.previousQueries) {
        context.previousQueries.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data)
        })
      }

      toast({
        title: 'Lỗi Find & Replace',
        description: error.message,
        variant: 'destructive',
      })
    },

    onSuccess: (data, variables) => {
      // Invalidate queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.changes() })

      const { matches } = variables
      const updatedCount = data.data?.updatedCount || matches.length

      toast({
        title: 'Find & Replace thành công',
        description: `Đã thay thế ${updatedCount} giá trị`,
      })
    }
  })

  // Swap mutation để hoán đổi 2 giá trị
  const swapMutation = useMutation({
    mutationFn: async ({ valueA, valueB, scope, flights }: {
      valueA: string,
      valueB: string,
      scope: 'staff' | 'reg' | 'both',
      flights: any[]
    }) => {
      // Tìm tất cả matches cho cả 2 giá trị
      const updates: FindReplaceUpdate[] = []

      flights.forEach((flight) => {
        const fieldsToCheck: string[] = []

        if (scope === 'staff' || scope === 'both') {
          fieldsToCheck.push('arr_staff', 'dep_staff')
        }
        if (scope === 'reg' || scope === 'both') {
          fieldsToCheck.push('arr_reg', 'dep_reg')
        }

        fieldsToCheck.forEach((field) => {
          const fieldValue = flight[field] as string
          if (!fieldValue) return

          let newValue = fieldValue
          let hasChange = false

          // Use advanced name matching for combined fields and asterisk handling
          const escapedValueA = escapeRegExp(valueA)
          const escapedValueB = escapeRegExp(valueB)
          const regexA = new RegExp(`(^|/)\\s*(${escapedValueA})(\\*?)\\s*(?=/|$)`, 'gi')
          const regexB = new RegExp(`(^|/)\\s*(${escapedValueB})(\\*?)\\s*(?=/|$)`, 'gi')

          // Check if fieldValue contains valueA and replace with valueB
          if (regexA.test(fieldValue)) {
            newValue = replaceNameInCombinedField(newValue, valueA, valueB, false)
            hasChange = true
          }

          // Check if fieldValue contains valueB and replace with valueA
          if (regexB.test(fieldValue)) {
            newValue = replaceNameInCombinedField(newValue, valueB, valueA, false)
            hasChange = true
          }

          if (hasChange && newValue !== fieldValue) {
            validateFlightField(field as EditableField, newValue)
            updates.push({
              flightId: flight.id,
              field: field as EditableField,
              value: newValue,
              oldValue: fieldValue
            })
          }
        })
      })

      if (updates.length === 0) {
        throw new Error('Không tìm thấy giá trị nào để hoán đổi')
      }

      // Gửi batch update request
      const response = await apiClient.post('/api/flights/batch-update', { updates })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || error.message || 'Hoán đổi thất bại')
      }

      return response.json()
    },

    onMutate: async ({ valueA, valueB, scope, flights }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: flightQueryKeys.lists() })

      // Snapshot previous data
      const previousQueries = queryClient.getQueriesData({ queryKey: flightQueryKeys.lists() })

      // Optimistically update all affected flights
      previousQueries.forEach(([queryKey, data]: [any, any]) => {
        if (data?.data) {
          const updatedData = {
            ...data,
            data: data.data.map((flight: any) => {
              const updatedFlight = { ...flight }
              const fieldsToCheck: string[] = []

              if (scope === 'staff' || scope === 'both') {
                fieldsToCheck.push('arr_staff', 'dep_staff')
              }
              if (scope === 'reg' || scope === 'both') {
                fieldsToCheck.push('arr_reg', 'dep_reg')
              }

              fieldsToCheck.forEach((field) => {
                const fieldValue = flight[field] as string
                if (!fieldValue) return

                let newValue = fieldValue

                // Use advanced name matching for combined fields and asterisk handling
                const escapedValueA = escapeRegExp(valueA)
                const escapedValueB = escapeRegExp(valueB)
                const regexA = new RegExp(`(^|/)\\s*(${escapedValueA})(\\*?)\\s*(?=/|$)`, 'gi')
                const regexB = new RegExp(`(^|/)\\s*(${escapedValueB})(\\*?)\\s*(?=/|$)`, 'gi')

                // Check if fieldValue contains valueA and replace with valueB
                if (regexA.test(fieldValue)) {
                  newValue = replaceNameInCombinedField(newValue, valueA, valueB, false)
                }

                // Check if fieldValue contains valueB and replace with valueA
                if (regexB.test(fieldValue)) {
                  newValue = replaceNameInCombinedField(newValue, valueB, valueA, false)
                }

                if (newValue !== fieldValue) {
                  updatedFlight[field] = newValue
                }
              })

              return updatedFlight
            })
          }
          queryClient.setQueryData(queryKey, updatedData)
        }
      })

      return { previousQueries, valueA, valueB, scope }
    },

    onError: (error, variables, context) => {
      // Revert optimistic updates on error
      if (context?.previousQueries) {
        context.previousQueries.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data)
        })
      }

      toast({
        title: 'Lỗi hoán đổi',
        description: error.message,
        variant: 'destructive',
      })
    },

    onSuccess: (data, variables) => {
      // Invalidate queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.changes() })

      const updatedCount = data.data?.updatedCount || 0

      toast({
        title: 'Hoán đổi thành công',
        description: `Đã hoán đổi "${variables.valueA}" ↔ "${variables.valueB}" (${updatedCount} thay đổi)`,
      })
    }
  })

  // Helper function để thực hiện find & replace
  const performFindReplace = async (matches: FindReplaceMatch[], replaceValue: string) => {
    if (!matches.length || !replaceValue.trim()) {
      throw new Error('Không có dữ liệu để thay thế')
    }

    return findReplaceMutation.mutateAsync({ matches, replaceValue })
  }

  // Helper function để thực hiện swap
  const performSwap = async (valueA: string, valueB: string, scope: 'staff' | 'reg' | 'both', flights: any[]) => {
    if (!valueA.trim() || !valueB.trim()) {
      throw new Error('Cả hai giá trị đều phải được nhập')
    }

    if (valueA === valueB) {
      throw new Error('Hai giá trị không được giống nhau')
    }

    return swapMutation.mutateAsync({ valueA, valueB, scope, flights })
  }

  return {
    performFindReplace,
    performSwap,
    isLoading: findReplaceMutation.isPending || swapMutation.isPending,
    error: findReplaceMutation.error || swapMutation.error,
    reset: () => {
      findReplaceMutation.reset()
      swapMutation.reset()
    }
  }
}

// Helper function để tạo summary cho find & replace operation
export const createFindReplaceSummary = (matches: FindReplaceMatch[], replaceValue: string) => {
  const groupedByField = matches.reduce((acc, match) => {
    if (!acc[match.field]) {
      acc[match.field] = []
    }
    acc[match.field].push(match)
    return acc
  }, {} as Record<string, FindReplaceMatch[]>)

  const summary = Object.entries(groupedByField).map(([field, fieldMatches]) => ({
    field,
    count: fieldMatches.length,
    flights: fieldMatches.map(m => `STT ${m.stt}`).join(', ')
  }))

  return {
    totalMatches: matches.length,
    uniqueFlights: new Set(matches.map(m => m.flightId)).size,
    fieldSummary: summary,
    replaceValue
  }
}

// Helper function để validate find & replace operation
export const validateFindReplaceOperation = (matches: FindReplaceMatch[], replaceValue: string) => {
  const errors: string[] = []

  if (!matches.length) {
    errors.push('Không có kết quả tìm kiếm nào')
  }

  if (!replaceValue.trim()) {
    errors.push('Giá trị thay thế không được để trống')
  }

  // Validate replace value cho từng field type
  const fieldTypes = new Set(matches.map(m => m.field))
  
  for (const field of fieldTypes) {
    try {
      validateFlightField(field as EditableField, replaceValue)
    } catch (error) {
      errors.push(`Giá trị không hợp lệ cho ${field}: ${error.message}`)
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
