import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { validateFlightField, type EditableField, type FlightChangeData } from '@/lib/flight-validation'
import { flightQueryKeys } from '@/hooks/useFlights'
import { apiClient } from '@/lib/api-client'

interface UpdateFlightFieldParams {
  flightId: string
  field: EditableField
  value: string
  oldValue?: string
}

interface UpdateFlightStatusParams {
  flightId: string
  field: 'arr_present' | 'arr_finished' | 'dep_present' | 'dep_boarded' | 'dep_finished'
  value: boolean
}

interface FlightData {
  id: string
  date: string
  stt: number
  [key: string]: any
}

export const useFlightMutations = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Mutation để update single field với optimistic updates
  const updateFlightField = useMutation({
    mutationFn: async ({ flightId, field, value }: UpdateFlightFieldParams) => {
      // Validate field trướ<PERSON> khi gửi request
      validateFlightField(field, value)

      const response = await apiClient.patch(`/api/flights/${flightId}`, {
        field,
        value: value || null
      })

      if (!response.ok) {
        const error = await response.json()
        console.error('API Error:', error)
        throw new Error(error.error || error.message || 'Cập nhật thất bại')
      }

      return response.json()
    },
    
    // Optimistic update
    onMutate: async ({ flightId, field, value, oldValue }) => {
      // Cancel any outgoing refetches for this flight
      await queryClient.cancelQueries({ queryKey: flightQueryKeys.detail(flightId) })
      await queryClient.cancelQueries({ queryKey: flightQueryKeys.lists() })

      // Snapshot the previous flight data
      const previousFlight = queryClient.getQueryData<FlightData>(flightQueryKeys.detail(flightId))

      // Optimistically update the specific flight
      if (previousFlight) {
        queryClient.setQueryData(flightQueryKeys.detail(flightId), {
          ...previousFlight,
          [field]: value || null
        })
      }

      // Update in all list caches that might contain this flight
      queryClient.getQueriesData({ queryKey: flightQueryKeys.lists() })
        .forEach(([queryKey, data]: [any, any]) => {
          if (data?.data) {
            const updatedData = {
              ...data,
              data: data.data.map((flight: FlightData) =>
                flight.id === flightId
                  ? { ...flight, [field]: value || null }
                  : flight
              )
            }
            queryClient.setQueryData(queryKey, updatedData)
          }
        })

      // Return a context object with the snapshotted value
      return { previousFlight, flightId, field, oldValue: oldValue || null, newValue: value || null }
    },
    
    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (err, variables, context) => {
      if (context?.previousFlight) {
        // Restore the previous flight data
        queryClient.setQueryData(flightQueryKeys.detail(variables.flightId), context.previousFlight)
      }

      toast({
        title: 'Lỗi cập nhật',
        description: err instanceof Error ? err.message : 'Không thể cập nhật dữ liệu',
        variant: 'destructive'
      })
    },
    
    // Always refetch after error or success
    onSettled: (data, error, variables) => {
      // Invalidate flight lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })

      // Invalidate specific flight detail
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.detail(variables.flightId) })

      // Invalidate field changes for this specific flight and field
      if (variables.flightId && variables.field) {
        queryClient.invalidateQueries({
          queryKey: ['field-changes', variables.flightId, variables.field]
        })
        queryClient.invalidateQueries({
          queryKey: flightQueryKeys.flightChanges(variables.flightId)
        })
      }
    },

    // Track change on success
    onSuccess: (data, variables, context) => {
      // Log the change for audit trail
      if (context) {
        logFlightChange.mutate({
          flightId: context.flightId,
          field: variables.field,
          oldValue: context.oldValue,
          newValue: context.newValue
        })
      }

      toast({
        title: 'Cập nhật thành công',
        description: `Đã cập nhật ${getFieldDisplayName(variables.field)}`,
      })
    }
  })

  // Mutation để log flight changes
  const logFlightChange = useMutation({
    mutationFn: async (changeData: {
      flightId: string
      field: string
      oldValue: string | null
      newValue: string | null
    }) => {
      const response = await apiClient.post('/api/flights/changes', {
        flight_id: changeData.flightId,
        field_name: changeData.field,
        old_value: changeData.oldValue,
        new_value: changeData.newValue,
        changed_at: new Date().toISOString()
      })

      if (!response.ok) {
        console.error('Failed to log flight change')
      }

      return response.json()
    },
    
    // Invalidate changes query to show real-time updates
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.changes() })
    }
  })

  // Batch update mutation
  const batchUpdateFlights = useMutation({
    mutationFn: async (updates: UpdateFlightFieldParams[]) => {
      const response = await apiClient.post('/api/flights/batch-update', { updates })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Cập nhật hàng loạt thất bại')
      }

      return response.json()
    },
    
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.changes() })

      toast({
        title: 'Cập nhật hàng loạt thành công',
        description: `Đã cập nhật ${data.updatedCount} chuyến bay`,
      })
    },
    
    onError: (err) => {
      toast({
        title: 'Lỗi cập nhật hàng loạt',
        description: err instanceof Error ? err.message : 'Không thể cập nhật dữ liệu',
        variant: 'destructive'
      })
    }
  })

  // Mutation để update flight status
  const updateFlightStatus = useMutation({
    mutationFn: async ({ flightId, field, value }: UpdateFlightStatusParams) => {
      const response = await apiClient.patch(`/api/flight-status/${flightId}/status`, {
        field,
        value
      })

      if (!response.ok) {
        const error = await response.json()
        console.error('Status Update API Error:', error)
        throw new Error(error.error || error.message || 'Cập nhật trạng thái thất bại')
      }

      return response.json()
    },

    // Optimistic update
    onMutate: async ({ flightId, field, value }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: flightQueryKeys.detail(flightId) })
      await queryClient.cancelQueries({ queryKey: flightQueryKeys.lists() })

      // Snapshot the previous data
      const previousFlight = queryClient.getQueryData<FlightData>(flightQueryKeys.detail(flightId))
      const previousLists = queryClient.getQueriesData({ queryKey: flightQueryKeys.lists() })

      // Optimistically update the flight status
      if (previousFlight) {
        queryClient.setQueryData(flightQueryKeys.detail(flightId), {
          ...previousFlight,
          [field]: value
        })
      }

      // Update in all list queries
      previousLists.forEach(([queryKey, data]) => {
        if (data && Array.isArray((data as any).data)) {
          const listData = data as { data: FlightData[], total: number, pagination: any }
          const updatedData = listData.data.map(flight =>
            flight.id === flightId
              ? { ...flight, [field]: value }
              : flight
          )
          queryClient.setQueryData(queryKey, {
            ...listData,
            data: updatedData
          })
        }
      })

      return { previousFlight, previousLists }
    },

    onError: (err, variables, context) => {
      // Revert optimistic updates on error
      if (context?.previousFlight) {
        queryClient.setQueryData(flightQueryKeys.detail(variables.flightId), context.previousFlight)
      }

      if (context?.previousLists) {
        context.previousLists.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data)
        })
      }

      toast({
        title: 'Lỗi cập nhật trạng thái',
        description: err.message,
        variant: 'destructive',
      })
    },

    onSuccess: (data, variables) => {
      // Invalidate queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.detail(variables.flightId) })

      toast({
        title: 'Cập nhật thành công',
        description: 'Trạng thái nhân viên đã được cập nhật',
      })
    }
  })

  return {
    updateFlightField,
    updateFlightStatus,
    logFlightChange,
    batchUpdateFlights,

    // Expose loading states
    isUpdating: updateFlightField.isPending || batchUpdateFlights.isPending,
    isUpdatingStatus: updateFlightStatus.isPending
  }
}

// Helper function để get display name cho field
const getFieldDisplayName = (field: EditableField): string => {
  const fieldNames: Record<EditableField, string> = {
    arr_reg: 'Số đăng ký máy bay đến',
    arr_time: 'Giờ đến',
    arr_staff: 'Nhân viên đến',
    dep_reg: 'Số đăng ký máy bay đi',
    dep_time: 'Giờ đi',
    dep_staff: 'Nhân viên đi',
    remark: 'Ghi chú',
    stt: 'STT'
  }

  return fieldNames[field] || field
}

// Hook để get flight changes
export const useFlightChanges = (flightId?: string) => {
  return useQuery({
    queryKey: flightId ? flightQueryKeys.flightChanges(flightId) : flightQueryKeys.changes(),
    queryFn: async () => {
      const url = flightId
        ? `/api/flights/${flightId}/changes`
        : '/api/flights/changes'

      const response = await apiClient.get(url)

      if (!response.ok) {
        throw new Error('Không thể lấy lịch sử thay đổi')
      }

      return response.json()
    },
    enabled: true,
    staleTime: 30 * 1000, // 30 giây - changes cần fresh
    gcTime: 2 * 60 * 1000 // 2 phút cache time
  })
}
