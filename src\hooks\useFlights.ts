import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import {
  type Flight,
  type CreateFlightData,
  type UpdateFlightData,
  type FlightSearchParams,
  type FlightStats,
  type PaginationData,
  flightSearchSchema,
  createFlightSchema,
  updateFlightSchema
} from '@/lib/flight-schemas'
import { useAuth } from '@/contexts/AuthContext'

// ============================================================================
// QUERY KEYS
// ============================================================================

export const flightQueryKeys = {
  // Base key
  all: ['flights'] as const,
  
  // Lists with filters
  lists: () => [...flightQueryKeys.all, 'list'] as const,
  list: (filters: Partial<FlightSearchParams>) => [...flightQueryKeys.lists(), filters] as const,
  
  // Individual flights
  details: () => [...flightQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...flightQueryKeys.details(), id] as const,
  
  // Statistics
  stats: () => [...flightQueryKeys.all, 'stats'] as const,
  statsByDate: (date: string) => [...flightQueryKeys.stats(), date] as const,
  
  // Changes and history
  changes: () => [...flightQueryKeys.all, 'changes'] as const,
  flightChanges: (flightId: string) => [...flightQueryKeys.changes(), flightId] as const,
  
  // Import related
  imports: () => [...flightQueryKeys.all, 'imports'] as const,
  importHistory: () => [...flightQueryKeys.imports(), 'history'] as const,
} as const

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

interface FlightApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  total?: number
  pagination?: PaginationData
}

interface FlightListResponse {
  data: Flight[]
  total: number
  pagination: PaginationData
}

// ============================================================================
// QUERY HOOKS
// ============================================================================

/**
 * Hook để fetch danh sách flights với pagination và filtering
 */
export const useFlights = (params: Partial<FlightSearchParams> = {}) => {
  const { accessToken } = useAuth()

  // Validate và set default values
  const validatedParams = flightSearchSchema.parse({
    page: 1,
    limit: 2000, // Increase default limit to show all flights (up to 2000)
    sortBy: 'date',
    sortOrder: 'desc',
    type: 'both',
    ...params
  })

  return useQuery({
    queryKey: flightQueryKeys.list(validatedParams),
    queryFn: async (): Promise<FlightListResponse> => {
      // Debug logging
      console.log('🔍 [useFlights] Starting API call with params:', validatedParams)
      console.log('🔍 [useFlights] AccessToken exists:', !!accessToken)
      console.log('🔍 [useFlights] AccessToken preview:', accessToken ? `${accessToken.substring(0, 20)}...` : 'null')

      // Build query string
      const searchParams = new URLSearchParams()

      Object.entries(validatedParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value))
        }
      })

      const apiUrl = `/api/flights?${searchParams.toString()}`
      console.log('🔍 [useFlights] API URL:', apiUrl)

      const response = await fetch(apiUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      console.log('🔍 [useFlights] Response status:', response.status)
      console.log('🔍 [useFlights] Response ok:', response.ok)
      console.log('🔍 [useFlights] Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ [useFlights] API Error:', errorData)
        throw new Error(errorData.error || 'Không thể lấy danh sách lịch bay')
      }

      const result: FlightApiResponse<Flight[]> = await response.json()
      console.log('🔍 [useFlights] Raw API response:', result)

      if (!result.success) {
        console.error('❌ [useFlights] API returned success=false:', result)
        throw new Error(result.error || 'Không thể lấy danh sách lịch bay')
      }

      const finalResult = {
        data: result.data || [],
        total: result.total || 0,
        pagination: result.pagination || {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: 0,
          totalPages: 0
        }
      }

      console.log('✅ [useFlights] Final processed result:', finalResult)
      return finalResult
    },
    
    // Caching strategy
    staleTime: 2 * 60 * 1000, // 2 phút - flight data thay đổi thường xuyên
    gcTime: 5 * 60 * 1000, // 5 phút cache time
    
    // Prefetch next page nếu có
    placeholderData: (previousData) => previousData,
    
    // Enhanced retry strategy
    retry: (failureCount, error) => {
      // Không retry cho client errors (4xx)
      if (error instanceof Error) {
        const message = error.message.toLowerCase()

        // Don't retry for client errors
        if (message.includes('400') || message.includes('401') ||
            message.includes('403') || message.includes('404') ||
            message.includes('422') || message.includes('429')) {
          return false
        }

        // Don't retry for validation errors
        if (message.includes('validation') || message.includes('invalid')) {
          return false
        }
      }

      // Retry up to 3 times for network/server errors
      return failureCount < 3
    },

    // Exponential backoff retry delay
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
  })
}

/**
 * Hook để fetch chi tiết một flight
 */
export const useFlightById = (id: string, enabled = true) => {
  const { accessToken } = useAuth()

  return useQuery({
    queryKey: flightQueryKeys.detail(id),
    queryFn: async (): Promise<Flight> => {
      const response = await fetch(`/api/flights/${id}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Không tìm thấy chuyến bay')
        }
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Không thể lấy thông tin chuyến bay')
      }

      const result: FlightApiResponse<Flight> = await response.json()

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Không thể lấy thông tin chuyến bay')
      }

      return result.data
    },
    enabled: enabled && !!id,
    staleTime: 1 * 60 * 1000, // 1 phút - chi tiết flight cần fresh hơn
    gcTime: 3 * 60 * 1000
  })
}

/**
 * Hook để fetch flight statistics theo ngày
 */
export const useFlightStats = (date?: string) => {
  const { accessToken } = useAuth()
  const targetDate = date || new Date().toISOString().split('T')[0] // Today by default

  return useQuery({
    queryKey: flightQueryKeys.statsByDate(targetDate),
    queryFn: async (): Promise<FlightStats> => {
      const response = await fetch(`/api/flights/stats?date=${targetDate}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Không thể lấy thống kê lịch bay')
      }

      const result: FlightApiResponse<FlightStats> = await response.json()

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Không thể lấy thống kê lịch bay')
      }

      return result.data
    },
    staleTime: 5 * 60 * 1000, // 5 phút - stats ít thay đổi
    gcTime: 10 * 60 * 1000
  })
}

// ============================================================================
// MUTATION HOOKS
// ============================================================================

/**
 * Hook để tạo flight mới
 */
export const useCreateFlight = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const { accessToken } = useAuth()

  return useMutation({
    mutationFn: async (data: CreateFlightData): Promise<Flight> => {
      // Validate data
      const validatedData = createFlightSchema.parse(data)

      const response = await fetch('/api/flights', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(validatedData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Không thể tạo chuyến bay')
      }

      const result: FlightApiResponse<Flight> = await response.json()

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Không thể tạo chuyến bay')
      }

      return result.data
    },
    
    onSuccess: (newFlight) => {
      // Invalidate all flight lists
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })
      
      // Invalidate stats for the flight date
      queryClient.invalidateQueries({ 
        queryKey: flightQueryKeys.statsByDate(newFlight.date) 
      })
      
      // Add to cache
      queryClient.setQueryData(flightQueryKeys.detail(newFlight.id), newFlight)
      
      toast({
        title: 'Thành công',
        description: `Đã tạo chuyến bay STT ${newFlight.stt} thành công`,
      })
    },
    
    onError: (error: Error) => {
      toast({
        title: 'Lỗi',
        description: error.message,
        variant: 'destructive',
      })
    }
  })
}

/**
 * Hook để cập nhật flight
 */
export const useUpdateFlight = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const { accessToken } = useAuth()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateFlightData }): Promise<Flight> => {
      // Validate data
      const validatedData = updateFlightSchema.parse(data)

      const response = await fetch(`/api/flights/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(validatedData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Không thể cập nhật chuyến bay')
      }

      const result: FlightApiResponse<Flight> = await response.json()

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Không thể cập nhật chuyến bay')
      }

      return result.data
    },
    
    onSuccess: (updatedFlight, { id }) => {
      // Update specific flight in cache
      queryClient.setQueryData(flightQueryKeys.detail(id), updatedFlight)
      
      // Invalidate lists that might contain this flight
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })
      
      // Invalidate stats
      queryClient.invalidateQueries({ 
        queryKey: flightQueryKeys.statsByDate(updatedFlight.date) 
      })
      
      toast({
        title: 'Thành công',
        description: 'Đã cập nhật chuyến bay thành công',
      })
    },
    
    onError: (error: Error) => {
      toast({
        title: 'Lỗi',
        description: error.message,
        variant: 'destructive',
      })
    }
  })
}

/**
 * Hook để xóa flight
 */
export const useDeleteFlight = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()
  const { accessToken } = useAuth()

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/flights/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Không thể xóa chuyến bay')
      }

      const result: FlightApiResponse = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Không thể xóa chuyến bay')
      }
    },

    onMutate: async (id) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: flightQueryKeys.detail(id) })

      // Get the flight data before deletion for rollback
      const previousFlight = queryClient.getQueryData<Flight>(flightQueryKeys.detail(id))

      // Remove from cache optimistically
      queryClient.removeQueries({ queryKey: flightQueryKeys.detail(id) })

      return { previousFlight, id }
    },

    onError: (error: Error, id, context) => {
      // Rollback: restore the flight if we had it
      if (context?.previousFlight) {
        queryClient.setQueryData(flightQueryKeys.detail(id), context.previousFlight)
      }

      toast({
        title: 'Lỗi',
        description: error.message,
        variant: 'destructive',
      })
    },

    onSuccess: (_, id, context) => {
      // Invalidate all flight lists
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })

      // Invalidate stats if we know the date
      if (context?.previousFlight?.date) {
        queryClient.invalidateQueries({
          queryKey: flightQueryKeys.statsByDate(context.previousFlight.date)
        })
      }

      // Invalidate changes for this flight
      queryClient.invalidateQueries({
        queryKey: flightQueryKeys.flightChanges(id)
      })

      toast({
        title: 'Thành công',
        description: 'Đã xóa chuyến bay thành công',
      })
    }
  })
}

// ============================================================================
// UTILITY HOOKS & FUNCTIONS
// ============================================================================

/**
 * Hook để prefetch next page
 */
export const usePrefetchFlights = () => {
  const queryClient = useQueryClient()
  const { accessToken } = useAuth()

  return (params: Partial<FlightSearchParams>) => {
    const nextPageParams = { ...params, page: (params.page || 1) + 1 }

    queryClient.prefetchQuery({
      queryKey: flightQueryKeys.list(nextPageParams),
      queryFn: async () => {
        const searchParams = new URLSearchParams()
        Object.entries(nextPageParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            searchParams.append(key, String(value))
          }
        })

        const response = await fetch(`/api/flights?${searchParams.toString()}`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        })
        if (!response.ok) throw new Error('Failed to prefetch')

        const result: FlightApiResponse<Flight[]> = await response.json()
        return {
          data: result.data || [],
          total: result.total || 0,
          pagination: result.pagination || { page: 1, limit: 2000, total: 0, totalPages: 0 }
        }
      },
      staleTime: 2 * 60 * 1000
    })
  }
}

/**
 * Hook để invalidate flight cache
 */
export const useInvalidateFlights = () => {
  const queryClient = useQueryClient()

  return {
    // Invalidate all flights
    invalidateAll: () => {
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.all })
    },

    // Invalidate specific flight
    invalidateFlight: (id: string) => {
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.detail(id) })
    },

    // Invalidate flight lists
    invalidateLists: () => {
      queryClient.invalidateQueries({ queryKey: flightQueryKeys.lists() })
    },

    // Invalidate stats
    invalidateStats: (date?: string) => {
      if (date) {
        queryClient.invalidateQueries({ queryKey: flightQueryKeys.statsByDate(date) })
      } else {
        queryClient.invalidateQueries({ queryKey: flightQueryKeys.stats() })
      }
    },

    // Invalidate changes
    invalidateChanges: (flightId?: string) => {
      if (flightId) {
        queryClient.invalidateQueries({ queryKey: flightQueryKeys.flightChanges(flightId) })
      } else {
        queryClient.invalidateQueries({ queryKey: flightQueryKeys.changes() })
      }
    }
  }
}

/**
 * Hook để get cached flight data
 */
export const useFlightCache = () => {
  const queryClient = useQueryClient()

  return {
    // Get flight from cache
    getFlight: (id: string): Flight | undefined => {
      return queryClient.getQueryData<Flight>(flightQueryKeys.detail(id))
    },

    // Set flight in cache
    setFlight: (id: string, flight: Flight) => {
      queryClient.setQueryData(flightQueryKeys.detail(id), flight)
    },

    // Get flights list from cache
    getFlightsList: (params: Partial<FlightSearchParams>): FlightListResponse | undefined => {
      return queryClient.getQueryData<FlightListResponse>(flightQueryKeys.list(params))
    },

    // Update flight in all relevant caches
    updateFlightInCache: (id: string, updater: (old: Flight) => Flight) => {
      // Update detail cache
      queryClient.setQueryData<Flight>(flightQueryKeys.detail(id), updater)

      // Update in all list caches that might contain this flight
      queryClient.getQueriesData<FlightListResponse>({ queryKey: flightQueryKeys.lists() })
        .forEach(([queryKey, data]) => {
          if (data?.data) {
            const updatedData = {
              ...data,
              data: data.data.map(flight =>
                flight.id === id ? updater(flight) : flight
              )
            }
            queryClient.setQueryData(queryKey, updatedData)
          }
        })
    }
  }
}

// ============================================================================
// CACHING STRATEGY DOCUMENTATION
// ============================================================================

/**
 * CACHING STRATEGY OVERVIEW:
 *
 * 1. **Query Keys Hierarchy**:
 *    - ['flights'] - Base key
 *    - ['flights', 'list', filters] - Lists with specific filters
 *    - ['flights', 'detail', id] - Individual flights
 *    - ['flights', 'stats', date] - Statistics by date
 *    - ['flights', 'changes', flightId] - Changes for specific flight
 *
 * 2. **Stale Time Strategy**:
 *    - Lists: 2 minutes (frequent updates expected)
 *    - Details: 1 minute (need fresh data for editing)
 *    - Stats: 5 minutes (less frequent changes)
 *
 * 3. **Cache Time (GC Time)**:
 *    - Lists: 5 minutes
 *    - Details: 3 minutes
 *    - Stats: 10 minutes
 *
 * 4. **Invalidation Strategy**:
 *    - Create: Invalidate lists + stats for date
 *    - Update: Update detail cache + invalidate lists + stats
 *    - Delete: Remove detail + invalidate lists + stats + changes
 *    - Field update: Optimistic update + invalidate on settle
 *
 * 5. **Optimistic Updates**:
 *    - Field updates (via useFlightMutations)
 *    - Delete operations (with rollback)
 *
 * 6. **Prefetching**:
 *    - Next page prefetching available
 *    - Triggered manually when needed
 *
 * 7. **Error Handling**:
 *    - Retry strategy: max 2 retries for 5xx errors
 *    - No retry for 4xx errors
 *    - Rollback for failed optimistic updates
 */
