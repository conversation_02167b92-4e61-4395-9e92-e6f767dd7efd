import { useQuery } from '@tanstack/react-query'
import {
  type Flight,
  type FlightSearchParams,
  type FlightStats,
  flightSearchSchema
} from '@/lib/flight-schemas'

// ============================================================================
// TYPES
// ============================================================================

interface FlightApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  total?: number
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

interface FlightListResponse {
  data: Flight[]
  total: number
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// ============================================================================
// QUERY KEYS
// ============================================================================

export const publicFlightQueryKeys = {
  all: ['public-flights'] as const,
  lists: () => [...publicFlightQueryKeys.all, 'list'] as const,
  list: (params: Partial<FlightSearchParams>) => [...publicFlightQueryKeys.lists(), params] as const,
  details: () => [...publicFlightQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...publicFlightQueryKeys.details(), id] as const,
  stats: () => [...publicFlightQueryKeys.all, 'stats'] as const,
  statsByDate: (date: string) => [...publicFlightQueryKeys.stats(), date] as const,
}

// ============================================================================
// HOOKS
// ============================================================================

/**
 * Hook để fetch danh sách flights từ public API (không cần authentication)
 */
export const usePublicFlights = (params: Partial<FlightSearchParams> = {}) => {
  // Validate và set default values
  const validatedParams = flightSearchSchema.parse({
    page: 1,
    limit: 2000, // Increase default limit to show all flights (up to 2000)
    sortBy: 'date',
    sortOrder: 'desc',
    type: 'both',
    ...params
  })

  return useQuery({
    queryKey: publicFlightQueryKeys.list(validatedParams),
    queryFn: async (): Promise<FlightListResponse> => {
      // Debug logging
      console.log('🔍 [usePublicFlights] Starting public API call with params:', validatedParams)

      // Build query string
      const searchParams = new URLSearchParams()

      Object.entries(validatedParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value))
        }
      })

      const url = `/api/public/flights?${searchParams.toString()}`
      console.log('🔍 [usePublicFlights] Fetching URL:', url)

      // Fetch without authentication headers
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      console.log('🔍 [usePublicFlights] Response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ [usePublicFlights] HTTP error:', response.status, errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: Không thể lấy danh sách lịch bay`)
      }

      const result: FlightApiResponse<Flight[]> = await response.json()
      console.log('🔍 [usePublicFlights] Raw API response:', result)

      if (!result.success) {
        console.error('❌ [usePublicFlights] API returned success=false:', result)
        throw new Error(result.error || 'Không thể lấy danh sách lịch bay')
      }

      const finalResult = {
        data: result.data || [],
        total: result.total || 0,
        pagination: result.pagination || {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: 0,
          totalPages: 0
        }
      }

      console.log('✅ [usePublicFlights] Final processed result:', finalResult)
      return finalResult
    },
    staleTime: 2 * 60 * 1000, // 2 phút - public data có thể cache lâu hơn
    gcTime: 5 * 60 * 1000, // 5 phút
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
}

/**
 * Hook để fetch chi tiết flight từ public API
 */
export const usePublicFlight = (id: string, enabled = true) => {
  return useQuery({
    queryKey: publicFlightQueryKeys.detail(id),
    queryFn: async (): Promise<Flight> => {
      const response = await fetch(`/api/public/flights/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Không tìm thấy chuyến bay')
        }
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Không thể lấy thông tin chuyến bay')
      }

      const result: FlightApiResponse<Flight> = await response.json()

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Không thể lấy thông tin chuyến bay')
      }

      return result.data
    },
    enabled: enabled && !!id,
    staleTime: 1 * 60 * 1000, // 1 phút
    gcTime: 3 * 60 * 1000
  })
}

/**
 * Hook để fetch flight statistics từ public API
 */
export const usePublicFlightStats = (date?: string) => {
  const targetDate = date || new Date().toISOString().split('T')[0] // Today by default

  return useQuery({
    queryKey: publicFlightQueryKeys.statsByDate(targetDate),
    queryFn: async (): Promise<FlightStats> => {
      const response = await fetch(`/api/public/flights/stats?date=${targetDate}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Không thể lấy thống kê lịch bay')
      }

      const result: FlightApiResponse<FlightStats> = await response.json()

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Không thể lấy thống kê lịch bay')
      }

      return result.data
    },
    staleTime: 5 * 60 * 1000, // 5 phút - stats ít thay đổi
    gcTime: 10 * 60 * 1000
  })
}

/**
 * Prefetch next page for better UX
 */
export const usePrefetchPublicFlights = (currentParams: Partial<FlightSearchParams>) => {
  // Implementation similar to useFlights but for public API
  // This can be added later if needed for performance optimization
}
