import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { apiClient } from '@/lib/api-client'
import { flightQueryKeys } from './useFlights'
import { cellColorQueryKeys } from './useCellColors'

// ============================================================================
// ROW OPERATIONS TYPES
// ============================================================================

interface InsertRowParams {
  flightId: string
  type: 'arrival' | 'departure'
  position: 'before' | 'after'
  date: string
}

interface DeleteRowParams {
  flightId: string
  type: 'arrival' | 'departure'
  date: string
}

interface InsertRowResponse {
  success: boolean
  data?: {
    id: string
    type: 'arrival' | 'departure'
    position: 'before' | 'after'
    newSTT: number
    originalSTT: number
  }
  error?: string
}

interface DeleteRowResponse {
  success: boolean
  data?: {
    id: string
    type: 'arrival' | 'departure'
    stt: number
  }
  error?: string
}

// ============================================================================
// ROW OPERATIONS SERVICE FUNCTIONS
// ============================================================================

const insertFlightRow = async (params: InsertRowParams): Promise<InsertRowResponse> => {
  try {
    const response = await apiClient.post(`/api/flights/${params.flightId}/insert-row`, {
      type: params.type,
      position: params.position,
      date: params.date
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to insert row')
    }

    return await response.json()
  } catch (error) {
    console.error('Error inserting flight row:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

const deleteFlightRow = async (params: DeleteRowParams): Promise<DeleteRowResponse> => {
  try {
    // Use POST method for delete operation with body data
    const response = await apiClient.post(`/api/flights/${params.flightId}/delete-row`, {
      type: params.type,
      date: params.date
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete row')
    }

    return await response.json()
  } catch (error) {
    console.error('Error deleting flight row:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// ============================================================================
// ROW OPERATIONS HOOKS
// ============================================================================

/**
 * Hook để insert flight row
 */
export const useInsertFlightRow = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: insertFlightRow,

    onSuccess: (data, variables) => {
      if (data.success) {
        // Invalidate all flight queries to refetch with new data
        queryClient.invalidateQueries({
          queryKey: flightQueryKeys.all
        })

        // Invalidate cell colors as STT changes might affect color mappings
        queryClient.invalidateQueries({
          queryKey: cellColorQueryKeys.all
        })

        toast({
          title: 'Thành công',
          description: `Đã chèn hàng ${variables.type === 'arrival' ? 'đến' : 'đi'} ${variables.position === 'before' ? 'phía trên' : 'phía dưới'}`,
        })
      } else {
        throw new Error(data.error || 'Insert row failed')
      }
    },

    onError: (error: Error, variables) => {
      toast({
        title: 'Lỗi',
        description: error.message || `Không thể chèn hàng ${variables.type === 'arrival' ? 'đến' : 'đi'}`,
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook để delete flight row
 */
export const useDeleteFlightRow = () => {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: deleteFlightRow,

    onSuccess: (data, variables) => {
      if (data.success) {
        // Invalidate all flight queries to refetch with new data
        queryClient.invalidateQueries({
          queryKey: flightQueryKeys.all
        })

        // Invalidate cell colors as the flight might be deleted
        queryClient.invalidateQueries({
          queryKey: cellColorQueryKeys.all
        })

        toast({
          title: 'Thành công',
          description: `Đã xóa hàng ${variables.type === 'arrival' ? 'đến' : 'đi'}`,
        })
      } else {
        throw new Error(data.error || 'Delete row failed')
      }
    },

    onError: (error: Error, variables) => {
      toast({
        title: 'Lỗi',
        description: error.message || `Không thể xóa hàng ${variables.type === 'arrival' ? 'đến' : 'đi'}`,
        variant: 'destructive',
      })
    },
  })
}

// ============================================================================
// COMBINED ROW OPERATIONS HOOK
// ============================================================================

/**
 * Hook tổng hợp tất cả row operations
 */
export const useRowOperations = () => {
  const insertRow = useInsertFlightRow()
  const deleteRow = useDeleteFlightRow()

  // Helper function để insert row với validation
  const insertFlightRowSafe = async (
    flightId: string,
    type: 'arrival' | 'departure',
    position: 'before' | 'after',
    date: string
  ) => {
    if (!flightId || !type || !position || !date) {
      throw new Error('Missing required parameters for insert row')
    }

    return insertRow.mutateAsync({
      flightId,
      type,
      position,
      date
    })
  }

  // Helper function để delete row với validation
  const deleteFlightRowSafe = async (
    flightId: string,
    type: 'arrival' | 'departure',
    date: string
  ) => {
    if (!flightId || !type || !date) {
      throw new Error('Missing required parameters for delete row')
    }

    return deleteRow.mutateAsync({
      flightId,
      type,
      date
    })
  }

  // Helper function để insert row before
  const insertRowBefore = (
    flightId: string,
    type: 'arrival' | 'departure',
    date: string
  ) => insertFlightRowSafe(flightId, type, 'before', date)

  // Helper function để insert row after
  const insertRowAfter = (
    flightId: string,
    type: 'arrival' | 'departure',
    date: string
  ) => insertFlightRowSafe(flightId, type, 'after', date)

  // Check if any operation is loading
  const isLoading = insertRow.isPending || deleteRow.isPending

  return {
    insertRow,
    deleteRow,
    insertFlightRowSafe,
    deleteFlightRowSafe,
    insertRowBefore,
    insertRowAfter,
    isLoading,
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Validate row operation parameters
 */
export const validateRowOperationParams = (
  flightId: string,
  type: 'arrival' | 'departure',
  date: string
): { isValid: boolean; error?: string } => {
  if (!flightId) {
    return { isValid: false, error: 'Flight ID is required' }
  }

  if (!type || !['arrival', 'departure'].includes(type)) {
    return { isValid: false, error: 'Valid type (arrival/departure) is required' }
  }

  if (!date || !/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    return { isValid: false, error: 'Valid date (YYYY-MM-DD) is required' }
  }

  return { isValid: true }
}

/**
 * Get row operation confirmation message
 */
export const getRowOperationMessage = (
  operation: 'insert' | 'delete',
  type: 'arrival' | 'departure',
  position?: 'before' | 'after'
): string => {
  const typeLabel = type === 'arrival' ? 'đến' : 'đi'
  
  if (operation === 'insert') {
    const positionLabel = position === 'before' ? 'phía trên' : 'phía dưới'
    return `Bạn có chắc muốn chèn hàng ${typeLabel} ${positionLabel}?`
  } else {
    return `Bạn có chắc muốn xóa hàng ${typeLabel}? Thao tác này không thể hoàn tác.`
  }
}
