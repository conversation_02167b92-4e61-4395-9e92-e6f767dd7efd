// API client với automatic token refresh
import { useAuth } from '@/contexts/AuthContext'

interface ApiRequestOptions extends RequestInit {
  skipAuth?: boolean
}

export const createApiClient = () => {
  const makeAuthenticatedRequest = async (
    url: string, 
    options: ApiRequestOptions = {}
  ): Promise<Response> => {
    const { skipAuth = false, ...requestOptions } = options
    
    // Get current access token
    let accessToken = localStorage.getItem('accessToken')
    
    if (!skipAuth && !accessToken) {
      throw new Error('Không có token xác thực. Vui lòng đăng nhập lại.')
    }
    
    // Make initial request
    const headers = {
      'Content-Type': 'application/json',
      ...requestOptions.headers,
      ...(accessToken && !skipAuth && { 'Authorization': `Bearer ${accessToken}` }),
    }
    
    let response = await fetch(url, {
      ...requestOptions,
      headers,
    })
    
    // If 401 and not skipping auth, try to refresh token
    if (response.status === 401 && !skipAuth) {
      console.log('Token expired, trying to refresh...')
      
      const refreshToken = localStorage.getItem('refreshToken')
      if (!refreshToken) {
        throw new Error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.')
      }
      
      // Try to refresh token
      const refreshResponse = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      })
      
      if (refreshResponse.ok) {
        const refreshData = await refreshResponse.json()
        localStorage.setItem('accessToken', refreshData.accessToken)
        
        // Retry original request with new token
        response = await fetch(url, {
          ...requestOptions,
          headers: {
            ...headers,
            'Authorization': `Bearer ${refreshData.accessToken}`,
          },
        })
      } else {
        // Refresh failed, clear auth and redirect to login
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('user')
        window.location.href = '/login'
        throw new Error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.')
      }
    }
    
    return response
  }
  
  return {
    get: (url: string, options?: ApiRequestOptions) => 
      makeAuthenticatedRequest(url, { ...options, method: 'GET' }),
    
    post: (url: string, data?: any, options?: ApiRequestOptions) => 
      makeAuthenticatedRequest(url, { 
        ...options, 
        method: 'POST', 
        body: data ? JSON.stringify(data) : undefined 
      }),
    
    patch: (url: string, data?: any, options?: ApiRequestOptions) => 
      makeAuthenticatedRequest(url, { 
        ...options, 
        method: 'PATCH', 
        body: data ? JSON.stringify(data) : undefined 
      }),
    
    put: (url: string, data?: any, options?: ApiRequestOptions) => 
      makeAuthenticatedRequest(url, { 
        ...options, 
        method: 'PUT', 
        body: data ? JSON.stringify(data) : undefined 
      }),
    
    delete: (url: string, options?: ApiRequestOptions) => 
      makeAuthenticatedRequest(url, { ...options, method: 'DELETE' }),
  }
}

export const apiClient = createApiClient()
