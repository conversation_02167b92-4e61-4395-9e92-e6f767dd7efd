// API client cho Quan Ly TTB
import { API_BASE_URL } from './config';

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  total?: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Database types (match với schema)
export interface WalkieTalkie {
  id: number;
  name: string;
  status: 'available' | 'assigned' | 'maintenance' | 'lost';
  assigned_to?: string;
  created_at: string;
  updated_at: string;
}

export interface AccessCard {
  id: number;
  name: string;
  status: 'active' | 'inactive' | 'expired' | 'lost';
  assigned_to?: string;
  created_at: string;
  updated_at: string;
}

export interface ActivityHistory {
  id: number;
  item_type: 'walkie_talkie' | 'access_card';
  item_id: number;
  action: 'create' | 'update' | 'delete' | 'assign' | 'return' | 'maintenance';
  description?: string;
  user_id?: string;
  borrower_name?: string; // Tên người mượn thiết bị
  issuer_name?: string; // Tên người giao từ bảng users
  old_values?: string; // JSON string
  new_values?: string; // JSON string
  created_at: string;
}

// Request types
export interface CreateWalkieTalkieRequest {
  name: string;
  status?: 'available' | 'assigned' | 'maintenance' | 'lost';
  assigned_to?: string;
}

export interface UpdateWalkieTalkieRequest extends Partial<CreateWalkieTalkieRequest> {}

export interface CreateAccessCardRequest {
  name: string;
  status?: 'active' | 'inactive' | 'expired' | 'lost';
  assigned_to?: string;
}

export interface UpdateAccessCardRequest extends Partial<CreateAccessCardRequest> {}

export interface CreateActivityRequest {
  item_type: 'walkie_talkie' | 'access_card';
  item_id: number;
  action: 'create' | 'update' | 'delete' | 'assign' | 'return' | 'maintenance';
  description?: string;
  user_id?: string;
  old_values?: any;
  new_values?: any;
}

// API Client class
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;

    // Get access token from localStorage
    const accessToken = localStorage.getItem('accessToken');

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(accessToken && { 'Authorization': `Bearer ${accessToken}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      // Handle 401 Unauthorized - token might be expired
      if (response.status === 401) {
        // Try to refresh token
        const refreshed = await this.refreshToken();
        if (refreshed) {
          // Retry the original request with new token
          const newAccessToken = localStorage.getItem('accessToken');
          const retryConfig: RequestInit = {
            ...config,
            headers: {
              ...config.headers,
              'Authorization': `Bearer ${newAccessToken}`,
            },
          };
          const retryResponse = await fetch(url, retryConfig);
          const retryData = await retryResponse.json();

          if (!retryResponse.ok) {
            throw new Error(retryData.error || `HTTP error! status: ${retryResponse.status}`);
          }

          return retryData;
        } else {
          // Refresh failed, redirect to login
          this.handleAuthError();
          throw new Error('Session expired. Please login again.');
        }
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private async refreshToken(): Promise<boolean> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) return false;

      const response = await fetch(`${this.baseUrl}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('accessToken', data.accessToken);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }

  private handleAuthError(): void {
    // Clear auth data
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');

    // Redirect to login page
    window.location.href = '/login';
  }

  // Walkie Talkies API
  async getWalkieTalkies(): Promise<ApiResponse<WalkieTalkie[]>> {
    return this.request<WalkieTalkie[]>('/api/walkie-talkies');
  }

  async getWalkieTalkie(id: number): Promise<ApiResponse<WalkieTalkie>> {
    return this.request<WalkieTalkie>(`/api/walkie-talkies/${id}`);
  }

  async createWalkieTalkie(data: CreateWalkieTalkieRequest): Promise<ApiResponse<WalkieTalkie>> {
    return this.request<WalkieTalkie>('/api/walkie-talkies', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateWalkieTalkie(id: number, data: UpdateWalkieTalkieRequest): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/walkie-talkies/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteWalkieTalkie(id: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/walkie-talkies/${id}`, {
      method: 'DELETE',
    });
  }

  // Access Cards API
  async getAccessCards(): Promise<ApiResponse<AccessCard[]>> {
    return this.request<AccessCard[]>('/api/access-cards');
  }

  async getAccessCard(id: number): Promise<ApiResponse<AccessCard>> {
    return this.request<AccessCard>(`/api/access-cards/${id}`);
  }

  async createAccessCard(data: CreateAccessCardRequest): Promise<ApiResponse<AccessCard>> {
    return this.request<AccessCard>('/api/access-cards', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateAccessCard(id: number, data: UpdateAccessCardRequest): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/access-cards/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteAccessCard(id: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/api/access-cards/${id}`, {
      method: 'DELETE',
    });
  }

  async getAccessCardsByUser(userId: string): Promise<ApiResponse<AccessCard[]>> {
    return this.request<AccessCard[]>(`/api/access-cards/by-user/${userId}`);
  }

  // Activity History API
  async getActivityHistory(params?: {
    page?: number;
    limit?: number;
    type?: 'walkie_talkie' | 'access_card';
    action?: string;
  }): Promise<ApiResponse<ActivityHistory[]>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.type) searchParams.set('type', params.type);
    if (params?.action) searchParams.set('action', params.action);

    const query = searchParams.toString();
    return this.request<ActivityHistory[]>(`/api/history${query ? `?${query}` : ''}`);
  }

  async getActivityRecord(id: number): Promise<ApiResponse<ActivityHistory>> {
    return this.request<ActivityHistory>(`/api/history/${id}`);
  }

  async createActivityRecord(data: CreateActivityRequest): Promise<ApiResponse<ActivityHistory>> {
    return this.request<ActivityHistory>('/api/history', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getItemHistory(type: 'walkie_talkie' | 'access_card', id: number): Promise<ApiResponse<ActivityHistory[]>> {
    return this.request<ActivityHistory[]>(`/api/history/item/${type}/${id}`);
  }

  async getUserHistory(userId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<ActivityHistory[]>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.request<ActivityHistory[]>(`/api/history/user/${userId}${query ? `?${query}` : ''}`);
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    return this.request<any>('/');
  }
}

// Export singleton instance
export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;
