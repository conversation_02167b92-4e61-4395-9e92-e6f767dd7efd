import { apiClient } from './api-client'
import type { CellColor, UpdateCellColor, BatchCellColors } from './flight-schemas'

// ============================================================================
// CELL COLORS SERVICE FUNCTIONS
// ============================================================================

/**
 * Get cell colors for a specific flight
 */
export const getCellColors = async (flightId: string): Promise<{
  success: boolean
  data?: {
    flight_id: string
    colors: Record<string, string>
    details: CellColor[]
  }
  error?: string
}> => {
  try {
    const response = await apiClient.get(`/api/flight-cell-colors/${flightId}`)
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to fetch cell colors')
    }

    return await response.json()
  } catch (error) {
    console.error('Error fetching cell colors:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Get cell colors for multiple flights (batch)
 */
export const getBatchCellColors = async (flightIds: string[]): Promise<{
  success: boolean
  data?: Record<string, Record<string, string>>
  error?: string
}> => {
  try {
    if (flightIds.length === 0) {
      return { success: true, data: {} }
    }

    const flightIdsParam = flightIds.join(',')
    const response = await apiClient.get(`/api/flight-cell-colors/batch?flightIds=${flightIdsParam}`)
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to fetch batch cell colors')
    }

    return await response.json()
  } catch (error) {
    console.error('Error fetching batch cell colors:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Update cell color for a specific field
 */
export const updateCellColor = async (
  flightId: string,
  fieldName: string,
  colorValue: string
): Promise<{
  success: boolean
  data?: {
    flight_id: string
    field_name: string
    color_value: string
  }
  error?: string
}> => {
  try {
    const response = await apiClient.put(`/api/flight-cell-colors/${flightId}/${fieldName}`, {
      color_value: colorValue
    })
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update cell color')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating cell color:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Remove cell color for a specific field
 */
export const deleteCellColor = async (
  flightId: string,
  fieldName: string
): Promise<{
  success: boolean
  data?: {
    flight_id: string
    field_name: string
    deleted: boolean
  }
  error?: string
}> => {
  try {
    const response = await apiClient.delete(`/api/flight-cell-colors/${flightId}/${fieldName}`)
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete cell color')
    }

    return await response.json()
  } catch (error) {
    console.error('Error deleting cell color:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Batch update multiple cell colors
 */
export const batchUpdateCellColors = async (
  colors: Array<{
    flight_id: string
    field_name: string
    color_value: string
  }>
): Promise<{
  success: boolean
  data?: {
    total: number
    updated: number
    errors?: Array<{
      flight_id: string
      field_name: string
      error: string
    }>
  }
  error?: string
}> => {
  try {
    const response = await apiClient.post('/api/flight-cell-colors/batch', {
      colors
    })
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to batch update cell colors')
    }

    return await response.json()
  } catch (error) {
    console.error('Error batch updating cell colors:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Validate hex color format
 */
export const isValidHexColor = (color: string): boolean => {
  return /^#[0-9A-Fa-f]{6}$/.test(color)
}

/**
 * Convert RGB to hex color
 */
export const rgbToHex = (r: number, g: number, b: number): string => {
  const toHex = (n: number) => {
    const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

/**
 * Convert hex color to RGB
 */
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

/**
 * Get contrast color (black or white) for a given background color
 */
export const getContrastColor = (backgroundColor: string): string => {
  const rgb = hexToRgb(backgroundColor)
  if (!rgb) return '#000000'
  
  // Calculate luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255
  
  // Return black for light backgrounds, white for dark backgrounds
  return luminance > 0.5 ? '#000000' : '#ffffff'
}

/**
 * Generate CSS style object for cell with color
 */
export const getCellColorStyle = (backgroundColor?: string): React.CSSProperties => {
  if (!backgroundColor || !isValidHexColor(backgroundColor)) {
    return {}
  }

  return {
    backgroundColor,
    color: getContrastColor(backgroundColor)
  }
}

/**
 * Predefined color palette for quick selection
 */
export const PRESET_COLORS = [
  '#ffffff', // White
  '#f3f4f6', // Gray 100
  '#e5e7eb', // Gray 200
  '#d1d5db', // Gray 300
  '#fef2f2', // Red 50
  '#fee2e2', // Red 100
  '#fca5a5', // Red 300
  '#ef4444', // Red 500
  '#f0fdf4', // Green 50
  '#dcfce7', // Green 100
  '#86efac', // Green 300
  '#22c55e', // Green 500
  '#eff6ff', // Blue 50
  '#dbeafe', // Blue 100
  '#93c5fd', // Blue 300
  '#3b82f6', // Blue 500
  '#fefce8', // Yellow 50
  '#fef3c7', // Yellow 100
  '#fde047', // Yellow 300
  '#eab308', // Yellow 500
  '#fdf4ff', // Purple 50
  '#f3e8ff', // Purple 100
  '#c084fc', // Purple 300
  '#a855f7', // Purple 500
] as const

/**
 * Get color name for display purposes
 */
export const getColorName = (hexColor: string): string => {
  const colorNames: Record<string, string> = {
    '#ffffff': 'Trắng',
    '#f3f4f6': 'Xám nhạt',
    '#e5e7eb': 'Xám',
    '#d1d5db': 'Xám đậm',
    '#fef2f2': 'Đỏ nhạt',
    '#fee2e2': 'Đỏ',
    '#fca5a5': 'Đỏ vừa',
    '#ef4444': 'Đỏ đậm',
    '#f0fdf4': 'Xanh lá nhạt',
    '#dcfce7': 'Xanh lá',
    '#86efac': 'Xanh lá vừa',
    '#22c55e': 'Xanh lá đậm',
    '#eff6ff': 'Xanh dương nhạt',
    '#dbeafe': 'Xanh dương',
    '#93c5fd': 'Xanh dương vừa',
    '#3b82f6': 'Xanh dương đậm',
    '#fefce8': 'Vàng nhạt',
    '#fef3c7': 'Vàng',
    '#fde047': 'Vàng vừa',
    '#eab308': 'Vàng đậm',
    '#fdf4ff': 'Tím nhạt',
    '#f3e8ff': 'Tím',
    '#c084fc': 'Tím vừa',
    '#a855f7': 'Tím đậm',
  }

  return colorNames[hexColor.toLowerCase()] || hexColor
}
