// Configuration utilities cho Quan Ly TTB

/**
 * Lấy API base URL dựa trên environment
 * - Development: http://localhost:8787 (Wrangler dev server)
 * - Production: '' (relative URLs - same domain)
 */
export const getApiBaseUrl = (): string => {
  // Check if we're in development mode
  if (import.meta.env.DEV) {
    return 'http://localhost:8787'; // Wrangler dev server
  }

  // Check if we're running on localhost (any port)
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return 'http://localhost:8787'; // Local development
  }

  // Production - use same domain (relative URLs)
  return '';
};

/**
 * API base URL instance
 */
export const API_BASE_URL = getApiBaseUrl();

/**
 * Environment detection utilities
 */
export const isDevelopment = (): boolean => {
  return import.meta.env.DEV || (typeof window !== 'undefined' && window.location.hostname === 'localhost');
};

export const isProduction = (): boolean => {
  return !isDevelopment();
};
