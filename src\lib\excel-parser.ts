import * as XLSX from 'xlsx'
import {
  excelFlightRowSchema,
  type ExcelFlightRow,
  type ExcelImportResult
} from './flight-schemas'
import { validateExcelBatch } from './flight-validator'

// ============================================================================
// EXCEL PARSING CONFIGURATION
// ============================================================================

export interface ExcelParseOptions {
  sheetName?: string
  skipRows?: number
  maxRows?: number
  validateData?: boolean
  onProgress?: (progress: number, processed: number, total: number) => void
}

export interface ExcelParseResult {
  success: boolean
  data?: ExcelFlightRow[]
  errors?: Array<{
    row: number
    field?: string
    message: string
    value?: any
  }>
  warnings?: Array<{
    row: number
    message: string
    value?: any
  }>
  metadata: {
    filename: string
    sheetName: string
    totalRows: number
    processedRows: number
    skippedRows: number
    columns: string[]
    processingTimeMs: number
    hasRemarkColumn: boolean
    detectedFormat: 'new' | 'legacy'
  }
}

// ============================================================================
// COLUMN MAPPING AND FORMAT DETECTION
// ============================================================================

// Standard column order for new format
// STT | FLT | FROM | REG | STA | STAFF | FLT | TO | REG | STD | STAFF | [REMARK]
const NEW_FORMAT_COLUMNS = [
  'stt',      // 0: STT
  'arr_flt',  // 1: FLT (Arrival)
  'arr_from', // 2: FROM
  'arr_reg',  // 3: REG (Arrival)
  'arr_time', // 4: STA
  'arr_staff',// 5: STAFF (Arrival)
  'dep_flt',  // 6: FLT (Departure)
  'dep_to',   // 7: TO
  'dep_reg',  // 8: REG (Departure)
  'dep_time', // 9: STD
  'dep_staff',// 10: STAFF (Departure)
  'remark'    // 11: REMARK (Optional)
]

// Legacy format mapping
const LEGACY_COLUMN_MAPPING: Record<string, string> = {
  // STT column
  'STT': 'stt',
  'Số thứ tự': 'stt',
  'No.': 'stt',

  // Arrival columns
  'Chuyến bay đến': 'arr_flt',
  'Arrival Flight': 'arr_flt',
  'ARR FLT': 'arr_flt',
  'FLT (ARR)': 'arr_flt',
  'Từ': 'arr_from',
  'From': 'arr_from',
  'ARR FROM': 'arr_from',
  'Số hiệu máy bay đến': 'arr_reg',
  'Arrival Aircraft': 'arr_reg',
  'ARR REG': 'arr_reg',
  'REG (ARR)': 'arr_reg',
  'Giờ đến': 'arr_time',
  'Arrival Time': 'arr_time',
  'ARR TIME': 'arr_time',
  'STA': 'arr_time',
  'Nhân viên đón': 'arr_staff',
  'Arrival Staff': 'arr_staff',
  'ARR STAFF': 'arr_staff',
  'STAFF (ARR)': 'arr_staff',

  // Departure columns
  'Chuyến bay đi': 'dep_flt',
  'Departure Flight': 'dep_flt',
  'DEP FLT': 'dep_flt',
  'FLT (DEP)': 'dep_flt',
  'Đến': 'dep_to',
  'To': 'dep_to',
  'DEP TO': 'dep_to',
  'TO': 'dep_to',
  'Số hiệu máy bay đi': 'dep_reg',
  'Departure Aircraft': 'dep_reg',
  'DEP REG': 'dep_reg',
  'REG (DEP)': 'dep_reg',
  'Giờ đi': 'dep_time',
  'Departure Time': 'dep_time',
  'DEP TIME': 'dep_time',
  'STD': 'dep_time',
  'Nhân viên tiễn': 'dep_staff',
  'Departure Staff': 'dep_staff',
  'DEP STAFF': 'dep_staff',
  'STAFF (DEP)': 'dep_staff',

  // Optional REMARK column
  'REMARK': 'remark',
  'Ghi chú': 'remark',
  'Note': 'remark'
}

// ============================================================================
// EXCEL PARSING FUNCTIONS
// ============================================================================

/**
 * Detect Excel format based on headers
 */
const detectExcelFormat = (headers: string[]): 'new' | 'legacy' => {
  if (!Array.isArray(headers) || headers.length === 0) {
    console.warn('⚠️ Invalid headers for format detection:', headers)
    return 'legacy'
  }

  try {
    const normalizedHeaders = headers.map(h =>
      (h || '').toString().trim().toUpperCase().replace(/[()]/g, '').trim()
    )

    console.log('🔍 Detecting format for headers:', normalizedHeaders)

    // Check for new format patterns
    // Required: STT, FROM, STA, TO, STD
    // Duplicated: FLT (2x), REG (2x), STAFF (2x)
    const requiredUnique = ['STT', 'FROM', 'STA', 'TO', 'STD']
    const requiredDuplicate = ['FLT', 'REG', 'STAFF']

    const hasRequiredUnique = requiredUnique.every(pattern =>
      normalizedHeaders.some(h => h === pattern || h.includes(pattern))
    )

    const hasDuplicates = requiredDuplicate.every(pattern => {
      const count = normalizedHeaders.filter(h =>
        h === pattern || h.includes(pattern) || h.startsWith(pattern)
      ).length
      return count >= 2
    })

    const isNewFormat = hasRequiredUnique && hasDuplicates
    console.log(`🔍 Format detection: ${isNewFormat ? 'NEW' : 'LEGACY'}`)

    return isNewFormat ? 'new' : 'legacy'
  } catch (error) {
    console.error('❌ Error in detectExcelFormat:', error)
    return 'legacy'
  }
}

/**
 * Map headers to column names for new format
 */
const mapHeadersNewFormat = (headers: string[]): Record<number, string> => {
  const mapping: Record<number, string> = {}

  if (!Array.isArray(headers)) {
    console.error('❌ Invalid headers for new format mapping:', headers)
    return mapping
  }

  try {
    console.log('🔍 Mapping new format headers:', headers)

    // Track occurrences for duplicate columns
    const occurrences: Record<string, number> = {}

    for (let i = 0; i < headers.length; i++) {
      const header = (headers[i] || '').toString().trim().toUpperCase()
      const cleanHeader = header.replace(/[()]/g, '').trim()

      // Map based on position and pattern
      if (cleanHeader === 'STT') {
        mapping[i] = 'stt'
      } else if (cleanHeader === 'FLT' || header.includes('FLT')) {
        const count = occurrences['FLT'] || 0
        if (header.includes('ARR') || count === 0) {
          mapping[i] = 'arr_flt'
        } else {
          mapping[i] = 'dep_flt'
        }
        occurrences['FLT'] = count + 1
      } else if (cleanHeader === 'FROM') {
        mapping[i] = 'arr_from'
      } else if (cleanHeader === 'REG' || header.includes('REG')) {
        const count = occurrences['REG'] || 0
        if (header.includes('ARR') || count === 0) {
          mapping[i] = 'arr_reg'
        } else {
          mapping[i] = 'dep_reg'
        }
        occurrences['REG'] = count + 1
      } else if (cleanHeader === 'STA') {
        mapping[i] = 'arr_time'
      } else if (cleanHeader === 'STAFF' || header.includes('STAFF')) {
        const count = occurrences['STAFF'] || 0
        if (header.includes('ARR') || count === 0) {
          mapping[i] = 'arr_staff'
        } else {
          mapping[i] = 'dep_staff'
        }
        occurrences['STAFF'] = count + 1
      } else if (cleanHeader === 'TO') {
        mapping[i] = 'dep_to'
      } else if (cleanHeader === 'STD') {
        mapping[i] = 'dep_time'
      } else if (cleanHeader === 'REMARK' || cleanHeader === 'GHI CHÚ' || cleanHeader === 'NOTE') {
        mapping[i] = 'remark'
      }
    }

    console.log('🔍 New format mapping result:', mapping)
    return mapping
  } catch (error) {
    console.error('❌ Error in mapHeadersNewFormat:', error)
    return mapping
  }
}

/**
 * Map headers to column names for legacy format
 */
const mapHeadersLegacyFormat = (headers: string[]): Record<number, string> => {
  const mapping: Record<number, string> = {}

  if (!Array.isArray(headers)) {
    console.error('❌ Invalid headers for legacy format mapping:', headers)
    return mapping
  }

  try {
    console.log('🔍 Mapping legacy format headers:', headers)

    for (let i = 0; i < headers.length; i++) {
      const header = (headers[i] || '').toString().trim()
      const mappedColumn = LEGACY_COLUMN_MAPPING[header]

      if (mappedColumn) {
        mapping[i] = mappedColumn
        console.log(`🔍 Mapped "${header}" -> "${mappedColumn}" at index ${i}`)
      }
    }

    console.log('🔍 Legacy format mapping result:', mapping)
    return mapping
  } catch (error) {
    console.error('❌ Error in mapHeadersLegacyFormat:', error)
    return mapping
  }
}

/**
 * Map headers to column names (auto-detect format)
 */
const mapHeaders = (headers: string[]): { mapping: Record<number, string>; format: 'new' | 'legacy'; hasRemark: boolean } => {
  if (!Array.isArray(headers)) {
    console.error('❌ Invalid headers for mapHeaders:', headers)
    return { mapping: {}, format: 'legacy', hasRemark: false }
  }

  try {
    const format = detectExcelFormat(headers)
    let mapping: Record<number, string>

    if (format === 'new') {
      mapping = mapHeadersNewFormat(headers)
    } else {
      mapping = mapHeadersLegacyFormat(headers)
    }

    // Check if REMARK column exists
    const hasRemark = Object.values(mapping).includes('remark')

    console.log(`🔍 Final mapping - Format: ${format}, Has REMARK: ${hasRemark}`)
    return { mapping, format, hasRemark }
  } catch (error) {
    console.error('❌ Error in mapHeaders:', error)
    return { mapping: {}, format: 'legacy', hasRemark: false }
  }
}

/**
 * Parse Excel file and extract flight data
 */
export const parseExcelFile = async (
  file: File,
  options: ExcelParseOptions = {}
): Promise<ExcelParseResult> => {
  const startTime = Date.now()

  try {
    console.log('🚀 Starting Excel parsing for file:', file.name)

    // Read file as array buffer
    options.onProgress?.(10, 0, 100)
    const arrayBuffer = await file.arrayBuffer()

    // Parse workbook
    options.onProgress?.(20, 0, 100)
    const workbook = XLSX.read(arrayBuffer, {
      type: 'array',
      cellDates: false,
      cellNF: false,
      cellText: false,
      sheetStubs: false
    })

    // Get sheet name
    const sheetName = options.sheetName || workbook.SheetNames[0]
    if (!workbook.Sheets[sheetName]) {
      throw new Error(`Sheet "${sheetName}" không tồn tại trong file Excel`)
    }

    const worksheet = workbook.Sheets[sheetName]

    // Convert sheet to JSON
    options.onProgress?.(30, 0, 100)
    const rawData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      defval: '',
      blankrows: false,
      raw: true
    }) as any[][]

    if (!Array.isArray(rawData) || rawData.length === 0) {
      throw new Error('File Excel không có dữ liệu')
    }

    // Extract headers and data rows
    const skipRows = options.skipRows || 0

    if (rawData.length <= skipRows) {
      throw new Error('File Excel không có đủ dữ liệu hoặc skipRows quá lớn')
    }

    const headerRow = rawData[skipRows]
    if (!Array.isArray(headerRow) || headerRow.length === 0) {
      throw new Error('Không tìm thấy header row hợp lệ trong file Excel')
    }

    // Extract data rows safely
    const dataRows = rawData.slice(skipRows + 1)
    if (!Array.isArray(dataRows)) {
      throw new Error('Lỗi khi trích xuất dữ liệu từ file Excel')
    }

    // Map headers to expected column names
    const { mapping: columnMapping, format, hasRemark } = mapHeaders(headerRow)

    // Process data rows
    options.onProgress?.(40, 0, 100)
    const processedData: any[] = []
    const errors: ExcelParseResult['errors'] = []
    const warnings: ExcelParseResult['warnings'] = []

    let processedRows = 0
    const maxRows = options.maxRows || dataRows.length
    const rowsToProcess = dataRows.slice(0, maxRows)

    console.log(`🔍 Processing ${rowsToProcess.length} rows (total data rows: ${dataRows.length}, maxRows: ${maxRows})`)

    // Process each row
    for (let i = 0; i < rowsToProcess.length; i++) {
      const row = rowsToProcess[i]
      const rowNumber = skipRows + i + 2 // +2 for header and 1-based indexing

      // Skip empty rows
      if (isEmptyRow(row)) {
        warnings.push({
          row: rowNumber,
          message: 'Dòng trống được bỏ qua'
        })
        continue
      }

      try {
        const mappedRow = mapRowData(row, columnMapping)
        if (mappedRow) {
          processedData.push(mappedRow)
          processedRows++
        }
      } catch (error) {
        console.error('❌ Error mapping row data:', error)
        errors.push({
          row: rowNumber,
          message: error instanceof Error ? error.message : 'Lỗi không xác định',
          value: row
        })
      }

      // Report progress
      if (i % 50 === 0 || i === rowsToProcess.length - 1) {
        const progress = 40 + ((i / rowsToProcess.length) * 40)
        options.onProgress?.(progress, i + 1, rowsToProcess.length)
      }
    }

    // Validate data if requested
    options.onProgress?.(80, 0, 100)
    let validatedData: ExcelFlightRow[] = []

    if (options.validateData !== false && processedData.length > 0) {
      console.log(`🔍 Validating ${processedData.length} processed rows...`)

      try {
        const validationResult = validateExcelBatch(processedData, new Date().toISOString().split('T')[0])

        if (validationResult && Array.isArray(validationResult.results)) {
          let successCount = 0
          let errorCount = 0

          validationResult.results.forEach(result => {
            if (result && result.success && result.data) {
              validatedData.push(result.data)
              successCount++
            } else if (result && result.errors && Array.isArray(result.errors)) {
              errorCount++
              result.errors.forEach(error => {
                if (error) {
                  errors.push({
                    row: (result.row || 0) + skipRows + 1,
                    field: error.field,
                    message: error.message,
                    value: error.value
                  })
                }
              })
            }
          })

          console.log(`✅ Validation completed: ${successCount} success, ${errorCount} errors`)
          if (validationResult.summary) {
            console.log('📊 Validation summary:', validationResult.summary)
          }
        } else {
          console.error('❌ validationResult.results is not an array:', validationResult?.results)
          validatedData = processedData
        }
      } catch (validationError) {
        console.error('❌ Error during validation:', validationError)
        validatedData = processedData
      }
    } else {
      validatedData = processedData
    }

    const processingTime = Date.now() - startTime
    options.onProgress?.(100, validatedData.length, validatedData.length)

    console.log(`✅ Excel parsing completed: ${validatedData.length} valid rows, ${errors.length} errors`)
    console.log(`📊 Processing summary: ${processedData.length} processed, ${validatedData.length} validated, ${warnings.length} warnings`)

    return {
      success: errors.length === 0,
      data: validatedData,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
      metadata: {
        filename: file.name,
        sheetName,
        totalRows: rawData.length - skipRows - 1,
        processedRows,
        skippedRows: rawData.length - skipRows - 1 - processedRows,
        columns: headerRow,
        processingTimeMs: processingTime,
        hasRemarkColumn: hasRemark,
        detectedFormat: format
      }
    }

  } catch (error) {
    const processingTime = Date.now() - startTime
    console.error('❌ Excel parsing failed:', error)

    return {
      success: false,
      errors: [{
        row: 0,
        message: error instanceof Error ? error.message : 'Lỗi không xác định khi đọc file Excel'
      }],
      metadata: {
        filename: file.name,
        sheetName: options.sheetName || 'Unknown',
        totalRows: 0,
        processedRows: 0,
        skippedRows: 0,
        columns: [],
        processingTimeMs: processingTime,
        hasRemarkColumn: false,
        detectedFormat: 'legacy'
      }
    }
  }
}

/**
 * Clean and format time value from Excel
 */
const cleanTimeValue = (value: any): string | undefined => {
  if (!value) return undefined

  // Handle string values
  if (typeof value === 'string') {
    const trimmed = value.trim()
    if (!trimmed) return undefined

    // Check if already in HH:MM or HH:MM+ format (overnight flights)
    if (/^([01]?[0-9]|2[0-3]):[0-5][0-9](\+)?$/.test(trimmed)) {
      return trimmed
    }

    // Handle various time formats
    // Format: "1/0/00" -> should be "01:00"
    const slashMatch = trimmed.match(/^(\d{1,2})\/(\d{1,2})\/(\d{1,2})$/)
    if (slashMatch) {
      const hours = parseInt(slashMatch[1], 10)
      const minutes = parseInt(slashMatch[2], 10)
      if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
      }
    }

    // Handle decimal time (Excel serial time)
    const num = parseFloat(trimmed)
    if (!isNaN(num) && num >= 0 && num < 1) {
      const totalMinutes = Math.round(num * 24 * 60)
      const hours = Math.floor(totalMinutes / 60)
      const minutes = totalMinutes % 60
      if (hours <= 23) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
      }
    }

    return trimmed // Return as-is if can't parse
  }

  // Handle number values (Excel serial time)
  if (typeof value === 'number') {
    if (value >= 0 && value < 1) {
      const totalMinutes = Math.round(value * 24 * 60)
      const hours = Math.floor(totalMinutes / 60)
      const minutes = totalMinutes % 60
      if (hours <= 23) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
      }
    }
    return value.toString()
  }

  // Handle Date objects
  if (value instanceof Date) {
    if (isNaN(value.getTime())) {
      return undefined
    }

    const hours = value.getHours()
    const minutes = value.getMinutes()

    if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    }
  }

  return undefined
}

/**
 * Map row data using column mapping
 */
const mapRowData = (row: any[], columnMapping: Record<number, string>): any => {
  const mappedData: any = {}

  if (!Array.isArray(row)) {
    console.error('❌ Invalid row data for mapping:', row)
    return mappedData
  }

  if (!columnMapping || typeof columnMapping !== 'object') {
    console.error('❌ Invalid column mapping:', columnMapping)
    return mappedData
  }

  try {
    for (let index = 0; index < row.length; index++) {
      const value = row[index]
      const columnName = columnMapping[index]

      if (columnName) {
        let processedValue = value

        // Special handling for time fields
        if (columnName === 'arr_time' || columnName === 'dep_time') {
          processedValue = cleanTimeValue(value)
        } else if (typeof value === 'string') {
          processedValue = value.trim() || undefined
        } else if (typeof value === 'number') {
          processedValue = value
        } else if (value instanceof Date) {
          // For non-time fields, convert Date to string
          processedValue = value.toISOString().split('T')[0] // YYYY-MM-DD format
        }

        mappedData[columnName] = processedValue
      }
    }
  } catch (error) {
    console.error('❌ Error in mapRowData:', error)
    throw new Error('Lỗi khi ánh xạ dữ liệu dòng: ' + (error instanceof Error ? error.message : 'Unknown error'))
  }

  return mappedData
}

/**
 * Check if a row is empty
 */
const isEmptyRow = (row: any[]): boolean => {
  if (!Array.isArray(row)) {
    console.warn('⚠️ Invalid row for empty check:', row)
    return true
  }

  try {
    return row.every(cell =>
      cell === null ||
      cell === undefined ||
      (typeof cell === 'string' && cell.trim() === '') ||
      (typeof cell === 'number' && isNaN(cell))
    )
  } catch (error) {
    console.error('❌ Error in isEmptyRow:', error)
    return true
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get available sheet names from Excel file
 */
export const getExcelSheetNames = async (file: File): Promise<string[]> => {
  try {
    const arrayBuffer = await file.arrayBuffer()
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })
    return workbook.SheetNames
  } catch (error) {
    throw new Error('Không thể đọc danh sách sheet từ file Excel')
  }
}

/**
 * Validate Excel file format
 */
export const validateExcelFile = (file: File): { valid: boolean; error?: string } => {
  // Check file size (max 10MB)
  if (file.size > 10 * 1024 * 1024) {
    return { valid: false, error: 'File không được vượt quá 10MB' }
  }

  // Check file type
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'text/csv' // .csv
  ]

  if (!validTypes.includes(file.type)) {
    return { valid: false, error: 'File phải có định dạng Excel (.xlsx, .xls) hoặc CSV (.csv)' }
  }

  return { valid: true }
}

/**
 * Generate sample Excel template with new format
 */
export const generateExcelTemplate = (): Blob => {
  // New format: STT | FLT | FROM | REG | STA | STAFF | FLT | TO | REG | STD | STAFF | REMARK
  const sampleData = [
    // Header row
    ['STT', 'FLT', 'FROM', 'REG', 'STA', 'STAFF', 'FLT', 'TO', 'REG', 'STD', 'STAFF', 'REMARK'],

    // Sample data rows
    [1, 'VN123', 'HAN', 'VN-A123', '08:30', 'Nguyễn Văn A', 'VN124', 'SGN', 'VN-A123', '10:00', 'Trần Thị B', 'Đúng giờ'],
    [2, 'QH456', 'DAD', 'VN-B456', '14:15', 'Lê Văn C', '', '', '', '', '', 'Chỉ có chuyến đến'],
    [3, '', '', '', '', '', '', 'VJ789', 'PQC', 'VN-C789', '16:45', 'Phạm Thị D', 'Chỉ có chuyến đi'],
    [4, 'BB123', 'SGN', 'VN-D123', '09:00', 'Hoàng Văn E', 'BB124', 'HAN', 'VN-D123', '11:30', 'Hoàng Văn E', ''],

    // Empty row for user input
    [5, '', '', '', '', '', '', '', '', '', '', '']
  ]

  const worksheet = XLSX.utils.aoa_to_sheet(sampleData)

  // Set column widths for better readability
  const columnWidths = [
    { wch: 5 },   // STT
    { wch: 8 },   // FLT (Arrival)
    { wch: 6 },   // FROM
    { wch: 10 },  // REG (Arrival)
    { wch: 6 },   // STA
    { wch: 15 },  // STAFF (Arrival)
    { wch: 8 },   // FLT (Departure)
    { wch: 6 },   // TO
    { wch: 10 },  // REG (Departure)
    { wch: 6 },   // STD
    { wch: 15 },  // STAFF (Departure)
    { wch: 20 }   // REMARK
  ]
  worksheet['!cols'] = columnWidths

  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Lịch bay')

  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
  return new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
}
