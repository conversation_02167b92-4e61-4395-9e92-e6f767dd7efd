import { z } from 'zod/v4'

// ============================================================================
// CORE FLIGHT SCHEMAS
// ============================================================================

// Base flight time schema with validation (supports overnight flights with + suffix)
const flightTimeSchema = z.string()
  .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9](\+)?$/, {
    error: 'Giờ phải có định dạng HH:MM hoặc HH:MM+ cho chuyến bay qua đêm (ví dụ: 08:30, 02:30+)'
  })
  .optional()

// Flight number schema with validation
const flightNumberSchema = z.string()
  .regex(/^[A-Z]{2}[0-9]{1,4}[A-Z]?$/, {
    error: 'Số hiệu bay phải có định dạng như VN123, QH456, VJ789A'
  })
  .optional()

// Airport code schema (IATA 3-letter codes)
const airportCodeSchema = z.string()
  .regex(/^[A-Z]{3}$/, {
    error: 'Mã sân bay phải là 3 chữ cái viết hoa (ví dụ: HAN, SGN, DAD)'
  })
  .optional()

// Aircraft registration schema
const aircraftRegSchema = z.string()
  .regex(/^[A-Z]{2}-[A-Z0-9]{3,6}$/, {
    error: 'Số đăng ký máy bay phải có định dạng VN-A123 hoặc VN-ABC123'
  })
  .optional()

// Staff name schema
const staffNameSchema = z.string()
  .min(2, 'Tên nhân viên phải có ít nhất 2 ký tự')
  .max(100, 'Tên nhân viên không được quá 100 ký tự')
  .regex(/^[a-zA-ZÀ-ỹ\s\-\.]+$/, {
    error: 'Tên nhân viên chỉ được chứa chữ cái, khoảng trắng, dấu gạch ngang và dấu chấm'
  })
  .optional()

// ============================================================================
// COMPREHENSIVE FLIGHT SCHEMA
// ============================================================================

export const flightSchema = z.object({
  id: z.string().min(1, 'ID không được để trống').optional(),
  date: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Ngày phải có định dạng YYYY-MM-DD')
    .refine((date) => {
      const parsed = new Date(date)
      return !isNaN(parsed.getTime()) && parsed >= new Date('2020-01-01')
    }, 'Ngày phải hợp lệ và không được trước năm 2020'),
  
  stt: z.number()
    .int('STT phải là số nguyên')
    .positive('STT phải là số dương')
    .max(999, 'STT không được vượt quá 999'),
  
  // Arrival fields
  arr_flt: flightNumberSchema,
  arr_from: airportCodeSchema,
  arr_reg: aircraftRegSchema,
  arr_time: flightTimeSchema,
  arr_staff: staffNameSchema,
  
  // Departure fields
  dep_flt: flightNumberSchema,
  dep_to: airportCodeSchema,
  dep_reg: aircraftRegSchema,
  dep_time: flightTimeSchema,
  dep_staff: staffNameSchema,

  // Optional remark field
  remark: z.string()
    .max(500, 'Ghi chú không được quá 500 ký tự')
    .optional(),

  // Metadata (optional for input, required for output)
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  created_by: z.string().optional(),
  updated_by: z.string().optional(),

  // Status fields for arrival
  arr_present: z.boolean().default(false).optional(),
  arr_finished: z.boolean().default(false).optional(),

  // Status fields for departure
  dep_present: z.boolean().default(false).optional(),
  dep_boarded: z.boolean().default(false).optional(),
  dep_finished: z.boolean().default(false).optional()
}).refine((data) => {
  // Business rule: Must have at least arrival OR departure info
  const hasArrival = data.arr_flt || data.arr_from || data.arr_time
  const hasDeparture = data.dep_flt || data.dep_to || data.dep_time
  return hasArrival || hasDeparture
}, {
  message: 'Phải có ít nhất thông tin chuyến bay đến HOẶC chuyến bay đi',
  path: ['arr_flt'] // Show error on arrival flight field
})

// Create and Update schemas
export const createFlightSchema = flightSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
  created_by: true,
  updated_by: true,
  // Status fields are managed by the system, not user input
  arr_present: true,
  arr_finished: true,
  dep_present: true,
  dep_boarded: true,
  dep_finished: true
})

export const updateFlightSchema = createFlightSchema.partial()

// ============================================================================
// EXCEL IMPORT SCHEMAS
// ============================================================================

// Excel file validation schema
export const excelFileSchema = z.file()
  .min(1, 'File không được để trống')
  .max(10_000_000, 'File không được vượt quá 10MB') // 10MB limit
  .refine((file) => {
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ]
    return validTypes.includes(file.type)
  }, {
    error: 'File phải có định dạng Excel (.xlsx, .xls) hoặc CSV (.csv)'
  })

// Excel row schema (for individual rows from Excel)
export const excelFlightRowSchema = z.object({
  stt: z.union([z.number(), z.string(), z.undefined(), z.null()]).transform((val) => {
    if (val === undefined || val === null || val === '') {
      throw new Error('STT không được để trống')
    }
    const num = typeof val === 'string' ? parseInt(val.toString().trim()) : Number(val)
    if (isNaN(num) || num <= 0) {
      throw new Error('STT phải là số dương')
    }
    return num
  }),

  // Arrival fields (can be empty strings, numbers, or undefined from Excel)
  arr_flt: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),
  arr_from: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),
  arr_reg: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),
  arr_time: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),
  arr_staff: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),

  // Departure fields (can be empty strings, numbers, or undefined from Excel)
  dep_flt: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),
  dep_to: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),
  dep_reg: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),
  dep_time: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),
  dep_staff: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional(),

  // Optional remark field (can be empty string, number, or undefined from Excel)
  remark: z.union([z.string(), z.number(), z.undefined(), z.null()]).transform(val => {
    if (val === undefined || val === null) return undefined
    const str = val.toString().trim()
    return str || undefined
  }).optional()
}).transform((data) => {
  // Transform to match flight schema format
  return {
    stt: data.stt,
    arr_flt: data.arr_flt,
    arr_from: data.arr_from,
    arr_reg: data.arr_reg,
    arr_time: data.arr_time,
    arr_staff: data.arr_staff,
    dep_flt: data.dep_flt,
    dep_to: data.dep_to,
    dep_reg: data.dep_reg,
    dep_time: data.dep_time,
    dep_staff: data.dep_staff,
    remark: data.remark
  }
})

// Excel import request schema
export const excelImportSchema = z.object({
  file: excelFileSchema,
  date: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Ngày phải có định dạng YYYY-MM-DD'),
  mode: z.enum(['replace', 'append', 'update'], {
    error: 'Chế độ import phải là replace, append hoặc update'
  }).default('replace'),
  sheetName: z.string().optional(),
  skipRows: z.number().int().min(0).max(10).default(0),
  validateOnly: z.boolean().default(false)
})

// Excel import result schema
export const excelImportResultSchema = z.object({
  success: z.boolean(),
  importId: z.string().uuid(),
  filename: z.string(),
  totalRows: z.number().int().min(0),
  processedRows: z.number().int().min(0),
  successRows: z.number().int().min(0),
  errorRows: z.number().int().min(0),
  skippedRows: z.number().int().min(0),
  errors: z.array(z.object({
    row: z.number().int(),
    field: z.string().optional(),
    message: z.string(),
    value: z.any().optional()
  })),
  warnings: z.array(z.object({
    row: z.number().int(),
    message: z.string(),
    value: z.any().optional()
  })),
  summary: z.string(),
  processingTimeMs: z.number().int().min(0)
})

// ============================================================================
// SEARCH & FILTER SCHEMAS
// ============================================================================

export const flightSearchSchema = z.object({
  // Pagination
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(2000).default(2000), // Increase max limit to handle large datasets
  
  // Date filtering
  date: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Ngày phải có định dạng YYYY-MM-DD')
    .optional(),
  dateFrom: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Ngày bắt đầu phải có định dạng YYYY-MM-DD')
    .optional(),
  dateTo: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Ngày kết thúc phải có định dạng YYYY-MM-DD')
    .optional(),
  
  // Search text
  search: z.string().max(100, 'Từ khóa tìm kiếm không được quá 100 ký tự').optional(),
  
  // Type filtering
  type: z.enum(['arrival', 'departure', 'both']).default('both'),
  
  // Staff filtering
  staff: z.string().max(100, 'Tên nhân viên không được quá 100 ký tự').optional(),
  
  // Airport filtering
  airport: z.string()
    .regex(/^[A-Z]{3}$/, 'Mã sân bay phải là 3 chữ cái viết hoa')
    .optional(),
  
  // Sorting
  sortBy: z.enum(['date', 'stt', 'arr_time', 'dep_time', 'created_at']).default('date'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
}).refine((data) => {
  // Validate date range
  if (data.dateFrom && data.dateTo) {
    return new Date(data.dateFrom) <= new Date(data.dateTo)
  }
  return true
}, {
  message: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc',
  path: ['dateTo']
})

// ============================================================================
// EXPORT SCHEMAS
// ============================================================================

export const flightExportSchema = z.object({
  format: z.enum(['excel', 'csv', 'pdf'], {
    error: 'Định dạng export phải là excel, csv hoặc pdf'
  }),
  
  // Date range for export
  dateFrom: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  dateTo: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  
  // Filters
  type: z.enum(['arrival', 'departure', 'both']).default('both'),
  staff: z.string().optional(),
  airport: z.string()
    .regex(/^[A-Z]{3}$/, 'Mã sân bay phải là 3 chữ cái viết hoa')
    .optional(),
  
  // Export options
  includeMetadata: z.boolean().default(true),
  filename: z.string().max(100, 'Tên file không được quá 100 ký tự').optional()
}).refine((data) => {
  return new Date(data.dateFrom) <= new Date(data.dateTo)
}, {
  message: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc',
  path: ['dateTo']
})

// ============================================================================
// CHANGE TRACKING SCHEMAS
// ============================================================================

export const flightChangeSchema = z.object({
  id: z.string().uuid().optional(),
  flight_id: z.string().uuid('Flight ID phải là UUID hợp lệ'),
  field_name: z.enum([
    'date', 'stt',
    'arr_flt', 'arr_from', 'arr_reg', 'arr_time', 'arr_staff',
    'dep_flt', 'dep_to', 'dep_reg', 'dep_time', 'dep_staff',
    'remark',
    'arr_present', 'arr_finished', 'dep_present', 'dep_boarded', 'dep_finished'
  ]),
  old_value: z.string().nullable(),
  new_value: z.string().nullable(),
  change_type: z.enum(['create', 'update', 'delete']).default('update'),
  changed_by: z.string().min(1, 'Người thay đổi không được để trống'),
  changed_at: z.string().optional(),
  reason: z.string().max(500, 'Lý do thay đổi không được quá 500 ký tự').optional(),
  batch_id: z.string().uuid().optional()
})

// ============================================================================
// DASHBOARD & STATISTICS SCHEMAS
// ============================================================================

export const flightStatsSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  totalFlights: z.number().int().min(0),
  arrivalCount: z.number().int().min(0),
  departureCount: z.number().int().min(0),
  uniqueStaff: z.number().int().min(0),
  earliestTime: z.string().optional(),
  latestTime: z.string().optional(),
  busyHours: z.array(z.object({
    hour: z.number().int().min(0).max(23),
    count: z.number().int().min(0)
  }))
})

// Pagination response schema
export const paginationSchema = z.object({
  page: z.number().int().min(1),
  limit: z.number().int().min(1),
  total: z.number().int().min(0),
  totalPages: z.number().int().min(0)
})

// API Response schemas
export const flightApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
  total: z.number().int().min(0).optional(),
  pagination: paginationSchema.optional()
})

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

// Helper function to validate flight data with custom error messages
export const validateFlightData = (data: unknown, isUpdate = false) => {
  const schema = isUpdate ? updateFlightSchema : createFlightSchema
  const result = schema.safeParse(data)

  if (!result.success) {
    const errors = result.error.errors.map(err => ({
      field: Array.isArray(err.path) ? err.path.join('.') : 'unknown',
      message: err.message,
      code: err.code
    }))
    return { success: false, errors }
  }

  return { success: true, data: result.data }
}

// Helper function to validate Excel import data
export const validateExcelImportData = (data: unknown) => {
  const result = excelImportSchema.safeParse(data)

  if (!result.success) {
    const errors = result.error.errors.map(err => ({
      field: Array.isArray(err.path) ? err.path.join('.') : 'unknown',
      message: err.message,
      code: err.code
    }))
    return { success: false, errors }
  }

  return { success: true, data: result.data }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type Flight = z.infer<typeof flightSchema>
export type CreateFlightData = z.infer<typeof createFlightSchema>
export type UpdateFlightData = z.infer<typeof updateFlightSchema>
export type ExcelFlightRow = z.infer<typeof excelFlightRowSchema>
export type ExcelImportData = z.infer<typeof excelImportSchema>
export type ExcelImportResult = z.infer<typeof excelImportResultSchema>
export type FlightSearchParams = z.infer<typeof flightSearchSchema>
export type FlightExportParams = z.infer<typeof flightExportSchema>
export type FlightChange = z.infer<typeof flightChangeSchema>
export type FlightStats = z.infer<typeof flightStatsSchema>
export type PaginationData = z.infer<typeof paginationSchema>

// ============================================================================
// PREVIEW CHANGES SCHEMAS (for Update mode import)
// ============================================================================

// Schema for individual field change in preview
export const previewFieldChangeSchema = z.object({
  field: z.string(),
  fieldDisplayName: z.string(),
  oldValue: z.string().nullable(),
  newValue: z.string().nullable(),
  hasChanged: z.boolean()
})

// Schema for preview change item
export const previewChangeItemSchema = z.object({
  type: z.enum(['update', 'insert']),
  stt: z.number(),
  flightId: z.string().optional(), // Only for updates
  oldData: flightSchema.optional(), // Only for updates
  newData: createFlightSchema,
  fieldChanges: z.array(previewFieldChangeSchema),
  hasChanges: z.boolean() // True if any field has changed
})

// Schema for preview changes response
export const previewChangesResponseSchema = z.object({
  success: z.boolean(),
  changes: z.array(previewChangeItemSchema),
  summary: z.object({
    totalItems: z.number(),
    updateCount: z.number(),
    insertCount: z.number(),
    changedFieldsCount: z.number()
  }),
  date: z.string(),
  filename: z.string()
})

// ============================================================================
// CELL COLORS SCHEMAS
// ============================================================================

export const cellColorSchema = z.object({
  id: z.string().optional(),
  flight_id: z.string().min(1, 'Flight ID không được để trống'),
  field_name: z.string().min(1, 'Field name không được để trống'),
  color_value: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Color phải là hex code hợp lệ (ví dụ: #ff0000)'),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  created_by: z.string().optional()
})

export const updateCellColorSchema = cellColorSchema.pick({
  color_value: true
})

export const batchCellColorsSchema = z.object({
  colors: z.array(cellColorSchema.pick({
    flight_id: true,
    field_name: true,
    color_value: true
  }))
})

// ============================================================================
// ROW OPERATIONS SCHEMAS
// ============================================================================

export const insertRowSchema = z.object({
  type: z.enum(['arrival', 'departure'], {
    errorMap: () => ({ message: 'Type phải là arrival hoặc departure' })
  }),
  position: z.enum(['before', 'after'], {
    errorMap: () => ({ message: 'Position phải là before hoặc after' })
  }),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date phải có định dạng YYYY-MM-DD')
})

export const deleteRowSchema = z.object({
  type: z.enum(['arrival', 'departure'], {
    errorMap: () => ({ message: 'Type phải là arrival hoặc departure' })
  }),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date phải có định dạng YYYY-MM-DD')
})

// Export types
export type PreviewFieldChange = z.infer<typeof previewFieldChangeSchema>
export type PreviewChangeItem = z.infer<typeof previewChangeItemSchema>
export type PreviewChangesResponse = z.infer<typeof previewChangesResponseSchema>
export type FlightApiResponse = z.infer<typeof flightApiResponseSchema>
export type CellColor = z.infer<typeof cellColorSchema>
export type UpdateCellColor = z.infer<typeof updateCellColorSchema>
export type BatchCellColors = z.infer<typeof batchCellColorsSchema>
export type InsertRowData = z.infer<typeof insertRowSchema>
export type DeleteRowData = z.infer<typeof deleteRowSchema>
