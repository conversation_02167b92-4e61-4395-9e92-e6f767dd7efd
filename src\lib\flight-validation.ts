import { z } from 'zod/v4'

// Validation schemas cho inline editing với Zod v4
export const flightNumberSchema = z.string()
  .min(1, { error: 'Số hiệu bay không được để trống' })
  .max(10, { error: 'Số hiệu bay không được quá 10 ký tự' })
  .regex(/^[A-Z0-9]+$/, { error: 'Số hiệu bay chỉ chứa chữ cái và số' })

export const airportCodeSchema = z.string()
  .min(3, { error: 'Mã sân bay phải có ít nhất 3 ký tự' })
  .max(4, { error: 'Mã sân bay không được quá 4 ký tự' })
  .regex(/^[A-Z]+$/, { error: 'Mã sân bay chỉ chứa chữ cái viết hoa' })

export const aircraftRegSchema = z.string()
  .min(1, { error: '<PERSON><PERSON> đăng ký máy bay không được để trống' })
  .max(10, { error: '<PERSON><PERSON> đăng ký máy bay không được quá 10 ký tự' })

export const timeSchema = z.string()
  .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](\+)?$/, {
    error: 'Thời gian phải có định dạng HH:MM hoặc HH:MM+ (cho chuyến bay qua đêm)'
  })

export const staffNameSchema = z.string()
  .min(1, { error: 'Tên nhân viên không được để trống' })
  .max(100, { error: 'Tên nhân viên không được quá 100 ký tự' })
  .regex(/^[a-zA-ZÀ-ỹĐđ0-9\s\/\*\-\.]+$/, {
    error: 'Tên nhân viên chỉ chứa chữ cái, số, dấu cách, dấu /, *, - và .'
  })

export const remarkSchema = z.string()
  .max(500, { error: 'Ghi chú không được quá 500 ký tự' })
  .optional()

export const sttSchema = z.string()
  .transform((val) => parseInt(val, 10))
  .refine((val) => !isNaN(val), { error: 'STT phải là số' })
  .refine((val) => val >= 1, { error: 'STT phải lớn hơn 0' })
  .refine((val) => val <= 999, { error: 'STT không được quá 999' })

// Schema cho từng field có thể edit (chỉ REG, STAFF, TIME, REMARK)
export const editableFieldSchemas = {
  arr_reg: z.optional(aircraftRegSchema),
  arr_time: z.optional(timeSchema),
  arr_staff: z.optional(staffNameSchema),
  dep_reg: z.optional(aircraftRegSchema),
  dep_time: z.optional(timeSchema),
  dep_staff: z.optional(staffNameSchema),
  remark: remarkSchema,
  stt: sttSchema
} as const

// Schema cho update flight (chỉ cho phép edit REG, STAFF, TIME, REMARK)
export const updateFlightFieldSchema = z.object({
  field: z.enum([
    'arr_reg', 'arr_time', 'arr_staff',
    'dep_reg', 'dep_time', 'dep_staff',
    'remark', 'stt'
  ]),
  value: z.string().optional(),
  flightId: z.string().min(1, { error: 'ID chuyến bay không được để trống' })
})

// Schema cho flight change tracking
export const flightChangeSchema = z.object({
  id: z.string().min(1),
  flight_id: z.string().min(1),
  field_name: z.string(),
  old_value: z.string().nullable(),
  new_value: z.string().nullable(),
  changed_by: z.string(),
  changed_at: z.string(),
  change_reason: z.string().optional()
})

// Schema cho batch update
export const batchUpdateSchema = z.object({
  updates: z.array(z.object({
    flightId: z.string().min(1),
    field: z.string(),
    value: z.string().optional()
  })).min(1, { error: 'Phải có ít nhất 1 cập nhật' })
})

// Validation functions
export const validateFlightField = (field: keyof typeof editableFieldSchemas, value: string) => {
  const schema = editableFieldSchemas[field]
  if (!schema) {
    throw new Error(`Không tìm thấy schema cho field: ${field}`)
  }

  try {
    // Handle empty string and null values - for optional fields, empty string should be valid
    const processedValue = value === '' || value === null || value === undefined ? undefined : value.trim()

    // For staff fields, allow empty values
    if ((field === 'arr_staff' || field === 'dep_staff') && !processedValue) {
      return undefined
    }

    return schema.parse(processedValue)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors[0]?.message || 'Giá trị không hợp lệ'
      console.error(`Validation error for field ${field}:`, errorMessage, 'Value:', value)
      throw new Error(errorMessage)
    }
    console.error(`Unexpected validation error for field ${field}:`, error)
    throw error
  }
}

// Helper function để validate multiple fields
export const validateFlightFields = (updates: Record<string, string>) => {
  const errors: Record<string, string> = {}
  
  for (const [field, value] of Object.entries(updates)) {
    if (field in editableFieldSchemas) {
      try {
        validateFlightField(field as keyof typeof editableFieldSchemas, value)
      } catch (error) {
        errors[field] = error instanceof Error ? error.message : 'Giá trị không hợp lệ'
      }
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Business logic validation
export const validateFlightBusinessRules = (flightData: any) => {
  const errors: string[] = []

  // Time validation - REMOVED
  // Note: In aviation, departure time can be <= arrival time as they may be different flights
  if (flightData.arr_time && flightData.dep_time) {
    console.log('✅ [flight-validation.ts] Time validation passed:', {
      arr_time: flightData.arr_time,
      dep_time: flightData.dep_time,
      note: 'Time validation removed - allowing flexible scheduling'
    })
  }

  // Staff assignment validation - REMOVED
  // Note: Same staff can serve both arrival and departure flights
  // This validation has been removed to allow flexible staff assignment
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export type EditableField = keyof typeof editableFieldSchemas
export type FlightChangeData = z.infer<typeof flightChangeSchema>
export type UpdateFlightFieldData = z.infer<typeof updateFlightFieldSchema>
export type BatchUpdateData = z.infer<typeof batchUpdateSchema>
