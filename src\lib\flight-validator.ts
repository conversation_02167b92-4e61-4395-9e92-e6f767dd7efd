import { 
  flightSchema, 
  createFlightSchema, 
  updateFlightSchema,
  excelFlightRowSchema,
  excelImportSchema,
  flightSearchSchema,
  flightExportSchema,
  validateFlightData,
  validateExcelImportData,
  type Flight,
  type CreateFlightData,
  type UpdateFlightData,
  type ExcelFlightRow,
  type ExcelImportData
} from './flight-schemas'

// ============================================================================
// FLIGHT DATA VALIDATION UTILITIES
// ============================================================================

/**
 * Validate flight data for creation
 */
export const validateCreateFlight = (data: unknown) => {
  return validateFlightData(data, false)
}

/**
 * Validate flight data for update
 */
export const validateUpdateFlight = (data: unknown) => {
  return validateFlightData(data, true)
}

/**
 * Validate Excel import request
 */
export const validateExcelImport = (data: unknown) => {
  return validateExcelImportData(data)
}

/**
 * Validate individual Excel row data
 */
export const validateExcelRow = (data: unknown, rowNumber: number) => {
  try {
    const result = excelFlightRowSchema.safeParse(data)

    if (!result.success) {
      const errors = (result.error?.errors || []).map(err => ({
        row: rowNumber,
        field: Array.isArray(err.path) ? err.path.join('.') : 'unknown',
        message: err.message || 'Validation error',
        code: err.code || 'validation_error',
        value: err.input
      }))

      // Log first few schema validation failures for debugging
      if (rowNumber <= 5) {
        console.log(`❌ Schema validation failed for row ${rowNumber}:`, {
          data,
          dataType: typeof data,
          dataKeys: data && typeof data === 'object' ? Object.keys(data) : 'not object',
          errors: errors.map(e => ({ field: e.field, message: e.message, code: e.code, value: e.value })),
          zodError: result.error?.issues
        })
      }

      return { success: false, errors }
    }

    return { success: true, data: result.data }
  } catch (error) {
    console.error('❌ Error in validateExcelRow:', error)
    return {
      success: false,
      errors: [{
        row: rowNumber,
        field: 'general',
        message: error instanceof Error ? error.message : 'Unknown validation error',
        code: 'validation_error'
      }]
    }
  }
}

/**
 * Validate search parameters
 */
export const validateSearchParams = (data: unknown) => {
  try {
    const result = flightSearchSchema.safeParse(data)

    if (!result.success) {
      const errors = (result.error?.errors || []).map(err => ({
        field: Array.isArray(err.path) ? err.path.join('.') : 'unknown',
        message: err.message || 'Validation error',
        code: err.code || 'validation_error'
      }))
      return { success: false, errors }
    }

    return { success: true, data: result.data }
  } catch (error) {
    console.error('❌ Error in validateSearchParams:', error)
    return {
      success: false,
      errors: [{
        field: 'general',
        message: error instanceof Error ? error.message : 'Unknown validation error',
        code: 'validation_error'
      }]
    }
  }
}

/**
 * Validate export parameters
 */
export const validateExportParams = (data: unknown) => {
  try {
    const result = flightExportSchema.safeParse(data)

    if (!result.success) {
      const errors = (result.error?.errors || []).map(err => ({
        field: Array.isArray(err.path) ? err.path.join('.') : 'unknown',
        message: err.message || 'Validation error',
        code: err.code || 'validation_error'
      }))
      return { success: false, errors }
    }

    return { success: true, data: result.data }
  } catch (error) {
    console.error('❌ Error in validateExportParams:', error)
    return {
      success: false,
      errors: [{
        field: 'general',
        message: error instanceof Error ? error.message : 'Unknown validation error',
        code: 'validation_error'
      }]
    }
  }
}

// ============================================================================
// BUSINESS LOGIC VALIDATION
// ============================================================================

/**
 * Validate flight business rules
 */
export const validateFlightBusinessRules = (flight: CreateFlightData | UpdateFlightData) => {
  const errors: Array<{ field: string; message: string }> = []

  // Rule 1: Must have at least arrival OR departure info
  const hasArrival = flight.arr_flt || flight.arr_from || flight.arr_time
  const hasDeparture = flight.dep_flt || flight.dep_to || flight.dep_time

  if (!hasArrival && !hasDeparture) {
    errors.push({
      field: 'general',
      message: 'Phải có ít nhất thông tin chuyến bay đến HOẶC chuyến bay đi'
    })
  }

  // Rule 2: If flight number exists, other fields should be provided
  if (flight.arr_flt && !flight.arr_time) {
    errors.push({
      field: 'arr_time',
      message: 'Giờ đến là bắt buộc khi có số hiệu chuyến bay đến'
    })
  }

  if (flight.dep_flt && !flight.dep_time) {
    errors.push({
      field: 'dep_time',
      message: 'Giờ khởi hành là bắt buộc khi có số hiệu chuyến bay đi'
    })
  }

  // Rule 3: Time validation - REMOVED
  // Note: In aviation, departure time can be <= arrival time as they may be different flights
  // This validation has been removed to allow flexible scheduling

  // Rule 4: Staff assignment validation - REMOVED
  // Note: Same staff can serve both arrival and departure flights
  // This validation has been removed to allow flexible staff assignment

  // Log validation details for debugging (only for failed cases)
  if (errors.length > 0) {
    console.log('❌ Business rule validation failed:', {
      stt: flight.stt,
      hasArrival,
      hasDeparture,
      arr_flt: flight.arr_flt,
      arr_time: flight.arr_time,
      dep_flt: flight.dep_flt,
      dep_time: flight.dep_time,
      errors: errors.map(e => e.message)
    })
  }

  return {
    success: errors.length === 0,
    errors
  }
}

/**
 * Validate Excel data batch
 */
export const validateExcelBatch = (rows: unknown[], date: string) => {
  const results: Array<{
    row: number
    success: boolean
    data?: ExcelFlightRow
    errors?: Array<{ field: string; message: string; code: string; value?: any }>
  }> = []

  // Validate input parameters
  if (!Array.isArray(rows)) {
    console.error('❌ validateExcelBatch: rows is not an array:', rows)
    return {
      results: [],
      summary: {
        total: 0,
        success: 0,
        errors: 0,
        successRate: 0
      }
    }
  }

  if (!date || typeof date !== 'string') {
    console.error('❌ validateExcelBatch: invalid date:', date)
    return {
      results: [],
      summary: {
        total: 0,
        success: 0,
        errors: 0,
        successRate: 0
      }
    }
  }

  const sttSet = new Set<number>()

  try {
    let schemaErrors = 0
    let businessRuleErrors = 0
    let duplicateErrors = 0

    rows.forEach((row, index) => {
      try {
        const rowNumber = index + 1
        const validation = validateExcelRow(row, rowNumber)

        if (validation.success && validation.data) {
          // Check for duplicate STT
          if (sttSet.has(validation.data.stt)) {
            duplicateErrors++
            results.push({
              row: rowNumber,
              success: false,
              errors: [{
                field: 'stt',
                message: `STT ${validation.data.stt} bị trùng lặp trong file Excel`,
                code: 'duplicate_stt'
              }]
            })
          } else {
            sttSet.add(validation.data.stt)

            // Validate business rules
            const businessValidation = validateFlightBusinessRules({
              ...validation.data,
              date
            })

            if (businessValidation.success) {
              results.push({
                row: rowNumber,
                success: true,
                data: validation.data
              })
            } else {
              businessRuleErrors++
              results.push({
                row: rowNumber,
                success: false,
                errors: (businessValidation.errors || []).map(err => ({
                  field: err?.field || 'general',
                  message: err?.message || 'Business rule violation',
                  code: 'business_rule_violation'
                }))
              })
            }
          }
        } else {
          schemaErrors++
          results.push({
            row: rowNumber,
            success: false,
            errors: validation.errors || []
          })
        }
      } catch (rowError) {
        console.error(`❌ Error processing row ${index + 1}:`, rowError)
        results.push({
          row: index + 1,
          success: false,
          errors: [{
            field: 'general',
            message: `Lỗi xử lý dòng ${index + 1}: ${rowError instanceof Error ? rowError.message : 'Unknown error'}`,
            code: 'processing_error'
          }]
        })
      }
    })

    console.log(`📊 Validation breakdown: Schema errors: ${schemaErrors}, Business rule errors: ${businessRuleErrors}, Duplicate errors: ${duplicateErrors}`)
    console.log(`📊 Total rows processed: ${rows.length}, Results generated: ${results.length}`)

    const successCount = results.filter(r => r.success).length
    const errorCount = results.filter(r => !r.success).length

    console.log(`📊 Final validation results: ${successCount} success, ${errorCount} errors`)

    return {
      results,
      summary: {
        total: rows.length,
        success: successCount,
        errors: errorCount,
        successRate: rows.length > 0 ? (successCount / rows.length) * 100 : 0
      }
    }
  } catch (error) {
    console.error('❌ Error in validateExcelBatch:', error)
    return {
      results: [],
      summary: {
        total: 0,
        success: 0,
        errors: 0,
        successRate: 0
      }
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Sanitize flight data for database insertion
 */
export const sanitizeFlightData = (data: CreateFlightData | UpdateFlightData): CreateFlightData | UpdateFlightData => {
  const sanitized = { ...data }
  
  // Convert empty strings to undefined
  Object.keys(sanitized).forEach(key => {
    const value = (sanitized as any)[key]
    if (typeof value === 'string' && value.trim() === '') {
      (sanitized as any)[key] = undefined
    }
  })
  
  return sanitized
}

/**
 * Format validation errors for API response
 */
export const formatValidationErrors = (errors: Array<{ field: string; message: string; code?: string }>) => {
  try {
    if (!Array.isArray(errors)) {
      console.error('❌ formatValidationErrors: errors is not an array:', errors)
      return {
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: []
      }
    }

    return {
      success: false,
      error: 'Dữ liệu không hợp lệ',
      details: errors.map(err => ({
        field: err?.field || 'unknown',
        message: err?.message || 'Unknown error',
        code: err?.code || 'validation_error'
      }))
    }
  } catch (error) {
    console.error('❌ Error in formatValidationErrors:', error)
    return {
      success: false,
      error: 'Dữ liệu không hợp lệ',
      details: []
    }
  }
}

/**
 * Check if flight data is complete
 */
export const isFlightDataComplete = (flight: Flight | CreateFlightData | UpdateFlightData): boolean => {
  const hasCompleteArrival = !!(flight.arr_flt && flight.arr_from && flight.arr_time)
  const hasCompleteDeparture = !!(flight.dep_flt && flight.dep_to && flight.dep_time)
  
  return hasCompleteArrival || hasCompleteDeparture
}

// Export all schemas for direct use
export {
  flightSchema,
  createFlightSchema,
  updateFlightSchema,
  excelFlightRowSchema,
  excelImportSchema,
  flightSearchSchema,
  flightExportSchema
}
