import { z } from 'zod/v4'

// Import flight schemas
export * from './flight-schemas'

// Schema cho thêm thiết bị mới
export const addDeviceSchema = z.object({
  name: z.string()
    .min(1, 'Tên thiết bị không được để trống')
    .min(3, 'Tên thiết bị phải có ít nhất 3 ký tự')
    .max(50, 'Tên thiết bị không được quá 50 ký tự')
})

// Schema cho cho mượn thiết bị
export const borrowDeviceSchema = z.object({
  borrowerName: z.string()
    .min(1, 'Tên người mượn không được để trống')
    .min(2, 'Tên người mượn phải có ít nhất 2 ký tự')
    .max(100, 'Tên người mượn không được quá 100 ký tự')
    .regex(/^[a-zA-ZÀ-ỹ0-9\s]+$/, 'Tên người mượn chỉ được chứa chữ cái, s<PERSON> và khoảng trắng')
})

// Schema cho đăng nhập
export const loginSchema = z.object({
  email: z.string()
    .min(1, 'Email không được để trống')
    .email('Email không hợp lệ'),
  password: z.string()
    .min(1, 'Mật khẩu không được để trống')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự')
})

// Schema cho đổi mật khẩu
export const changePasswordSchema = z.object({
  currentPassword: z.string()
    .min(1, 'Mật khẩu hiện tại không được để trống'),
  newPassword: z.string()
    .min(6, 'Mật khẩu mới phải có ít nhất 6 ký tự'),
  confirmPassword: z.string()
    .min(1, 'Xác nhận mật khẩu không được để trống')
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'Mật khẩu xác nhận không khớp',
  path: ['confirmPassword']
})

// Legacy flight schemas (kept for backward compatibility)
// Note: Use flight-schemas.ts for new comprehensive schemas

// Types từ schemas
export type AddDeviceFormData = z.infer<typeof addDeviceSchema>
export type BorrowDeviceFormData = z.infer<typeof borrowDeviceSchema>
export type LoginFormData = z.infer<typeof loginSchema>
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>
// Flight types are now exported from flight-schemas.ts
