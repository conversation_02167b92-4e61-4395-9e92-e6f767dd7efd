/**
 * Utility functions for search functionality
 * Enhanced with multi-term search support using comma-separated terms
 */

// Escape special regex characters
export const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// Remove Vietnamese diacritics for better matching
export const removeDiacritics = (str: string): string => {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
}

// Parse multiple search terms from comma-separated string
export const parseSearchTerms = (searchInput: string): string[] => {
  if (!searchInput || !searchInput.trim()) return []

  return searchInput
    .split(',')
    .map(term => term.trim())
    .filter(term => term.length > 0)
}

// Enhanced search function for flight data with multi-term support
export const searchFlightData = (
  searchInput: string,
  flight: any
): boolean => {
  if (!searchInput.trim()) return true

  // Parse multiple search terms
  const searchTerms = parseSearchTerms(searchInput)
  if (searchTerms.length === 0) return true

  const searchFields = [
    flight.arr_flt,
    flight.dep_flt,
    flight.arr_staff,
    flight.dep_staff,
    flight.arr_from,
    flight.dep_to,
    flight.arr_reg,
    flight.dep_reg
  ]

  // Check if ANY search term matches ANY field (OR logic for terms)
  return searchTerms.some(term => {
    const normalizedTerm = removeDiacritics(term)

    return searchFields.some(field => {
      if (!field) return false
      const normalizedField = removeDiacritics(field)
      return normalizedField.includes(normalizedTerm)
    })
  })
}

// Enhanced highlight function with multi-term support
export const highlightText = (text: string, searchInput: string): string => {
  if (!searchInput || !text) return text

  // Parse multiple search terms
  const searchTerms = parseSearchTerms(searchInput)
  if (searchTerms.length === 0) return text

  // Collect all matches from all terms first
  const allMatches: Array<{ start: number; end: number; term: string }> = []

  searchTerms.forEach(term => {
    const trimmedTerm = term.trim()
    if (!trimmedTerm) return

    // First try exact case-insensitive match
    const escapedTerm = escapeRegExp(trimmedTerm)
    const exactRegex = new RegExp(escapedTerm, 'gi')
    let match

    while ((match = exactRegex.exec(text)) !== null) {
      allMatches.push({
        start: match.index,
        end: match.index + match[0].length,
        term: trimmedTerm
      })
      // Prevent infinite loop for zero-length matches
      if (match.index === exactRegex.lastIndex) {
        exactRegex.lastIndex++
      }
    }

    // If no exact matches found, try diacritic-insensitive matching
    if (allMatches.filter(m => m.term === trimmedTerm).length === 0) {
      const normalizedTerm = removeDiacritics(trimmedTerm)
      const normalizedText = removeDiacritics(text)
      let searchIndex = 0

      while (searchIndex < normalizedText.length) {
        const index = normalizedText.indexOf(normalizedTerm, searchIndex)
        if (index === -1) break

        allMatches.push({
          start: index,
          end: index + normalizedTerm.length,
          term: trimmedTerm
        })

        searchIndex = index + 1
      }
    }
  })

  // Sort matches by start position and merge overlapping ones
  allMatches.sort((a, b) => a.start - b.start)

  // Merge overlapping matches
  const mergedMatches: Array<{ start: number; end: number }> = []
  for (const match of allMatches) {
    if (mergedMatches.length === 0) {
      mergedMatches.push({ start: match.start, end: match.end })
    } else {
      const lastMatch = mergedMatches[mergedMatches.length - 1]
      if (match.start <= lastMatch.end) {
        // Overlapping or adjacent, merge them
        lastMatch.end = Math.max(lastMatch.end, match.end)
      } else {
        mergedMatches.push({ start: match.start, end: match.end })
      }
    }
  }

  // Apply highlighting from end to start to avoid index shifting
  let result = text
  for (let i = mergedMatches.length - 1; i >= 0; i--) {
    const match = mergedMatches[i]
    const before = result.substring(0, match.start)
    const matchedText = result.substring(match.start, match.end)
    const after = result.substring(match.end)
    const highlighted = `<mark class="bg-yellow-200 text-yellow-900 px-1 rounded">${matchedText}</mark>`
    result = before + highlighted + after
  }

  return result
}

// React component helper for highlighted text
export const createHighlightedText = (text: string, searchTerm: string) => {
  const highlightedHtml = highlightText(text, searchTerm)
  return {
    __html: highlightedHtml
  }
}

// Enhanced function to check if text contains any of the search terms
export const containsSearchTerm = (text: string, searchInput: string): boolean => {
  if (!searchInput || !text) return true

  // Parse multiple search terms
  const searchTerms = parseSearchTerms(searchInput)
  if (searchTerms.length === 0) return true

  const normalizedText = removeDiacritics(text)

  // Check if ANY search term matches (OR logic)
  return searchTerms.some(term => {
    const normalizedTerm = removeDiacritics(term)
    return normalizedText.includes(normalizedTerm)
  })
}
