/**
 * Timezone utilities for Vietnam timezone (GMT+7)
 * Provides consistent date/time formatting and conversion functions
 */

// Vietnam timezone constant
export const VIETNAM_TIMEZONE = 'Asia/Ho_Chi_Minh'

/**
 * Convert UTC date string to Vietnam timezone Date object
 * @param utcString - UTC date string from database (YYYY-MM-DD HH:MM:SS or ISO format)
 * @returns Date object in Vietnam timezone
 */
export function createDateFromUTC(utcString: string): Date {
  if (!utcString) return new Date()
  
  // SQLite datetime('now') returns UTC string format: YYYY-MM-DD HH:MM:SS
  // Add 'Z' to ensure JavaScript parses it as UTC
  const isoString = utcString.includes('T') ? utcString : utcString.replace(' ', 'T') + 'Z'
  return new Date(isoString)
}

/**
 * Get current date in Vietnam timezone as YYYY-MM-DD format
 * @returns Date string in YYYY-MM-DD format
 */
export function getCurrentDateVN(): string {
  const now = new Date()
  return now.toLocaleDateString('sv-SE', { timeZone: VIETNAM_TIMEZONE })
}

/**
 * Get current datetime in Vietnam timezone as ISO string
 * @returns ISO string in Vietnam timezone
 */
export function getCurrentDateTimeVN(): string {
  const now = new Date()
  return now.toLocaleString('sv-SE', { 
    timeZone: VIETNAM_TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(' ', 'T')
}

/**
 * Format date to Vietnam timezone display
 * @param date - Date object or UTC string
 * @param options - Formatting options
 * @returns Formatted date string
 */
export function formatDateVN(
  date: Date | string, 
  options: {
    includeTime?: boolean
    includeSeconds?: boolean
    shortFormat?: boolean
  } = {}
): string {
  const { includeTime = true, includeSeconds = false, shortFormat = false } = options
  
  const dateObj = typeof date === 'string' ? createDateFromUTC(date) : date
  
  if (!dateObj || isNaN(dateObj.getTime())) {
    return '-'
  }

  const formatOptions: Intl.DateTimeFormatOptions = {
    timeZone: VIETNAM_TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }

  if (includeTime) {
    formatOptions.hour = '2-digit'
    formatOptions.minute = '2-digit'
    if (includeSeconds) {
      formatOptions.second = '2-digit'
    }
  }

  if (shortFormat) {
    return dateObj.toLocaleString('vi-VN', formatOptions)
  }

  return dateObj.toLocaleString('vi-VN', formatOptions)
}

/**
 * Format time only in Vietnam timezone
 * @param date - Date object or UTC string
 * @param includeSeconds - Whether to include seconds
 * @returns Time string in HH:MM or HH:MM:SS format
 */
export function formatTimeVN(date: Date | string, includeSeconds = false): string {
  const dateObj = typeof date === 'string' ? createDateFromUTC(date) : date
  
  if (!dateObj || isNaN(dateObj.getTime())) {
    return '-'
  }

  const formatOptions: Intl.DateTimeFormatOptions = {
    timeZone: VIETNAM_TIMEZONE,
    hour: '2-digit',
    minute: '2-digit'
  }

  if (includeSeconds) {
    formatOptions.second = '2-digit'
  }

  return dateObj.toLocaleTimeString('vi-VN', formatOptions)
}

/**
 * Check if two dates are the same day in Vietnam timezone
 * @param date1 - First date
 * @param date2 - Second date
 * @returns True if same day in Vietnam timezone
 */
export function isSameDayVN(date1: Date | string, date2: Date | string): boolean {
  const d1 = typeof date1 === 'string' ? createDateFromUTC(date1) : date1
  const d2 = typeof date2 === 'string' ? createDateFromUTC(date2) : date2
  
  if (!d1 || !d2 || isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return false
  }

  const date1VN = new Date(d1.toLocaleString("en-US", { timeZone: VIETNAM_TIMEZONE }))
  const date2VN = new Date(d2.toLocaleString("en-US", { timeZone: VIETNAM_TIMEZONE }))

  return date1VN.toDateString() === date2VN.toDateString()
}

/**
 * Convert date to Vietnam timezone for comparison
 * @param date - Date object or UTC string
 * @returns Date object adjusted to Vietnam timezone
 */
export function toVietnamTimezone(date: Date | string): Date {
  const dateObj = typeof date === 'string' ? createDateFromUTC(date) : date
  
  if (!dateObj || isNaN(dateObj.getTime())) {
    return new Date()
  }

  return new Date(dateObj.toLocaleString("en-US", { timeZone: VIETNAM_TIMEZONE }))
}

/**
 * Get relative time string in Vietnamese
 * @param date - Date object or UTC string
 * @returns Relative time string (e.g., "2 phút trước", "1 giờ trước")
 */
export function getRelativeTimeVN(date: Date | string): string {
  const dateObj = typeof date === 'string' ? createDateFromUTC(date) : date
  
  if (!dateObj || isNaN(dateObj.getTime())) {
    return '-'
  }

  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMinutes < 1) {
    return 'Vừa xong'
  } else if (diffMinutes < 60) {
    return `${diffMinutes} phút trước`
  } else if (diffHours < 24) {
    return `${diffHours} giờ trước`
  } else if (diffDays < 7) {
    return `${diffDays} ngày trước`
  } else {
    return formatDateVN(dateObj, { includeTime: false })
  }
}

/**
 * Parse flight time with overnight support (e.g., "02:30+")
 * @param timeString - Time string in HH:MM or HH:MM+ format
 * @returns Object with time info and overnight flag
 */
export function parseFlightTime(timeString: string): {
  time: string
  isOvernight: boolean
  displayTime: string
} {
  if (!timeString) {
    return { time: '', isOvernight: false, displayTime: '-' }
  }

  const isOvernight = timeString.endsWith('+')
  const time = isOvernight ? timeString.slice(0, -1) : timeString
  const displayTime = timeString // Keep original format for display

  return { time, isOvernight, displayTime }
}

/**
 * Format flight time for display with timezone consideration
 * @param timeString - Time string in HH:MM or HH:MM+ format
 * @param date - Date for the flight (YYYY-MM-DD)
 * @returns Formatted time string with timezone info
 */
export function formatFlightTimeVN(timeString: string, date?: string): string {
  if (!timeString) return '-'

  const { displayTime } = parseFlightTime(timeString)

  return displayTime
}
