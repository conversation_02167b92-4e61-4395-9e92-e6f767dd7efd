import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { WalkieTalkieManager } from "@/components/WalkieTalkieManager";
import { AccessCardManager } from "@/components/AccessCardManager";
import { ActivityHistory } from "@/components/ActivityHistory";
import { Radio, CreditCard, Users, Clock, Loader2, LogOut, User, Plane } from "lucide-react";
import { useWalkieTalkies, useAccessCards } from "@/hooks/api";
import { useAuth } from "@/contexts/AuthContext";
import { Link } from "@tanstack/react-router";

const Index = () => {
  const { user, logout } = useAuth();

  // Fetch data chỉ để tính statistics
  const { data: walkieTalkies = [], isLoading: isLoadingWalkieTalkies, error: walkieTalkiesError } = useWalkieTalkies();
  const { data: accessCards = [], isLoading: isLoadingAccessCards, error: accessCardsError } = useAccessCards();

  const isLoading = isLoadingWalkieTalkies || isLoadingAccessCards;
  const hasError = walkieTalkiesError || accessCardsError;

  // Calculate statistics
  const walkieTalkiesTotal = walkieTalkies.length;
  const walkieTalkiesAvailable = walkieTalkies.filter(item => item.status === "available").length;
  const walkieTalkiesAssigned = walkieTalkies.filter(item => item.status === "assigned").length;

  const accessCardsTotal = accessCards.length;
  const accessCardsAvailable = accessCards.filter(item => item.status === "active" && !item.assigned_to).length;
  const accessCardsAssigned = accessCards.filter(item => item.assigned_to).length;

  const totalItems = walkieTalkiesTotal + accessCardsTotal;
  const assignedItems = walkieTalkiesAssigned + accessCardsAssigned;
  const availableItems = walkieTalkiesAvailable + accessCardsAvailable;

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
            <h2 className="text-red-800 font-semibold mb-2">Lỗi tải dữ liệu</h2>
            <p className="text-red-600 text-sm">
              {walkieTalkiesError?.message || accessCardsError?.message || 'Không thể kết nối đến server'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Thử lại
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-4 sm:py-0 sm:h-16 space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <Radio className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg sm:text-xl font-bold text-gray-900">Quản lý TTB - HDCX</h1>
                <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">Hệ thống quản lý bộ đàm và thẻ từ</p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
              <Link to="/flight-schedule" className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors">
                <Plane className="h-4 w-4" />
                <span>Lịch bay</span>
              </Link>
              <div className="flex items-center space-x-2 text-xs sm:text-sm text-gray-600">
                <User className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="truncate max-w-32 sm:max-w-none">Xin chào, {user?.full_name || user?.username}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={logout}
                className="flex items-center space-x-2 w-full sm:w-auto justify-center"
              >
                <LogOut className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="text-xs sm:text-sm">Đăng xuất</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Bộ đàm</CardTitle>
              <div className="bg-blue-100 p-2 rounded-full">
                <Radio className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{walkieTalkiesTotal}</div>
              <p className="text-xs text-gray-500">
                {walkieTalkiesAvailable} có sẵn • {walkieTalkiesAssigned} đang mượn
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Thẻ từ</CardTitle>
              <div className="bg-purple-100 p-2 rounded-full">
                <CreditCard className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{accessCardsTotal}</div>
              <p className="text-xs text-gray-500">
                {accessCardsAvailable} có sẵn • {accessCardsAssigned} đang mượn
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Đang mượn</CardTitle>
              <div className="bg-orange-100 p-2 rounded-full">
                <Clock className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{assignedItems}</div>
              <p className="text-xs text-gray-500">Thiết bị đang được sử dụng</p>
            </CardContent>
          </Card>

          <Card className="bg-white hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Có sẵn</CardTitle>
              <div className="bg-green-100 p-2 rounded-full">
                <Users className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{availableItems}</div>
              <p className="text-xs text-gray-500">Sẵn sàng cho mượn</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="walkie-talkies" className="space-y-6">
          <TabsList className="bg-white p-1 rounded-lg shadow-sm">
            <TabsTrigger value="walkie-talkies" className="flex items-center space-x-2">
              <Radio className="h-4 w-4" />
              <span>Bộ đàm</span>
            </TabsTrigger>
            <TabsTrigger value="access-cards" className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4" />
              <span>thẻ từ</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>Lịch sử</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="walkie-talkies">
            <WalkieTalkieManager />
          </TabsContent>

          <TabsContent value="access-cards">
            <AccessCardManager />
          </TabsContent>

          <TabsContent value="history">
            <ActivityHistory />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Index;
