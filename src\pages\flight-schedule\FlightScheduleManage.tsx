import { useState, useMemo } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Plane
} from "lucide-react";
import { Link } from "@tanstack/react-router";
import FlightTable from "@/components/flight-schedule/FlightTable";
import { FlightScheduleToolbar } from "@/components/flight-schedule/FlightScheduleToolbar";
import { FindReplaceModal } from "@/components/flight-schedule/FindReplaceModal";
import { useFlights } from "@/hooks/useFlights";
import { useFlightMutations } from "@/hooks/useFlightMutations";
import { useFindReplace } from "@/hooks/useFindReplace";
import { searchFlightData } from "@/lib/search-utils";
import { useIsMobile } from "@/hooks/use-mobile";



const FlightScheduleManage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  // Sử dụng ngày hiện tại thay vì ngày cũ
  const [selectedDate, setSelectedDate] = useState(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
  });
  const [isFindReplaceOpen, setIsFindReplaceOpen] = useState(false);
  const { isMobile } = useIsMobile();

  // Sử dụng hook useFlights để lấy dữ liệu thật từ database
  const { data: flightResponse, isLoading, error } = useFlights({
    date: selectedDate,
    limit: 2000 // Increase limit to show all flights (up to 2000)
  });

  // Sử dụng mutation để update status
  const { updateFlightStatus, isUpdatingStatus } = useFlightMutations();
  const { performFindReplace, performSwap, isLoading: isFindReplaceLoading } = useFindReplace();

  const flightData = flightResponse?.data || [];

  // Debug logging
  console.log('FlightScheduleManage - selectedDate:', selectedDate);
  console.log('FlightScheduleManage - searchTerm:', searchTerm);
  console.log('FlightScheduleManage - isLoading:', isLoading);
  console.log('FlightScheduleManage - error:', error);
  console.log('FlightScheduleManage - flightResponse:', flightResponse);
  console.log('FlightScheduleManage - flightData length:', flightData.length);

  // Client-side fuzzy search filtering
  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) return flightData;
    return flightData.filter(flight => searchFlightData(searchTerm, flight));
  }, [flightData, searchTerm]);

  const handleStatusUpdate = (flightId: string, field: string, value: boolean) => {
    // Validate field type
    const validFields = ['arr_present', 'arr_finished', 'dep_present', 'dep_boarded', 'dep_finished'];
    if (!validFields.includes(field)) {
      console.error('Invalid status field:', field);
      return;
    }

    // Call mutation to update status
    updateFlightStatus.mutate({
      flightId,
      field: field as 'arr_present' | 'arr_finished' | 'dep_present' | 'dep_boarded' | 'dep_finished',
      value
    });
  };

  const handleExport = () => {
    console.log('Export functionality');
    // TODO: Implement export logic
  };

  const handleAddFlight = () => {
    console.log('Add flight functionality');
    // TODO: Implement add flight logic
  };

  const handleFilter = () => {
    console.log('Filter functionality');
    // TODO: Implement filter logic
  };

  const handleFindReplace = () => {
    setIsFindReplaceOpen(true);
  };

  const handleFindReplaceClose = () => {
    setIsFindReplaceOpen(false);
  };

  const handleFindReplaceSubmit = async (matches: any[], replaceValue: string) => {
    await performFindReplace(matches, replaceValue);
  };

  const handleSwapSubmit = async (valueA: string, valueB: string, scope: 'staff' | 'reg' | 'both') => {
    await performSwap(valueA, valueB, scope, filteredData);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="w-full mx-auto px-2 sm:px-4 lg:px-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-4 sm:py-0 sm:h-16 space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <Plane className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg sm:text-xl font-bold text-gray-900">Lịch bay - HDCX</h1>
                <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">Quản lý và theo dõi lịch bay</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Link to="/">
                <Button variant="outline" size="sm">
                  Trang chủ
                </Button>
              </Link>
              <Link to="/flight-schedule/public">
                <Button variant="outline" size="sm">
                  Xem công khai
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className={`w-full mx-auto py-6 ${isMobile ? 'px-2' : 'px-2 sm:px-4 lg:px-6'}`}>
        {/* Consolidated Toolbar */}
        <FlightScheduleToolbar
          searchTerm={searchTerm}
          selectedDate={selectedDate}
          onSearchChange={setSearchTerm}
          onDateChange={setSelectedDate}
          onExport={handleExport}
          onAddFlight={handleAddFlight}
          onFilter={handleFilter}
          onFindReplace={handleFindReplace}
          className="mb-6"
        />

        {/* Flight Schedule Content */}
        {isMobile ? (
          // Mobile: Direct list without card wrapper
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Lịch bay {selectedDate}
              </h2>
              <Badge variant="outline" className="text-xs">
                {filteredData.length} chuyến
              </Badge>
            </div>

            {/* Content */}
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-gray-500">Đang tải dữ liệu...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-red-500">Lỗi: {error.message}</div>
              </div>
            ) : (
              <FlightTable
                data={filteredData as any}
                onStatusUpdate={handleStatusUpdate}
                showStatusControls={true}
                searchTerm={searchTerm}
              />
            )}
          </div>
        ) : (
          // Desktop: Card wrapper
          <Card className="bg-white">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Lịch bay ngày {selectedDate}</span>
                <Badge variant="outline">{filteredData.length} chuyến bay</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">Đang tải dữ liệu...</div>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-red-500">Lỗi: {error.message}</div>
                </div>
              ) : (
                <FlightTable
                  data={filteredData as any}
                  onStatusUpdate={handleStatusUpdate}
                  showStatusControls={true}
                  searchTerm={searchTerm}
                  date={selectedDate}
                />
              )}
            </CardContent>
          </Card>
        )}

        {/* Find & Replace Modal */}
        <FindReplaceModal
          isOpen={isFindReplaceOpen}
          onClose={handleFindReplaceClose}
          flights={filteredData}
          onReplace={handleFindReplaceSubmit}
          onSwap={handleSwapSubmit}
        />
      </div>
    </div>
  );
};

export default FlightScheduleManage;
