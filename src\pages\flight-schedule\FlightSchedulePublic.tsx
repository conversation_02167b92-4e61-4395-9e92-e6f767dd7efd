import { useState, useMemo, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Clock,
  RefreshCw,
  Eye
} from "lucide-react";

import FlightTable from "@/components/flight-schedule/FlightTable";
import { SearchFilter } from "@/components/flight-schedule/SearchFilter";
import { usePublicFlights } from "@/hooks/usePublicFlights";
import { searchFlightData } from "@/lib/search-utils";
import { getCurrentDateVN, formatDateVN } from "@/lib/timezone-utils";
import { useIsMobile } from "@/hooks/use-mobile";



const FlightSchedulePublic = () => {
  const [searchTerm, setSearchTerm] = useState("");
  // Sử dụng ngày hiện tại với Vietnam timezone
  const [selectedDate, setSelectedDate] = useState(() => {
    return getCurrentDateVN(); // Format: YYYY-MM-DD in Vietnam timezone
  });
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const { isMobile } = useIsMobile();

  // Sử dụng hook usePublicFlights để lấy dữ liệu từ public API (không cần authentication)
  const { data: flightResponse, isLoading, error, refetch } = usePublicFlights({
    date: selectedDate,
    limit: 2000 // Increase limit to show all flights (up to 2000)
  });

  const flightData = flightResponse?.data || [];

  // Debug logging
  console.log('FlightSchedulePublic - selectedDate:', selectedDate);
  console.log('FlightSchedulePublic - searchTerm:', searchTerm);
  console.log('FlightSchedulePublic - isLoading:', isLoading);
  console.log('FlightSchedulePublic - error:', error);
  console.log('FlightSchedulePublic - flightResponse:', flightResponse);
  console.log('FlightSchedulePublic - flightData length:', flightData.length);

  // Auto refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdated(new Date());
      refetch(); // Refresh data from API
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [refetch]);

  // Client-side fuzzy search filtering
  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) return flightData;
    return flightData.filter(flight => searchFlightData(searchTerm, flight));
  }, [flightData, searchTerm]);

  const handleRefresh = () => {
    setLastUpdated(new Date());
    refetch(); // Refresh data from API
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="w-full mx-auto px-2 sm:px-4 lg:px-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-4 sm:py-0 sm:h-16 space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="bg-green-600 p-2 rounded-lg">
                <Eye className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg sm:text-xl font-bold text-gray-900">Lịch bay công khai</h1>
                <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">Xem lịch bay dành cho nhân viên</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Clock className="h-4 w-4" />
              <span>Cập nhật: {formatDateVN(lastUpdated, { includeTime: true, includeSeconds: false })}</span>
              <button
                onClick={handleRefresh}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <RefreshCw className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className={`w-full mx-auto py-6 ${isMobile ? 'px-2' : 'px-2 sm:px-4 lg:px-6'}`}>
        {/* Search Filter */}
        <SearchFilter
          searchTerm={searchTerm}
          selectedDate={selectedDate}
          onSearchChange={setSearchTerm}
          onDateChange={setSelectedDate}
          showRefresh={true}
          onRefresh={handleRefresh}
          lastUpdated={lastUpdated}
          className="mb-6"
        />

        {/* Flight Schedule Content */}
        {isMobile ? (
          // Mobile: Direct list without card wrapper
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Lịch bay {formatDateVN(selectedDate, { includeTime: false })}
              </h2>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {filteredData.length} chuyến
                </Badge>
                <Badge variant="secondary" className="bg-green-100 text-green-600 text-xs">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                  LIVE
                </Badge>
              </div>
            </div>

            {/* Content */}
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-gray-500">Đang tải dữ liệu...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-red-500">Lỗi: {error.message}</div>
              </div>
            ) : (
              <FlightTable
                data={filteredData as any}
                showStatusControls={false}
                isPublicView={true}
                searchTerm={searchTerm}
                date={selectedDate}
              />
            )}
          </div>
        ) : (
          // Desktop: Card wrapper
          <Card className="bg-white">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Lịch bay ngày {formatDateVN(selectedDate, { includeTime: false })}</span>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">{filteredData.length} chuyến bay</Badge>
                  <Badge variant="secondary" className="bg-green-100 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                    LIVE
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">Đang tải dữ liệu...</div>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-red-500">Lỗi: {error.message}</div>
                </div>
              ) : (
                <FlightTable
                  data={filteredData as any}
                  showStatusControls={false}
                  isPublicView={true}
                  searchTerm={searchTerm}
                  date={selectedDate}
                />
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default FlightSchedulePublic;
