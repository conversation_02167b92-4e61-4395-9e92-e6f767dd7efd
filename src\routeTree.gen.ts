/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as R404RouteImport } from './routes/_404'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated.index'
import { Route as FlightSchedulePublicRouteImport } from './routes/flight-schedule.public'
import { Route as AuthenticatedFlightScheduleRouteImport } from './routes/_authenticated.flight-schedule'
import { Route as AuthenticatedFlightScheduleIndexRouteImport } from './routes/_authenticated.flight-schedule.index'
import { Route as AuthenticatedFlightScheduleImportRouteImport } from './routes/_authenticated.flight-schedule.import'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const R404Route = R404RouteImport.update({
  id: '/_404',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const FlightSchedulePublicRoute = FlightSchedulePublicRouteImport.update({
  id: '/flight-schedule/public',
  path: '/flight-schedule/public',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedFlightScheduleRoute =
  AuthenticatedFlightScheduleRouteImport.update({
    id: '/flight-schedule',
    path: '/flight-schedule',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedFlightScheduleIndexRoute =
  AuthenticatedFlightScheduleIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedFlightScheduleRoute,
  } as any)
const AuthenticatedFlightScheduleImportRoute =
  AuthenticatedFlightScheduleImportRouteImport.update({
    id: '/import',
    path: '/import',
    getParentRoute: () => AuthenticatedFlightScheduleRoute,
  } as any)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/flight-schedule': typeof AuthenticatedFlightScheduleRouteWithChildren
  '/flight-schedule/public': typeof FlightSchedulePublicRoute
  '/': typeof AuthenticatedIndexRoute
  '/flight-schedule/import': typeof AuthenticatedFlightScheduleImportRoute
  '/flight-schedule/': typeof AuthenticatedFlightScheduleIndexRoute
}
export interface FileRoutesByTo {
  '': typeof R404Route
  '/login': typeof LoginRoute
  '/flight-schedule/public': typeof FlightSchedulePublicRoute
  '/': typeof AuthenticatedIndexRoute
  '/flight-schedule/import': typeof AuthenticatedFlightScheduleImportRoute
  '/flight-schedule': typeof AuthenticatedFlightScheduleIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_404': typeof R404Route
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/flight-schedule': typeof AuthenticatedFlightScheduleRouteWithChildren
  '/flight-schedule/public': typeof FlightSchedulePublicRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/flight-schedule/import': typeof AuthenticatedFlightScheduleImportRoute
  '/_authenticated/flight-schedule/': typeof AuthenticatedFlightScheduleIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/login'
    | '/flight-schedule'
    | '/flight-schedule/public'
    | '/'
    | '/flight-schedule/import'
    | '/flight-schedule/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | ''
    | '/login'
    | '/flight-schedule/public'
    | '/'
    | '/flight-schedule/import'
    | '/flight-schedule'
  id:
    | '__root__'
    | '/_404'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/flight-schedule'
    | '/flight-schedule/public'
    | '/_authenticated/'
    | '/_authenticated/flight-schedule/import'
    | '/_authenticated/flight-schedule/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  R404Route: typeof R404Route
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
  FlightSchedulePublicRoute: typeof FlightSchedulePublicRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_404': {
      id: '/_404'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof R404RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/flight-schedule/public': {
      id: '/flight-schedule/public'
      path: '/flight-schedule/public'
      fullPath: '/flight-schedule/public'
      preLoaderRoute: typeof FlightSchedulePublicRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/flight-schedule': {
      id: '/_authenticated/flight-schedule'
      path: '/flight-schedule'
      fullPath: '/flight-schedule'
      preLoaderRoute: typeof AuthenticatedFlightScheduleRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/flight-schedule/': {
      id: '/_authenticated/flight-schedule/'
      path: '/'
      fullPath: '/flight-schedule/'
      preLoaderRoute: typeof AuthenticatedFlightScheduleIndexRouteImport
      parentRoute: typeof AuthenticatedFlightScheduleRoute
    }
    '/_authenticated/flight-schedule/import': {
      id: '/_authenticated/flight-schedule/import'
      path: '/import'
      fullPath: '/flight-schedule/import'
      preLoaderRoute: typeof AuthenticatedFlightScheduleImportRouteImport
      parentRoute: typeof AuthenticatedFlightScheduleRoute
    }
  }
}

interface AuthenticatedFlightScheduleRouteChildren {
  AuthenticatedFlightScheduleImportRoute: typeof AuthenticatedFlightScheduleImportRoute
  AuthenticatedFlightScheduleIndexRoute: typeof AuthenticatedFlightScheduleIndexRoute
}

const AuthenticatedFlightScheduleRouteChildren: AuthenticatedFlightScheduleRouteChildren =
  {
    AuthenticatedFlightScheduleImportRoute:
      AuthenticatedFlightScheduleImportRoute,
    AuthenticatedFlightScheduleIndexRoute:
      AuthenticatedFlightScheduleIndexRoute,
  }

const AuthenticatedFlightScheduleRouteWithChildren =
  AuthenticatedFlightScheduleRoute._addFileChildren(
    AuthenticatedFlightScheduleRouteChildren,
  )

interface AuthenticatedRouteChildren {
  AuthenticatedFlightScheduleRoute: typeof AuthenticatedFlightScheduleRouteWithChildren
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedFlightScheduleRoute:
    AuthenticatedFlightScheduleRouteWithChildren,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  R404Route: R404Route,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
  FlightSchedulePublicRoute: FlightSchedulePublicRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
