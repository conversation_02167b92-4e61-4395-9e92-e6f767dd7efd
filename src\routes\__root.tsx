import { createRootRoute, Outlet } from '@tanstack/react-router'
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "@/contexts/AuthContext";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Giảm auto refresh
      refetchOnWindowFocus: false, // Không refetch khi focus window
      refetchOnMount: true, // Vẫn refetch khi mount component (cần thiết)
      refetchOnReconnect: true, // Refetch khi reconnect (cần thiết)
      staleTime: 5 * 60 * 1000, // 5 phút - data được coi là fresh trong 5 phút
      gcTime: 10 * 60 * 1000, // 10 phút - cache time

      // Enhanced retry strategy
      retry: (failureCount, error) => {
        // Don't retry for client errors (4xx)
        if (error instanceof Error) {
          const message = error.message.toLowerCase()

          if (message.includes('400') || message.includes('401') ||
              message.includes('403') || message.includes('404') ||
              message.includes('422') || message.includes('429')) {
            return false
          }

          if (message.includes('validation') || message.includes('invalid')) {
            return false
          }
        }

        // Retry up to 2 times for network/server errors
        return failureCount < 2
      },

      // Exponential backoff retry delay
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000), // Max 10s delay
    },
    mutations: {
      // Enhanced retry for mutations
      retry: (failureCount, error) => {
        // Don't retry mutations for client errors
        if (error instanceof Error) {
          const message = error.message.toLowerCase()

          if (message.includes('400') || message.includes('401') ||
              message.includes('403') || message.includes('404') ||
              message.includes('422') || message.includes('429')) {
            return false
          }
        }

        // Retry once for network/server errors
        return failureCount < 1
      },

      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000), // Max 5s delay for mutations
    },
  },
});

export const Route = createRootRoute({
  component: () => {
    return (
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <Outlet />
          </TooltipProvider>
        </AuthProvider>
      </QueryClientProvider>
    )
  }
})
