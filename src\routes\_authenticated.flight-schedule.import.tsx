import { createFileRoute } from '@tanstack/react-router'
import { ImportExcel } from '@/components/flight-schedule/ImportExcel'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Plane } from 'lucide-react'
import { Link } from '@tanstack/react-router'

export const Route = createFileRoute('/_authenticated/flight-schedule/import')({
  component: FlightScheduleImportPage,
})

function FlightScheduleImportPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link to="/flight-schedule">
                <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                  <ArrowLeft className="h-4 w-4" />
                  <span>Quay lại</span>
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <Plane className="h-5 w-5 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-900">Import lịch bay</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link to="/flight-schedule">
                <Button variant="outline" size="sm">
                  Quản lý lịch bay
                </Button>
              </Link>
              <Link to="/flight-schedule/public">
                <Button variant="outline" size="sm">
                  Xem công khai
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ImportExcel 
          onImportSuccess={() => {
            // Navigate back to manage page after successful import
            window.location.href = '/flight-schedule'
          }}
        />
      </div>
    </div>
  )
}
