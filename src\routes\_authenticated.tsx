import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2 } from 'lucide-react'

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: async ({ location }) => {
    // Check if user is authenticated
    const accessToken = localStorage.getItem('accessToken')
    if (!accessToken) {
      throw redirect({
        to: '/login',
        search: {
          // Use the current location to power a redirect after login
          redirect: location.href,
        },
      })
    }
  },
  component: AuthenticatedLayout,
})

function AuthenticatedLayout() {
  const { isLoading, isAuthenticated } = useAuth()

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-sm text-muted-foreground"><PERSON><PERSON> kiểm tra đăng nhập...</p>
        </div>
      </div>
    )
  }

  // If not authenticated after loading, this shouldn't happen due to beforeLoad
  // but it's a good safety check
  if (!isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Không có quyền truy cập</h1>
          <p className="text-muted-foreground">Vui lòng đăng nhập để tiếp tục.</p>
        </div>
      </div>
    )
  }

  // Render the authenticated content
  return <Outlet />
}
