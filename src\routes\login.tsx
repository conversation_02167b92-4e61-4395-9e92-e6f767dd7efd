import { createFileRoute, redirect } from '@tanstack/react-router'
import { LoginForm } from '@/components/auth/LoginForm'
import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'
import { toast } from 'sonner'

export const Route = createFileRoute('/login')({
  beforeLoad: ({ context }) => {
    // Redirect to home if already authenticated
    const isAuthenticated = localStorage.getItem('accessToken')
    if (isAuthenticated) {
      throw redirect({
        to: '/',
      })
    }
  },
  component: LoginPage,
})

function LoginPage() {
  const { login } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleLogin = async (data: { email: string; password: string }) => {
    setIsLoading(true)
    setError(null)

    try {
      await login(data.email, data.password)
      toast.success('Đăng nhập thành công!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Đăng nhập thất bại'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <LoginForm
      onSubmit={handleLogin}
      isLoading={isLoading}
      error={error}
    />
  )
}
