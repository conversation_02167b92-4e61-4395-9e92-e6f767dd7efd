/* Mobile optimizations for Flight Schedule */

/* Mobile-specific card animations */
@media (max-width: 767px) {
  .flight-card-enter {
    opacity: 0;
    transform: translateY(20px);
  }
  
  .flight-card-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  }
  
  .flight-card-exit {
    opacity: 1;
    transform: translateY(0);
  }
  
  .flight-card-exit-active {
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  }

  /* Mobile touch optimizations */
  .mobile-flight-card {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .mobile-flight-card:active {
    transform: scale(0.98);
    transition: transform 150ms ease-in-out;
  }

  /* Mobile button optimizations */
  .mobile-status-button {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* Mobile input optimizations */
  .mobile-editable-input {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 44px;
  }

  /* Mobile search optimizations */
  .mobile-search-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Mobile toolbar optimizations */
  .mobile-toolbar-button {
    min-height: 44px;
    touch-action: manipulation;
  }

  /* Mobile card content spacing */
  .mobile-flight-content {
    padding: 12px;
  }

  .mobile-flight-section {
    padding: 8px 12px;
    margin-bottom: 8px;
  }

  /* Mobile badge optimizations */
  .mobile-badge {
    font-size: 10px;
    padding: 2px 6px;
  }

  /* Mobile status icon optimizations */
  .mobile-status-icon {
    width: 16px;
    height: 16px;
  }

  /* Mobile header optimizations */
  .mobile-header {
    padding: 12px 16px;
  }

  .mobile-header-title {
    font-size: 18px;
    line-height: 1.2;
  }

  /* Mobile loading states */
  .mobile-loading-spinner {
    width: 24px;
    height: 24px;
  }

  /* Mobile error states */
  .mobile-error-message {
    font-size: 14px;
    padding: 16px;
    text-align: center;
  }

  /* Mobile empty states */
  .mobile-empty-state {
    padding: 32px 16px;
    text-align: center;
  }

  .mobile-empty-state-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
  }

  /* Mobile scroll optimizations */
  .mobile-scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Mobile safe area optimizations */
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Mobile keyboard optimizations */
  .mobile-keyboard-adjust {
    padding-bottom: 0;
  }

  /* Mobile focus optimizations */
  .mobile-focus-visible:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  /* Mobile hover state removal */
  .mobile-no-hover:hover {
    background-color: initial;
    transform: none;
  }

  /* Mobile text selection optimizations */
  .mobile-no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Mobile swipe gesture preparations */
  .mobile-swipeable {
    touch-action: pan-x;
  }

  /* Mobile pull-to-refresh preparations */
  .mobile-pull-refresh {
    overscroll-behavior-y: contain;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode mobile optimizations */
@media (prefers-color-scheme: dark) and (max-width: 767px) {
  .mobile-flight-card {
    background-color: #1f2937;
    border-color: #374151;
  }

  .mobile-flight-section {
    background-color: #111827;
  }

  .mobile-header {
    background-color: #1f2937;
    border-color: #374151;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .flight-card-enter-active,
  .flight-card-exit-active,
  .mobile-flight-card:active {
    transition: none;
    transform: none;
  }
}

/* High contrast mode optimizations */
@media (prefers-contrast: high) {
  .mobile-flight-card {
    border-width: 2px;
  }

  .mobile-status-button {
    border-width: 2px;
  }
}

/* Print optimizations for mobile */
@media print and (max-width: 767px) {
  .mobile-flight-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .mobile-toolbar-button,
  .mobile-status-button {
    display: none;
  }
}
