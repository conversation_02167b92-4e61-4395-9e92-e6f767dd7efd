import React from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

// Test component để kiểm tra fix React hooks error #300
export const TestMobileFix: React.FC = () => {
  const { isMobile, isInitialized } = useIsMobile();

  console.log('TestMobileFix - isMobile:', isMobile, 'isInitialized:', isInitialized);

  if (!isInitialized) {
    return <div>Đang khởi tạo...</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Test Mobile Fix</h2>
      <div className="space-y-2">
        <p>Is Mobile: {isMobile ? 'Yes' : 'No'}</p>
        <p>Is Initialized: {isInitialized ? 'Yes' : 'No'}</p>
        <p>Window Width: {typeof window !== 'undefined' ? window.innerWidth : 'N/A'}px</p>
        <p>Breakpoint: {isMobile ? '< 768px' : '>= 768px'}</p>
      </div>
      
      {/* Test conditional rendering */}
      {isMobile ? (
        <div className="mt-4 p-4 bg-blue-100 rounded">
          <h3 className="font-semibold">Mobile View</h3>
          <p>This should render on mobile devices without React hooks error.</p>
        </div>
      ) : (
        <div className="mt-4 p-4 bg-green-100 rounded">
          <h3 className="font-semibold">Desktop View</h3>
          <p>This should render on desktop devices without React hooks error.</p>
        </div>
      )}
    </div>
  );
};

export default TestMobileFix;
