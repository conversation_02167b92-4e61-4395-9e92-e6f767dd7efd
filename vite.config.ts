import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { TanStackRouterVite } from '@tanstack/router-vite-plugin';
import tailwindcss from "@tailwindcss/vite";
import { cloudflare } from "@cloudflare/vite-plugin";

// https://vitejs.dev/config/
export default defineConfig(() => ({
  server: {
    host: "::",
    port: 8080,
    headers: {
      'Cache-Control': 'no-cache'
    },
    watch: {
      ignored: [
        "**/.wrangler/**",
        "**/node_modules/**",
        "**/.git/**"
      ]
    }
  },
  plugins: [
    cloudflare(),
    tailwindcss(),
    TanStackRouterVite(),
    react(),
  ],
  build: {
    outDir: 'dist/client',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-accordion', '@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          tanstack: ['@tanstack/react-query', '@tanstack/react-router', '@tanstack/react-table', '@tanstack/react-form']
        }
      }
    }
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
