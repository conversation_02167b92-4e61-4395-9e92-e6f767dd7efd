{
  "name": "quan-ly-ttb",
  "main": "src/api/index.ts",
  "compatibility_date": "2024-12-01",
  "compatibility_flags": ["nodejs_compat"],

  // Static assets configuration for SPA
  "assets": {
    "directory": "./dist/client/client",
    "binding": "ASSETS",
    "not_found_handling": "single-page-application"
  },

  // D1 database binding
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "quan-ly-ttb-db",
      "database_id": "699380aa-b84d-4a31-b08e-223d1ac77240"
    }
  ],

  // Environment variables
  "vars": {
    "NODE_ENV": "production"
  }
}
