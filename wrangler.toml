name = "quan-ly-ttb"
main = "src/api/index.ts"
compatibility_date = "2024-12-01"
compatibility_flags = ["nodejs_compat"]

# D1 database binding
[[d1_databases]]
binding = "DB"
database_name = "quan-ly-ttb-db"
database_id = "699380aa-b84d-4a31-b08e-223d1ac77240"

# Environment variables cho local development
[vars]
NODE_ENV = "development"

# Assets configuration (nếu cần serve static files)
# assets = { directory = "public" }
